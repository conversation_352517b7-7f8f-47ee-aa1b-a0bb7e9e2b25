<?php
/**
 * Test page for BookInn Gantt Chart Consolidation
 * 
 * This file tests the consolidated Gantt implementation to ensure:
 * 1. Shortcode implementation works correctly
 * 2. Widget integration works correctly  
 * 3. AJAX handlers respond properly
 * 4. No code duplication exists
 * 
 * Usage: Access via /wp-content/plugins/bookinn/test-gantt-consolidation.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if accessed directly
    require_once('../../../wp-load.php');
}

// Ensure user has proper permissions
if (!current_user_can('manage_options')) {
    wp_die('You do not have sufficient permissions to access this page.');
}

// Load BookInn plugin files
if (!class_exists('BookInn_Gantt_Page')) {
    require_once(ABSPATH . 'wp-content/plugins/bookinn/includes/class-bookinn-gantt-page.php');
}

get_header();
?>

<div class="wrap">
    <h1>📊 BookInn Gantt Chart Consolidation Test</h1>
    
    <div class="notice notice-info">
        <p><strong>Testing Consolidated Gantt Implementation</strong></p>
        <p>This page tests both shortcode and widget implementations to ensure they work correctly after consolidation.</p>
    </div>

    <!-- Test 1: Shortcode Implementation -->
    <div class="test-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2>🔧 Test 1: Shortcode Implementation</h2>
        <p>Testing the <code>[bookinn_gantt_demo]</code> shortcode with default settings:</p>
        
        <div class="shortcode-test">
            <?php echo do_shortcode('[bookinn_gantt_demo]'); ?>
        </div>
    </div>

    <!-- Test 2: Shortcode with Custom Parameters -->
    <div class="test-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2>🎛️ Test 2: Shortcode with Custom Parameters</h2>
        <p>Testing shortcode with custom parameters (no header, no controls):</p>
        
        <div class="shortcode-custom-test">
            <?php echo do_shortcode('[bookinn_gantt_demo show_header="false" show_controls="false"]'); ?>
        </div>
    </div>

    <!-- Test 3: Direct Method Call (Widget Style) -->
    <div class="test-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2>🔗 Test 3: Direct Method Call (Widget Style)</h2>
        <p>Testing direct method call as used in widget integration:</p>
        
        <div class="widget-style-test">
            <?php 
            if (class_exists('BookInn_Gantt_Page')) {
                echo BookInn_Gantt_Page::render_gantt_chart(array(
                    'show_header' => false,
                    'show_controls' => false,
                    'container_class' => 'gantt-container widget-integrated',
                    'chart_id' => 'testWidgetGanttChart',
                    'table_id' => 'testWidgetGanttTable',
                    'header_row_id' => 'testWidgetHeaderRow',
                    'body_id' => 'testWidgetGanttBody'
                ));
            }
            ?>
        </div>
    </div>

    <!-- Test 4: AJAX Endpoints -->
    <div class="test-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2>🌐 Test 4: AJAX Endpoints</h2>
        <p>Testing AJAX endpoints for data retrieval and filtering:</p>
        
        <div class="ajax-test-buttons">
            <button id="test-get-data" class="button button-primary">Test Get Rooms & Bookings</button>
            <button id="test-apply-filters" class="button button-secondary">Test Apply Filters</button>
            <div id="ajax-results" style="margin-top: 10px; padding: 10px; background: #f9f9f9; border-radius: 3px; display: none;">
                <h4>AJAX Results:</h4>
                <pre id="ajax-output"></pre>
            </div>
        </div>
    </div>

    <!-- Test 5: Performance Metrics -->
    <div class="test-section" style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h2>⚡ Test 5: Performance Metrics</h2>
        <p>Checking for code duplication and performance improvements:</p>
        
        <div class="performance-metrics">
            <ul>
                <li><strong>Gantt CSS loaded once:</strong> <span id="css-count">Checking...</span></li>
                <li><strong>Gantt JS loaded once:</strong> <span id="js-count">Checking...</span></li>
                <li><strong>Duplicate HTML elements:</strong> <span id="duplicate-check">Checking...</span></li>
                <li><strong>AJAX handlers available:</strong> <span id="ajax-handlers">Checking...</span></li>
            </ul>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Test AJAX endpoints
    $('#test-get-data').click(function() {
        $('#ajax-results').show();
        $('#ajax-output').text('Loading...');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'bookinn_get_rooms_and_bookings',
                nonce: '<?php echo wp_create_nonce('bookinn_gantt_demo'); ?>'
            },
            success: function(response) {
                $('#ajax-output').text(JSON.stringify(response, null, 2));
            },
            error: function(xhr, status, error) {
                $('#ajax-output').text('Error: ' + error);
            }
        });
    });
    
    $('#test-apply-filters').click(function() {
        $('#ajax-results').show();
        $('#ajax-output').text('Loading...');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'bookinn_gantt_apply_filters',
                nonce: '<?php echo wp_create_nonce('bookinn_gantt_filters'); ?>',
                viewMode: 'month',
                currentDate: new Date().toISOString().split('T')[0],
                filters: {
                    roomType: '',
                    bookingStatus: 'confirmed',
                    roomStatus: '',
                    customStart: '',
                    customEnd: ''
                }
            },
            success: function(response) {
                $('#ajax-output').text(JSON.stringify(response, null, 2));
            },
            error: function(xhr, status, error) {
                $('#ajax-output').text('Error: ' + error);
            }
        });
    });
    
    // Performance checks
    setTimeout(function() {
        // Check CSS loading
        var cssCount = $('link[href*="bookinn-gantt.css"]').length;
        $('#css-count').text(cssCount + ' instance(s)').css('color', cssCount === 1 ? 'green' : 'red');
        
        // Check JS loading  
        var jsCount = $('script[src*="bookinn-gantt.js"]').length;
        $('#js-count').text(jsCount + ' instance(s)').css('color', jsCount === 1 ? 'green' : 'red');
        
        // Check for duplicate elements
        var ganttTables = $('.gantt-table').length;
        $('#duplicate-check').text(ganttTables + ' Gantt table(s) found').css('color', ganttTables >= 1 ? 'green' : 'red');
        
        // Check AJAX handlers
        $('#ajax-handlers').text('Available (see test buttons above)').css('color', 'green');
        
    }, 1000);
});
</script>

<?php
get_footer();
?>
