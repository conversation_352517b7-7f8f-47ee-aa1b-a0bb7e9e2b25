[19-Aug-2025 13:31:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:31:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:31:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:31:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:32:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:32:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:32:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:32:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:32:02 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:32:02 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:32:04 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:32:04 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:32:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: ac589184b9...
[19-Aug-2025 13:32:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[19-Aug-2025 13:32:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:32:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:32:06 UTC] BookInn Filters Debug: Received filters: Array
(
)

[19-Aug-2025 13:32:06 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 33aabe0c6b
)

[19-Aug-2025 13:32:06 UTC] BookInn Filter: Final WHERE clause: 1=1
[19-Aug-2025 13:32:06 UTC] BookInn Filter: WHERE values: Array
(
)

[19-Aug-2025 13:32:06 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[19-Aug-2025 13:32:06 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[19-Aug-2025 13:32:06 UTC] BookInn Filter: Found 8 bookings, total: 8
[19-Aug-2025 13:32:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:32:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:32:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:32:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:32:09 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:32:09 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:32:10 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:32:10 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:32:12 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:32:12 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:20 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:20 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:22 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:35:22 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:35:23 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:23 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:27 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:27 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:28 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:28 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: ac589184b9...
[19-Aug-2025 13:35:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[19-Aug-2025 13:35:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:35 UTC] BookInn Filters Debug: Received filters: Array
(
)

[19-Aug-2025 13:35:35 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 33aabe0c6b
)

[19-Aug-2025 13:35:35 UTC] BookInn Filter: Final WHERE clause: 1=1
[19-Aug-2025 13:35:35 UTC] BookInn Filter: WHERE values: Array
(
)

[19-Aug-2025 13:35:35 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[19-Aug-2025 13:35:35 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[19-Aug-2025 13:35:35 UTC] BookInn Filter: Found 8 bookings, total: 8
[19-Aug-2025 13:35:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:35:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:35:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:55 UTC] BookInn AJAX: Nonce verification attempt with nonce: ac589184b9...
[19-Aug-2025 13:35:55 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[19-Aug-2025 13:35:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:56 UTC] BookInn Filters Debug: Received filters: Array
(
)

[19-Aug-2025 13:35:56 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 33aabe0c6b
)

[19-Aug-2025 13:35:56 UTC] BookInn Filter: Final WHERE clause: 1=1
[19-Aug-2025 13:35:56 UTC] BookInn Filter: WHERE values: Array
(
)

[19-Aug-2025 13:35:56 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[19-Aug-2025 13:35:56 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[19-Aug-2025 13:35:56 UTC] BookInn Filter: Found 8 bookings, total: 8
[19-Aug-2025 13:35:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:35:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:35:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:35:59 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:35:59 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:36:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:36:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:36:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:36:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:29 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:40:29 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:40:30 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:30 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:35 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:35 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: ac589184b9...
[19-Aug-2025 13:40:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[19-Aug-2025 13:40:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:41 UTC] BookInn Filters Debug: Received filters: Array
(
)

[19-Aug-2025 13:40:41 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 33aabe0c6b
)

[19-Aug-2025 13:40:41 UTC] BookInn Filter: Final WHERE clause: 1=1
[19-Aug-2025 13:40:41 UTC] BookInn Filter: WHERE values: Array
(
)

[19-Aug-2025 13:40:41 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[19-Aug-2025 13:40:41 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[19-Aug-2025 13:40:41 UTC] BookInn Filter: Found 8 bookings, total: 8
[19-Aug-2025 13:40:41 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:40:41 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:40:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:40:46 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:40:46 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:43:49 UTC] BookInn Widget Gantt: ajax_get_rooms_and_bookings called
[19-Aug-2025 13:43:53 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:43:53 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:43:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:43:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:43:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:43:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:43:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:43:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:43:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: ac589184b9...
[19-Aug-2025 13:43:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[19-Aug-2025 13:44:00 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:44:00 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:44:00 UTC] BookInn Filters Debug: Received filters: Array
(
)

[19-Aug-2025 13:44:00 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 33aabe0c6b
)

[19-Aug-2025 13:44:00 UTC] BookInn Filter: Final WHERE clause: 1=1
[19-Aug-2025 13:44:00 UTC] BookInn Filter: WHERE values: Array
(
)

[19-Aug-2025 13:44:00 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[19-Aug-2025 13:44:00 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[19-Aug-2025 13:44:00 UTC] BookInn Filter: Found 8 bookings, total: 8
[19-Aug-2025 13:44:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:44:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:44:01 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:44:01 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:44:02 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:44:02 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:44:02 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:44:02 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:44:05 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:44:05 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:44:06 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:44:06 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:44:07 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:44:07 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:32 UTC] BookInn Widget Gantt: ajax_get_rooms_and_bookings called
[19-Aug-2025 13:46:32 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:46:32 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:46:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:36 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:36 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:38 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:38 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:40 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:46:40 UTC] BookInn Filters Debug: Received filters: Array
(
)

[19-Aug-2025 13:46:40 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:46:40 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 33aabe0c6b
)

[19-Aug-2025 13:46:40 UTC] BookInn Filter: Final WHERE clause: 1=1
[19-Aug-2025 13:46:40 UTC] BookInn Filter: WHERE values: Array
(
)

[19-Aug-2025 13:46:40 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[19-Aug-2025 13:46:40 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[19-Aug-2025 13:46:40 UTC] BookInn Filter: Found 8 bookings, total: 8
[19-Aug-2025 13:46:42 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:42 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:44 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:44 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: ac589184b9...
[19-Aug-2025 13:46:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[19-Aug-2025 13:46:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:46:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:46:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:41 UTC] BookInn Widget Gantt: ajax_get_rooms_and_bookings called
[19-Aug-2025 13:48:41 UTC] BookInn Widget Gantt: ajax_get_rooms_and_bookings called
[19-Aug-2025 13:48:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:48:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:48:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:45 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:45 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:47 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:47 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:48 UTC] BookInn AJAX: Nonce verification attempt with nonce: ac589184b9...
[19-Aug-2025 13:48:48 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_ajax
[19-Aug-2025 13:48:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:50 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 13:48:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 13:48:50 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:50 UTC] BookInn Filters Debug: Received filters: Array
(
)

[19-Aug-2025 13:48:50 UTC] BookInn Filters Debug: POST data: Array
(
    [action] => bookinn_get_bookings
    [nonce] => 33aabe0c6b
)

[19-Aug-2025 13:48:50 UTC] BookInn Filter: Final WHERE clause: 1=1
[19-Aug-2025 13:48:50 UTC] BookInn Filter: WHERE values: Array
(
)

[19-Aug-2025 13:48:50 UTC] BookInn Filter: Final query: 
                SELECT b.*, 
                       r.room_number, 
                       rt.name as room_type_name,
                       g.first_name as guest_first_name,
                       g.last_name as guest_last_name,
                       g.email as guest_email,
                       g.phone as guest_phone,
                       CONCAT(g.first_name, ' ', g.last_name) as guest_name
                FROM wpbi_bookinn_bookings b
                LEFT JOIN wpbi_bookinn_rooms r ON b.room_id = r.id
                LEFT JOIN wpbi_bookinn_room_types rt ON r.room_type_id = rt.id
                LEFT JOIN wpbi_bookinn_guests g ON b.guest_id = g.id
                WHERE 1=1
                ORDER BY b.created_at DESC
                LIMIT 20 OFFSET 0
            
[19-Aug-2025 13:48:50 UTC] PHP Notice:  La funzione wpdb::prepare è stata richiamata <strong>in maniera scorretta</strong>. L'argomento wpdb::prepare() della query deve avere un valore. Leggi <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> per maggiori informazioni. (Questo messaggio è stato aggiunto nella versione 3.9.0.) in /var/www/html/bookInn/wp-includes/functions.php on line 6121
[19-Aug-2025 13:48:50 UTC] BookInn Filter: Found 8 bookings, total: 8
[19-Aug-2025 13:48:52 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:52 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:54 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:54 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 13:48:56 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 13:48:56 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 14:54:01 UTC] BookInn Widget Gantt: ajax_get_rooms_and_bookings called
[19-Aug-2025 14:54:01 UTC] BookInn Widget Gantt: ajax_get_rooms_and_bookings called
[19-Aug-2025 14:54:02 UTC] BookInn AJAX: Nonce verification attempt with nonce: 1ad989f568...
[19-Aug-2025 14:54:02 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_management_nonce
[19-Aug-2025 14:54:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 14:54:03 UTC] BookInn AJAX: Nonce verification attempt with nonce: 33aabe0c6b...
[19-Aug-2025 14:54:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
[19-Aug-2025 14:54:03 UTC] BookInn AJAX: Nonce verified successfully with action: bookinn_dashboard_nonce
