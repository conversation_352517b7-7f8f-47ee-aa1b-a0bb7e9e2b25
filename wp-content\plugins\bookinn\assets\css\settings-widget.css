/**
 * BookInn Settings Widget Styles
 * 
 * @package BookInn
 * @subpackage Assets
 * @since 1.0.0
 */

/* Settings Widget Container */
.bookinn-settings-widget {
    background: #fff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Settings Header */
.bookinn-settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

/* Tab Navigation */
.bookinn-settings-tabs {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.bookinn-tab-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    outline: none;
}

.bookinn-tab-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.bookinn-tab-button.active {
    background: #fff;
    color: #667eea;
    border-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.bookinn-tab-button .dashicons {
    font-size: 16px;
    line-height: 1;
}

/* Settings Actions */
.bookinn-settings-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Form Styles */
.bookinn-settings-form {
    padding: 0;
    background: #f8f9fa;
}

.bookinn-tab-content {
    display: none;
    padding: 30px;
    min-height: 400px;
    background: #fff;
}

.bookinn-tab-content.active {
    display: block;
}

.bookinn-settings-section {
    margin-bottom: 30px;
}

.bookinn-settings-section h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 8px;
}

/* Form Layout */
.bookinn-form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.bookinn-form-group {
    display: flex;
    flex-direction: column;
}

.bookinn-form-group label {
    font-weight: 600;
    margin-bottom: 6px;
    color: #34495e;
    font-size: 13px;
}

/* Input Styles */
.bookinn-input,
.bookinn-select {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
}

.bookinn-input:focus,
.bookinn-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.bookinn-select {
    cursor: pointer;
}

/* Checkbox Styles */
.bookinn-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 0;
    font-weight: 500;
    color: #34495e;
}

.bookinn-checkbox {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
    cursor: pointer;
}

.bookinn-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

/* Button Styles */
.bookinn-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    text-decoration: none;
    outline: none;
}

.bookinn-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.bookinn-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border: 1px solid transparent;
}

.bookinn-btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.bookinn-btn-secondary {
    background: #6c757d;
    color: #fff;
    border: 1px solid #6c757d;
}

.bookinn-btn-secondary:hover:not(:disabled) {
    background: #5a6268;
    border-color: #545b62;
    transform: translateY(-1px);
}

.bookinn-btn .dashicons {
    font-size: 16px;
    line-height: 1;
}

/* Status Messages */
.bookinn-settings-status {
    padding: 15px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.bookinn-alert {
    padding: 12px 15px;
    border-radius: 4px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bookinn-alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.bookinn-alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.bookinn-alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Animations */
@keyframes bookinn-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.bookinn-spin {
    animation: bookinn-spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bookinn-settings-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .bookinn-settings-tabs {
        justify-content: center;
    }
    
    .bookinn-tab-button {
        flex: 1;
        justify-content: center;
        min-width: 80px;
    }
    
    .bookinn-settings-actions {
        justify-content: center;
    }
    
    .bookinn-form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .bookinn-tab-content {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .bookinn-tab-button {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .bookinn-tab-button .dashicons {
        font-size: 14px;
    }
    
    .bookinn-tab-content {
        padding: 15px;
    }
    
    .bookinn-btn {
        padding: 8px 16px;
        font-size: 12px;
    }
    
    .bookinn-settings-actions {
        flex-direction: column;
    }
}

/* Error States */
.bookinn-input.error,
.bookinn-select.error {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* Loading States */
.bookinn-form-group.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Tooltips */
.bookinn-tooltip {
    position: relative;
    cursor: help;
}

.bookinn-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #f1f5f9;
    color: #fff;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.bookinn-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bookinn-settings-widget {
        background: #f1f5f9;
        border-color: #34495e;
    }
    
    .bookinn-tab-content {
        background: #f1f5f9;
        color: #ecf0f1;
    }
    
    .bookinn-settings-section h3 {
        color: #ecf0f1;
    }
    
    .bookinn-form-group label {
        color: #bdc3c7;
    }
    
    .bookinn-input,
    .bookinn-select {
        background: #34495e;
        border-color: #4a5f7a;
        color: #ecf0f1;
    }
    
    .bookinn-checkbox-group {
        background: #34495e;
        border-color: #4a5f7a;
    }
    
    .bookinn-checkbox-label {
        color: #bdc3c7;
    }
}
