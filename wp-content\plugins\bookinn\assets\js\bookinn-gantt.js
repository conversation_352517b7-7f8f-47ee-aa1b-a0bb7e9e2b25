// Fullscreen toggle per il container Gantt
window.bookinnToggleGanttFullscreen = function(e) {
    e.preventDefault();
    var container = document.querySelector('.bookinn-calendar-gantt-container');
    if (!container) return;
    if (!document.fullscreenElement) {
        if (container.requestFullscreen) {
            container.requestFullscreen();
        } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
        } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
        }
        container.classList.add('bookinn-gantt-fullscreen');
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
        container.classList.remove('bookinn-gantt-fullscreen');
    }
};
// BookInn Gantt Chart JS
(function($){

// --- COLLEGA FILTRI ESTERNI DASHBOARD AL GANTT ---
$(document).ready(function() {
    // Seleziona i filtri esterni (modifica gli ID se necessario)
    const statusFilter = document.getElementById('filter-status');
    const roomFilter = document.getElementById('filter-room');
    const dateFromFilter = document.getElementById('filter-date-from');
    const dateToFilter = document.getElementById('filter-date-to');
    const roomTypeFilter = document.getElementById('filter-room-type');
    const roomStatusFilter = document.getElementById('filter-room-status');
    const bookingStatusFilter = document.getElementById('filter-booking-status');
    const applyBtn = document.getElementById('apply-filters');
    // Variabili globali per i filtri
    window.ganttStatusFilter = '';
    window.ganttRoomFilter = '';
    window.ganttDateFromFilter = '';
    window.ganttDateToFilter = '';
    window.ganttRoomTypeFilter = '';
    window.ganttRoomStatusFilter = '';
    window.ganttBookingStatusFilter = '';

    function updateGanttFiltersFromExternal() {
        window.ganttStatusFilter = statusFilter ? statusFilter.value : '';
        window.ganttRoomFilter = roomFilter ? roomFilter.value : '';
        window.ganttDateFromFilter = dateFromFilter ? dateFromFilter.value : '';
        window.ganttDateToFilter = dateToFilter ? dateToFilter.value : '';
        window.ganttRoomTypeFilter = roomTypeFilter ? roomTypeFilter.value : '';
        window.ganttRoomStatusFilter = roomStatusFilter ? roomStatusFilter.value : '';
        window.ganttBookingStatusFilter = bookingStatusFilter ? bookingStatusFilter.value : '';
        renderGanttChart();
    }
    if (applyBtn) {
        applyBtn.addEventListener('click', function(e) {
            e.preventDefault();
            updateGanttFiltersFromExternal();
        });
    }
    // Applica anche su cambio diretto dei filtri (opzionale)
    [statusFilter, roomFilter, dateFromFilter, dateToFilter, roomTypeFilter, roomStatusFilter, bookingStatusFilter].forEach(function(el) {
        if (el) el.addEventListener('change', updateGanttFiltersFromExternal);
    });
});
let hotelData = { rooms: [], bookings: [] };
let currentStartDate = new Date();
let tooltip, draggedBooking = null, originalCell = null, dropIndicator = null, isResizing = false, resizeData = null;

function fetchRoomsAndBookings() {
    $.ajax({
        url: BookInnGantt.ajax_url,
        type: 'POST',
        dataType: 'json',
        data: {
            action: 'bookinn_widget_get_rooms_and_bookings',
            nonce: BookInnGantt.nonce
        },
        success: function(response) {
            console.log('[BookInn Gantt] Dati AJAX ricevuti:', response);
            if(response.success && response.data) {
                // Normalizza e valida i dati
                let rooms = Array.isArray(response.data.rooms) ? response.data.rooms : [];
                let bookings = Array.isArray(response.data.bookings) ? response.data.bookings : [];
                console.log('[BookInn Gantt] Camere:', rooms);
                console.log('[BookInn Gantt] Prenotazioni:', bookings);
                // Controlla formato bookings
                bookings = bookings.map(function(b) {
                    // Mapping for frontend management widget dataset
                    b.id = b.id ? String(b.id) : '';
                    b.roomId = b.room ? String(b.room) : (b.roomId ? String(b.roomId) : '');
                    b.guestName = b['guest name'] ? String(b['guest name']) : (b.guestName ? String(b.guestName) : '');
                    b.status = b.status ? String(b.status) : 'confirmed';
                    b.checkIn = b.checkin ? b.checkin : (b.checkIn ? b.checkIn : '');
                    b.checkOut = b.checkout ? b.checkout : (b.checkOut ? b.checkOut : '');
                    b.date = b.date ? b.date : '';
                    b.guests = b.guests ? parseInt(b.guests) : 1;
                    b.totalAmount = b.totalAmount ? parseFloat(b.totalAmount) : 0;
                    return b;
                });
                hotelData = { rooms, bookings };
                if (rooms.length === 0) {
                    console.warn('[BookInn Gantt] Nessuna camera trovata.');
                }
                if (bookings.length === 0) {
                    console.warn('[BookInn Gantt] Nessuna prenotazione trovata.');
                    alert('Nessuna prenotazione trovata nei dati ricevuti!');
                }
                renderGanttChart();
            } else {
                console.error('[BookInn Gantt] Errore dati:', response);
                alert('Errore nel caricamento dati: ' + (response.data || ''));
            }
        },
        error: function(xhr, status, error){
            console.error('[BookInn Gantt] AJAX error:', status, error);
            alert('Errore di rete nel caricamento dati.');
        }
    });
}


// === GANTT LOGIC ===
function renderGanttChart(options = {}) {
    // Support for multiple Gantt instances with different element IDs
    const config = {
        headerRowId: options.headerRowId || 'headerRow',
        bodyId: options.bodyId || 'ganttBody',
        tableId: options.tableId || 'ganttTable',
        ...options
    };

    updatePeriodDisplay();
    renderDateHeaders(config);
    renderGanttBody(config);
    renderBookingLegend();
}

function renderBookingLegend() {
    const legendContainer = document.querySelector('.legend');
    if (!legendContainer) return;
    // Stati reali trovati nelle prenotazioni
    const statusMap = {
        'confirmed': { label: 'Confirmed', color: '#28a745' },
        'pending': { label: 'Pending', color: '#ffc107' },
        'checkedin': { label: 'Checked-in', color: '#17a2b8' },
        'checked_in': { label: 'Checked-in', color: '#17a2b8' },
        'cancelled': { label: 'Cancelled', color: '#dc3545' },
        'no_show': { label: 'No Show', color: '#757575' }
    };
    // Trova tutti gli stati presenti nei dati
    const statuses = new Set(hotelData.bookings.map(b => b.status));
    legendContainer.innerHTML = '';
    statuses.forEach(status => {
        const info = statusMap[status] || { label: status, color: '#888' };
        const item = document.createElement('div');
        item.className = 'legend-item';
        item.innerHTML = `<div class="legend-color" style="background:${info.color}"></div><span>${info.label}</span>`;
        legendContainer.appendChild(item);
    });
// RIMOSSA GRAFFA IN ECCESSO
}

function updatePeriodDisplay() {
    let start, end;
    if (typeof currentViewMode !== 'undefined' && currentViewMode === 'month') {
        start = new Date(currentStartDate.getFullYear(), currentStartDate.getMonth(), 1);
        end = new Date(currentStartDate.getFullYear(), currentStartDate.getMonth() + 1, 0);
    } else if (typeof currentViewMode !== 'undefined' && currentViewMode === 'custom' && customStartDate && customEndDate) {
        start = new Date(customStartDate);
        end = new Date(customEndDate);
    } else {
        start = new Date(currentStartDate);
        end = new Date(currentStartDate);
        end.setDate(currentStartDate.getDate() + 6);
    }
    const options = { day: 'numeric', month: 'short', year: 'numeric' };
    const startStr = start.toLocaleDateString('it-IT', options);
    const endStr = end.toLocaleDateString('it-IT', options);
    $("#currentPeriod").text(`${startStr} - ${endStr}`);
}

function renderDateHeaders(config = {}) {
    const headerRowId = config.headerRowId || 'headerRow';
    const tableId = config.tableId || 'ganttTable';

    let headerRow = document.getElementById(headerRowId);
    if (!headerRow) {
        // Ricrea headerRow se non esiste
        const table = document.getElementById(tableId);
        if (table) {
            headerRow = document.createElement('tr');
            headerRow.id = headerRowId;
            table.tHead.appendChild(headerRow);
        } else {
            return;
        }
    }
    // Prima colonna: Room
    headerRow.innerHTML = '';
    const roomTh = document.createElement('th');
    roomTh.className = 'room-header';
    roomTh.textContent = 'Room';
    headerRow.appendChild(roomTh);
    // Colonne date
    let days = [];
    if (typeof currentViewMode !== 'undefined' && currentViewMode === 'month') {
        let year = currentStartDate.getFullYear();
        let month = currentStartDate.getMonth();
        let daysInMonth = new Date(year, month + 1, 0).getDate();
        for (let i = 1; i <= daysInMonth; i++) {
            days.push(new Date(year, month, i));
        }
    } else if (typeof currentViewMode !== 'undefined' && currentViewMode === 'custom' && customStartDate && customEndDate) {
        let d = new Date(customStartDate);
        let end = new Date(customEndDate);
        while (d <= end) {
            days.push(new Date(d));
            d.setDate(d.getDate() + 1);
        }
    } else {
        for (let i = 0; i < 7; i++) {
            let date = new Date(currentStartDate);
            date.setDate(currentStartDate.getDate() + i);
            days.push(date);
        }
    }
    days.forEach(date => {
        const dayName = date.toLocaleDateString('it-IT', { weekday: 'short' });
        const dayNum = date.getDate();
        const monthName = date.toLocaleDateString('it-IT', { month: 'short' });
        const th = document.createElement('th');
        th.className = 'date-header';
        th.innerHTML = `${dayName}<br>${dayNum} ${monthName}`;
        headerRow.appendChild(th);
    });
}

function renderGanttBody(config = {}) {
    const bodyId = config.bodyId || 'ganttBody';
    const tableId = config.tableId || 'ganttTable';

    let tbody = document.getElementById(bodyId);
    if (!tbody) {
        // Ricrea tbody se non esiste
        const table = document.getElementById(tableId);
        if (table) {
            tbody = document.createElement('tbody');
            tbody.id = bodyId;
            table.appendChild(tbody);
        } else {
            return;
        }
    }
    tbody.innerHTML = '';
    // Calcola i giorni da visualizzare (mese/intervallo custom/settimana)
    let days = [];
    if (typeof currentViewMode !== 'undefined' && currentViewMode === 'month') {
        let year = currentStartDate.getFullYear();
        let month = currentStartDate.getMonth();
        let daysInMonth = new Date(year, month + 1, 0).getDate();
        for (let i = 1; i <= daysInMonth; i++) {
            days.push(new Date(year, month, i));
        }
    } else if (typeof currentViewMode !== 'undefined' && currentViewMode === 'custom' && customStartDate && customEndDate) {
        let d = new Date(customStartDate);
        let end = new Date(customEndDate);
        while (d <= end) {
            days.push(new Date(d));
            d.setDate(d.getDate() + 1);
        }
    } else {
        for (let i = 0; i < 7; i++) {
            let date = new Date(currentStartDate);
            date.setDate(currentStartDate.getDate() + i);
            days.push(date);
        }
    }
    hotelData.rooms.forEach(room => {
        const row = document.createElement('tr');
        row.className = 'room-row';
        const roomCell = document.createElement('td');
        // Prima colonna: solo nome camera, senza classe room-header
        roomCell.style.minWidth = '320px';
        roomCell.style.width = '320px';
        roomCell.textContent = room.name ? room.name : '';
        row.appendChild(roomCell);
        const bookingsForRoom = getBookingsForRoom(room);
        days.forEach((date, i) => {
            const cell = document.createElement('td');
            cell.className = 'gantt-cell';
            cell.style.position = 'relative';
            cell.dataset.roomId = room.id;
            cell.dataset.date = date.toISOString().split('T')[0];
            setupCellDropListeners(cell);
            bookingsForRoom.forEach(booking => {
                const bookingElement = createBookingForDay(booking, date, i);
                if (bookingElement) {
                    cell.appendChild(bookingElement);
                }
            });
            row.appendChild(cell);
        });
        tbody.appendChild(row);
    });
}

function getBookingsForRoom(room) {
    const weekStart = new Date(currentStartDate);
    const weekEnd = new Date(currentStartDate);
    weekEnd.setDate(weekStart.getDate() + 6);
    // Mapping by room id (from booking.room or booking.roomId)
    return hotelData.bookings.filter(booking => {
        // Filtro per room
        if (String(booking.roomId) !== String(room.id)) return false;
        // Filtro per status (generico)
        if (window.ganttStatusFilter && booking.status !== window.ganttStatusFilter) return false;
        // Filtro per booking status (se presente)
        if (window.ganttBookingStatusFilter && booking.status !== window.ganttBookingStatusFilter) return false;
        // Filtro per room select (se diverso da id)
        if (window.ganttRoomFilter && String(room.id) !== String(window.ganttRoomFilter)) return false;
        // Filtro per room type (se presente)
        if (window.ganttRoomTypeFilter && room.room_type && String(room.room_type) !== String(window.ganttRoomTypeFilter)) return false;
        // Filtro per room status (se presente)
        if (window.ganttRoomStatusFilter && room.status && String(room.status) !== String(window.ganttRoomStatusFilter)) return false;
        // Filtro per date
        let checkIn = new Date(booking.checkIn);
        let checkOut = new Date(booking.checkOut);
        if (window.ganttDateFromFilter && checkOut < new Date(window.ganttDateFromFilter)) return false;
        if (window.ganttDateToFilter && checkIn > new Date(window.ganttDateToFilter)) return false;
        // Booking must overlap with visible period
        let periodStart, periodEnd;
        if (typeof currentViewMode !== 'undefined' && currentViewMode === 'month') {
            periodStart = new Date(currentStartDate.getFullYear(), currentStartDate.getMonth(), 1);
            periodEnd = new Date(currentStartDate.getFullYear(), currentStartDate.getMonth() + 1, 0);
        } else if (typeof currentViewMode !== 'undefined' && currentViewMode === 'custom' && customStartDate && customEndDate) {
            periodStart = new Date(customStartDate);
            periodEnd = new Date(customEndDate);
        } else {
            periodStart = new Date(currentStartDate);
            periodEnd = new Date(currentStartDate);
            periodEnd.setDate(currentStartDate.getDate() + 6);
        }
        return checkOut > periodStart && checkIn < periodEnd;
    });
}

function createBookingForDay(booking, currentDate, dayIndex) {
    const checkIn = new Date(booking.checkIn);
    const checkOut = new Date(booking.checkOut);
    // Calcola il periodo visualizzato
    let periodStart, days;
    if (typeof currentViewMode !== 'undefined' && currentViewMode === 'month') {
        periodStart = new Date(currentStartDate.getFullYear(), currentStartDate.getMonth(), 1);
        days = (new Date(currentStartDate.getFullYear(), currentStartDate.getMonth() + 1, 0)).getDate();
    } else if (typeof currentViewMode !== 'undefined' && currentViewMode === 'custom' && customStartDate && customEndDate) {
        periodStart = new Date(customStartDate);
        days = Math.floor((new Date(customEndDate) - periodStart) / (1000 * 60 * 60 * 24)) + 1;
    } else {
        periodStart = new Date(currentStartDate);
        days = 7;
    }
    const bookingStartInPeriod = Math.max(0, Math.floor((checkIn - periodStart) / (1000 * 60 * 60 * 24)));
    if (dayIndex === bookingStartInPeriod) {
        const bookingDiv = document.createElement('div');
        bookingDiv.className = `booking`;
        // Stato colore
        const statusColors = {
            'confirmed': '#eafbe7',
            'pending': '#fffbe6',
            'checkedin': '#e6f7fb',
            'checked_in': '#e6f7fb',
            'cancelled': '#fbe7ed',
            'no_show': '#f2f2f2'
        };
        const borderColors = {
            'confirmed': '#28a745',
            'pending': '#ffc107',
            'checkedin': '#17a2b8',
            'checked_in': '#17a2b8',
            'cancelled': '#dc3545',
            'no_show': '#757575'
        };
        const bg = statusColors[booking.status] || '#f7f7f7';
        const border = borderColors[booking.status] || '#e0e0e0';
        bookingDiv.style.background = bg;
        bookingDiv.style.border = `2px solid ${border}`;
        bookingDiv.style.color = '#222';
        // Calcola la fine visibile della barra
        let periodEnd = new Date(periodStart);
        periodEnd.setDate(periodStart.getDate() + days - 1);
        const visibleEnd = checkOut <= periodEnd ? checkOut : periodEnd;
        const duration = Math.ceil((visibleEnd - Math.max(checkIn, periodStart)) / (1000 * 60 * 60 * 24));
        bookingDiv.style.position = 'absolute';
        bookingDiv.style.left = '2px';
        bookingDiv.style.top = '4px';
        bookingDiv.style.bottom = '4px';
        bookingDiv.style.width = `${duration * 100 - 4}%`;
        bookingDiv.style.zIndex = '10';
        bookingDiv.textContent = booking.guestName ? booking.guestName : booking.id;
        bookingDiv.draggable = true;
        bookingDiv.dataset.bookingId = booking.id;
        addResizeHandles(bookingDiv, booking);
        setupBookingDragListeners(bookingDiv, booking);
        bookingDiv.addEventListener('mouseenter', (e) => !isResizing && showTooltip(e, booking));
        bookingDiv.addEventListener('mouseleave', (e) => !isResizing && hideTooltip());
        bookingDiv.addEventListener('mousemove', (e) => !isResizing && updateTooltipPosition(e));
        return bookingDiv;
    }
    return null;
}


// === Tooltip ===
function showTooltip(event, booking) {
    if (!tooltip) return;
    const checkInDate = new Date(booking.checkIn).toLocaleDateString('it-IT');
    const checkOutDate = new Date(booking.checkOut).toLocaleDateString('it-IT');
    const statusText = {
        'confirmed': 'Confermata',
        'pending': 'In Attesa',
        'checked_in': 'Check-in Effettuato',
        'checkedin': 'Check-in Effettuato',
        'cancelled': 'Cancellata',
        'no_show': 'No Show'
    };
    tooltip.innerHTML = `
        <strong>${booking.guestName || ''}</strong><br>
        Check-in: ${checkInDate}<br>
        Check-out: ${checkOutDate}<br>
        Ospiti: ${booking.guests || 1}<br>
        Stato: ${statusText[booking.status] || booking.status}<br>
        Totale: €${booking.totalAmount || 0}
    `;
    tooltip.style.opacity = '1';
    updateTooltipPosition(event);
}
function hideTooltip() {
    if (tooltip) tooltip.style.opacity = '0';
}
function updateTooltipPosition(event) {
    if (!tooltip) return;
    tooltip.style.left = `${event.clientX + 10}px`;
    tooltip.style.top = `${event.clientY - 10}px`;
}

// === Navigation ===
window.currentViewMode = 'month'; // 'month' o 'custom'
window.customStartDate = null;
window.customEndDate = null;
window.rollingDays = null;

window.previousMonth = function() {
    if (window.currentViewMode === 'custom' && window.customStartDate && window.customEndDate) {
        // Rolling custom: scorri di N giorni
        const n = Math.ceil((new Date(window.customEndDate) - new Date(window.customStartDate)) / (1000*60*60*24)) + 1;
        let start = new Date(window.customStartDate);
        start.setDate(start.getDate() - n);
        let end = new Date(start);
        end.setDate(start.getDate() + n - 1);
        window.customStartDate = start.toISOString().split('T')[0];
        window.customEndDate = end.toISOString().split('T')[0];
        renderGanttChart();
    } else {
        // Rolling mensile
        window.currentViewMode = 'month';
        window.customStartDate = null;
        window.customEndDate = null;
        currentStartDate.setMonth(currentStartDate.getMonth() - 1);
        renderGanttChart();
    }
};
window.nextMonth = function() {
    if (window.currentViewMode === 'custom' && window.customStartDate && window.customEndDate) {
        // Rolling custom: scorri di N giorni
        const n = Math.ceil((new Date(window.customEndDate) - new Date(window.customStartDate)) / (1000*60*60*24)) + 1;
        let start = new Date(window.customStartDate);
        start.setDate(start.getDate() + n);
        let end = new Date(start);
        end.setDate(start.getDate() + n - 1);
        window.customStartDate = start.toISOString().split('T')[0];
        window.customEndDate = end.toISOString().split('T')[0];
        renderGanttChart();
    } else {
        // Rolling mensile
        window.currentViewMode = 'month';
        window.customStartDate = null;
        window.customEndDate = null;
        currentStartDate.setMonth(currentStartDate.getMonth() + 1);
        renderGanttChart();
    }
};
window.goToToday = function() {
    window.currentViewMode = 'month';
    window.customStartDate = null;
    window.customEndDate = null;
    const today = new Date();
    currentStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
    renderGanttChart();
};
window.setCustomPeriod = function(start, end) {
    window.currentViewMode = 'custom';
    window.customStartDate = start;
    window.customEndDate = end;
    renderGanttChart();
};

window.resetGanttFilters = function() {
    // Reset solo dei filtri custom, non del periodo rolling
    document.getElementById('gantt-room-type-filter').value = '';
    document.getElementById('gantt-booking-status-filter').value = '';
    document.getElementById('gantt-room-status-filter').value = '';
    document.getElementById('customStart').value = '';
    document.getElementById('customEnd').value = '';
    window.currentViewMode = 'month';
    window.customStartDate = null;
    window.customEndDate = null;
    const today = new Date();
    currentStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
    renderGanttChart();
};

window.resetGanttFilters = function() {
    // Reset solo dei filtri custom, non del periodo rolling
    document.getElementById('gantt-room-type-filter').value = '';
    document.getElementById('gantt-booking-status-filter').value = '';
    document.getElementById('gantt-room-status-filter').value = '';
    document.getElementById('customStart').value = '';
    document.getElementById('customEnd').value = '';
    window.currentViewMode = 'month';
    window.customStartDate = null;
    window.customEndDate = null;
    // Non toccare currentStartDate (rolling rimane dove sta)
    renderGanttChart();
};

// === Drag & Drop ===
function setupBookingDragListeners(bookingElement, booking) {
    bookingElement.addEventListener('dragstart', (e) => {
        if (isResizing) { e.preventDefault(); return; }
        draggedBooking = booking;
        originalCell = bookingElement.parentElement;
        bookingElement.classList.add('dragging');
        hideTooltip();
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/plain', booking.id);
    });
    bookingElement.addEventListener('dragend', (e) => {
        bookingElement.classList.remove('dragging', 'invalid-drop');
        cleanupDragIndicators();
        draggedBooking = null;
        originalCell = null;
    });
}
function setupCellDropListeners(cell) {
    cell.addEventListener('dragover', (e) => {
        e.preventDefault();
        e.dataTransfer.dropEffect = 'move';
        if (!draggedBooking) return;
        const isValid = validateDrop(cell, draggedBooking);
        if (isValid) {
            cell.classList.add('drag-over');
            showDropIndicator(cell, e);
        } else {
            cell.classList.remove('drag-over');
            document.querySelector('.booking.dragging')?.classList.add('invalid-drop');
        }
    });
    cell.addEventListener('dragleave', (e) => {
        if (!cell.contains(e.relatedTarget)) {
            cell.classList.remove('drag-over');
            hideDropIndicator();
            document.querySelector('.booking.dragging')?.classList.remove('invalid-drop');
        }
    });
    cell.addEventListener('drop', (e) => {
        e.preventDefault();
        cell.classList.remove('drag-over');
        hideDropIndicator();
        if (!draggedBooking) return;
        const isValid = validateDrop(cell, draggedBooking);
        if (isValid) {
            handleBookingDrop(cell, draggedBooking, e);
        }
    });
}
function validateDrop(targetCell, booking) {
    const targetRoomId = targetCell.dataset.roomId;
    const targetDate = new Date(targetCell.dataset.date);
    const bookingDuration = Math.ceil((new Date(booking.checkOut) - new Date(booking.checkIn)) / (1000 * 60 * 60 * 24));
    if (targetCell === originalCell) return false;
    const conflictingBookings = hotelData.bookings.filter(b => {
        if (b.id === booking.id || b.roomId != targetRoomId) return false;
        const bStart = new Date(b.checkIn);
        const bEnd = new Date(b.checkOut);
        const newEnd = new Date(targetDate);
        newEnd.setDate(newEnd.getDate() + bookingDuration);
        return (targetDate < bEnd && newEnd > bStart);
    });
    return conflictingBookings.length === 0;
}
function handleBookingDrop(targetCell, booking, event) {
    const targetRoomId = targetCell.dataset.roomId;
    const targetDate = new Date(targetCell.dataset.date);
    const bookingDuration = Math.ceil((new Date(booking.checkOut) - new Date(booking.checkIn)) / (1000 * 60 * 60 * 24));
    const newCheckOut = new Date(targetDate);
    newCheckOut.setDate(newCheckOut.getDate() + bookingDuration);
    const bookingIndex = hotelData.bookings.findIndex(b => b.id === booking.id);
    if (bookingIndex !== -1) {
        hotelData.bookings[bookingIndex] = {
            ...booking,
            roomId: targetRoomId,
            checkIn: targetDate.toISOString().split('T')[0],
            checkOut: newCheckOut.toISOString().split('T')[0]
        };
        showMoveNotification(booking, targetRoomId, targetDate);
        window.bookinnUpdateBooking(hotelData.bookings[bookingIndex]);
        renderGanttChart();
    }
}
function showDropIndicator(cell, event) {
    hideDropIndicator();
    if (!dropIndicator) {
        dropIndicator = document.createElement('div');
        dropIndicator.className = 'drop-indicator';
    }
    const rect = cell.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const cellWidth = rect.width;
    const position = x < cellWidth / 2 ? 'left' : 'right';
    dropIndicator.style.display = 'block';
    dropIndicator.style.left = position === 'left' ? '0px' : 'calc(100% - 3px)';
    cell.appendChild(dropIndicator);
}
function hideDropIndicator() {
    if (dropIndicator && dropIndicator.parentElement) {
        dropIndicator.style.display = 'none';
        dropIndicator.parentElement.removeChild(dropIndicator);
    }
}
function cleanupDragIndicators() {
    document.querySelectorAll('.drag-over').forEach(cell => {
        cell.classList.remove('drag-over');
    });
    hideDropIndicator();
}
function showMoveNotification(booking, newRoomId, newDate) {
    const roomName = (hotelData.rooms.find(r => r.id == newRoomId)?.name) || newRoomId;
    const dateStr = newDate.toLocaleDateString('it-IT', { weekday: 'short', day: 'numeric', month: 'short' });
    const notification = document.createElement('div');
    notification.style.cssText = `position: fixed; top: 20px; right: 20px; background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.3); z-index: 10000; font-weight: bold; max-width: 300px; animation: slideIn 0.3s ease-out;`;
    notification.innerHTML = `<div style="margin-bottom: 5px;">📋 Prenotazione Spostata!</div><div style="font-size: 13px; opacity: 0.9;">${booking.guestName} → ${roomName}<br>Nuovo check-in: ${dateStr}</div>`;
    const style = document.createElement('style');
    style.textContent = `@keyframes slideIn { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }`;
    document.head.appendChild(style);
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.style.animation = 'slideIn 0.3s ease-out reverse';
        setTimeout(() => {
            if (notification.parentElement) document.body.removeChild(notification);
            if (style.parentElement) document.head.removeChild(style);
        }, 300);
    }, 3000);
}

// === Resize ===
function addResizeHandles(bookingElement, booking) {
    const leftHandle = document.createElement('div');
    leftHandle.className = 'resize-handle left';
    leftHandle.title = 'Modifica data check-in';
    const rightHandle = document.createElement('div');
    rightHandle.className = 'resize-handle right';
    rightHandle.title = 'Modifica data check-out';
    bookingElement.appendChild(leftHandle);
    bookingElement.appendChild(rightHandle);
    setupResizeListeners(leftHandle, booking, 'start');
    setupResizeListeners(rightHandle, booking, 'end');
}
function setupResizeListeners(handle, booking, type) {
    handle.addEventListener('mousedown', (e) => {
        e.stopPropagation();
        e.preventDefault();
        isResizing = true;
        resizeData = {
            booking: booking,
            type: type,
            startX: e.clientX,
            originalCheckIn: new Date(booking.checkIn),
            originalCheckOut: new Date(booking.checkOut),
            bookingElement: handle.parentElement,
            previewElement: null
        };
        resizeData.bookingElement.classList.add('resizing');
        resizeData.bookingElement.draggable = false;
        hideTooltip();
        document.addEventListener('mousemove', handleResize);
        document.addEventListener('mouseup', finishResize);
    });
}
function handleResize(e) {
    if (!isResizing || !resizeData) return;
    const deltaX = e.clientX - resizeData.startX;
    const cellWidth = 40;
    const daysDelta = Math.round(deltaX / cellWidth);
    let newCheckIn = new Date(resizeData.originalCheckIn);
    let newCheckOut = new Date(resizeData.originalCheckOut);
    if (resizeData.type === 'start') {
        newCheckIn.setDate(resizeData.originalCheckIn.getDate() + daysDelta);
        if (newCheckIn >= newCheckOut) {
            newCheckIn = new Date(newCheckOut);
            newCheckIn.setDate(newCheckOut.getDate() - 1);
        }
    } else {
        newCheckOut.setDate(resizeData.originalCheckOut.getDate() + daysDelta);
        if (newCheckOut <= newCheckIn) {
            newCheckOut = new Date(newCheckIn);
            newCheckOut.setDate(newCheckIn.getDate() + 1);
        }
    }
    const isValid = validateResize(resizeData.booking, newCheckIn, newCheckOut);
    showResizePreview(resizeData.booking, newCheckIn, newCheckOut, isValid);
}
function validateResize(booking, newCheckIn, newCheckOut) {
    const conflictingBookings = hotelData.bookings.filter(b => {
        if (b.id === booking.id || b.roomId != booking.roomId) return false;
        const bStart = new Date(b.checkIn);
        const bEnd = new Date(b.checkOut);
        return (newCheckIn < bEnd && newCheckOut > bStart);
    });
    return conflictingBookings.length === 0;
}
function showResizePreview(booking, newCheckIn, newCheckOut, isValid) {
    removeResizePreview();
    const roomRow = document.querySelector(`tr:has([data-room-id="${booking.roomId}"])`);
    if (!roomRow) return;
    const weekStart = new Date(currentStartDate);
    const weekEnd = new Date(currentStartDate);
    weekEnd.setDate(weekStart.getDate() + 6);
    const previewStart = newCheckIn >= weekStart ? newCheckIn : weekStart;
    const previewEnd = newCheckOut <= weekEnd ? newCheckOut : weekEnd;
    if (previewStart >= previewEnd || previewStart > weekEnd || previewEnd < weekStart) return;
    const startDayIndex = Math.floor((previewStart - weekStart) / (1000 * 60 * 60 * 24));
    const duration = Math.ceil((previewEnd - previewStart) / (1000 * 60 * 60 * 24));
    if (startDayIndex < 0 || startDayIndex >= 7) return;
    const targetCell = roomRow.cells[startDayIndex + 1];
    if (!targetCell) return;
    const preview = document.createElement('div');
    preview.className = 'resize-preview';
    preview.style.left = '2px';
    preview.style.width = `${(duration * 100) - 4}%`;
    if (!isValid) {
        preview.style.borderColor = '#dc3545';
        preview.style.background = 'rgba(220, 53, 69, 0.2)';
    }
    targetCell.appendChild(preview);
    resizeData.previewElement = preview;
}
function removeResizePreview() {
    if (resizeData && resizeData.previewElement) {
        resizeData.previewElement.remove();
        resizeData.previewElement = null;
    }
}
function finishResize(e) {
    if (!isResizing || !resizeData) return;
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', finishResize);
    const deltaX = e.clientX - resizeData.startX;
    const cellWidth = 40;
    const daysDelta = Math.round(deltaX / cellWidth);
    let newCheckIn = new Date(resizeData.originalCheckIn);
    let newCheckOut = new Date(resizeData.originalCheckOut);
    if (resizeData.type === 'start') {
        newCheckIn.setDate(resizeData.originalCheckIn.getDate() + daysDelta);
        if (newCheckIn >= newCheckOut) {
            newCheckIn = new Date(newCheckOut);
            newCheckIn.setDate(newCheckOut.getDate() - 1);
        }
    } else {
        newCheckOut.setDate(resizeData.originalCheckOut.getDate() + daysDelta);
        if (newCheckOut <= newCheckIn) {
            newCheckOut = new Date(newCheckIn);
            newCheckOut.setDate(newCheckIn.getDate() + 1);
        }
    }
    const isValid = validateResize(resizeData.booking, newCheckIn, newCheckOut);
    if (isValid && (newCheckIn.getTime() !== resizeData.originalCheckIn.getTime() || newCheckOut.getTime() !== resizeData.originalCheckOut.getTime())) {
        const bookingIndex = hotelData.bookings.findIndex(b => b.id === resizeData.booking.id);
        if (bookingIndex !== -1) {
            hotelData.bookings[bookingIndex] = {
                ...resizeData.booking,
                checkIn: newCheckIn.toISOString().split('T')[0],
                checkOut: newCheckOut.toISOString().split('T')[0]
            };
            showResizeNotification(resizeData.booking, newCheckIn, newCheckOut, resizeData.type);
            window.bookinnUpdateBooking(hotelData.bookings[bookingIndex]);
        }
    }
    resizeData.bookingElement.classList.remove('resizing');
    resizeData.bookingElement.draggable = true;
    removeResizePreview();
    isResizing = false;
    resizeData = null;
    renderGanttChart();
}
function showResizeNotification(booking, newCheckIn, newCheckOut, type) {
    const checkInStr = newCheckIn.toLocaleDateString('it-IT', { weekday: 'short', day: 'numeric', month: 'short' });
    const checkOutStr = newCheckOut.toLocaleDateString('it-IT', { weekday: 'short', day: 'numeric', month: 'short' });
    const typeText = type === 'start' ? 'Check-in modificato' : 'Check-out modificato';
    const duration = Math.ceil((newCheckOut - newCheckIn) / (1000 * 60 * 60 * 24));
    const notification = document.createElement('div');
    notification.style.cssText = `position: fixed; top: 20px; right: 20px; background: linear-gradient(135deg, #17a2b8, #6f42c1); color: white; padding: 15px 20px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.3); z-index: 10000; font-weight: bold; max-width: 300px; animation: slideIn 0.3s ease-out;`;
    notification.innerHTML = `<div style="margin-bottom: 5px;">📏 ${typeText}!</div><div style="font-size: 13px; opacity: 0.9;">${booking.guestName}<br>${checkInStr} → ${checkOutStr}<br>Durata: ${duration} ${duration === 1 ? 'notte' : 'notti'}</div>`;
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.style.animation = 'slideIn 0.3s ease-out reverse';
        setTimeout(() => {
            if (notification.parentElement) document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Esempio di hook dopo modifica:
window.bookinnUpdateBooking = function(booking) {
    $.ajax({
        url: BookInnGantt.ajax_url,
        type: 'POST',
        dataType: 'json',
        data: {
            action: 'bookinn_update_booking_dates',
            booking_id: booking.id,
            room_id: booking.roomId,
            check_in_date: booking.checkIn,
            check_out_date: booking.checkOut,
            nonce: BookInnGantt.nonce
        },
        success: function(response) {
            if(!response.success) alert('Errore aggiornamento: ' + (response.data || ''));
        },
        error: function(){
            alert('Errore di rete durante aggiornamento.');
        }
    });
};

$(function(){
    tooltip = document.getElementById('tooltip');
    fetchRoomsAndBookings();
});
})(jQuery);
