<?php
/**
 * Plugin Name: BookInn - Hotel Booking System
 * Plugin URI: https://bookinn.local
 * Description: Sistema completo di prenotazione per hotel e B&B con interfaccia frontend moderna, calendario drag&drop, dashboard analytics e integrazione OTA.
 * Version: 1.0.4
 * Author: BookInn Team
 * Author URI: https://bookinn.local
 * Text Domain: bookinn
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// BookInn PSR-4 Autoloader
require_once plugin_dir_path(__FILE__) . 'includes/class-bookinn-loader.php';
BookInn_Loader::register();



// Define plugin constants
define('BOOKINN_VERSION', '1.0.8');
define('BOOKINN_PLUGIN_FILE', __FILE__);
define('BOOKINN_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BOOKINN_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('BOOKINN_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BOOKINN_PLUGIN_BASENAME', plugin_basename(__FILE__));
// Aggiungi intervallo di cron personalizzato "every_minute" per compatibilità Action Scheduler
add_filter('cron_schedules', function($schedules) {
    if (!isset($schedules['every_minute'])) {
        $schedules['every_minute'] = array(
            'interval' => 60,
            'display'  => __('Ogni minuto', 'bookinn')
        );
    }
    return $schedules;
});


/**
 * Main BookInn Class
 */
final class BookInn {
    
    /**
     * The single instance of the class
     */
    private static $_instance = null;
    
    /**
     * Main BookInn Instance
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }
    
    /**
     * BookInn Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Hook into actions and filters
     */
    private function init_hooks() {
        add_action('init', array($this, 'init'), 0);
        add_action('plugins_loaded', array($this, 'plugins_loaded'));
        
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activation'));
        register_deactivation_hook(__FILE__, array($this, 'deactivation'));
    }
    
    /**
     * Init BookInn when WordPress initialises
     */
    public function init() {
        // Load plugin text domain
        $this->load_textdomain();
        
        // Include required files
        $this->includes();
        
        // Initialize components
        $this->init_components();
    }
    
    /**
     * Load Localisation files
     */
    public function load_textdomain() {
        load_plugin_textdomain('bookinn', false, dirname(plugin_basename(__FILE__)) . '/languages/');
    }
    
    /**
     * Include required core files
     */
    public function includes() {
        // Tutte le classi BookInn_ vengono ora caricate automaticamente dall'autoloader
        // Solo file non-class (helper, funzioni standalone) vanno inclusi manualmente qui
        
        // Include only non-class files if needed (functions, helpers, etc.)
        // Example: include_once BOOKINN_PLUGIN_DIR . 'includes/bookinn-functions.php';
    }
    
    /**
     * Initialize plugin components
     */
    public function init_components() {
        // Initialize core components (autoloader attivo)
        if (class_exists('BookInn_Core')) BookInn_Core::instance();
        if (class_exists('BookInn_Post_Types')) BookInn_Post_Types::instance();
        if (class_exists('BookInn_Assets')) BookInn_Assets::instance();
        if (class_exists('BookInn_API')) BookInn_API::instance();

        // Initialize REST API OTA endpoints
        if (class_exists('BookInn_REST_OTA')) {
            new BookInn_REST_OTA();
        }

        // Initialize AJAX handlers - Use both unified and widget-specific handlers
        if (class_exists('BookInn_Unified_Ajax_Handlers')) {
            new BookInn_Unified_Ajax_Handlers();  // Handles all AJAX requests (unified system)
        }

        // Initialize widget-specific AJAX handlers (for reports and dashboard functionality)
        if (class_exists('BookInn_Widget_Ajax_Handler')) {
            new BookInn_Widget_Ajax_Handler();  // Handles widget-specific AJAX requests
        }

        // Register widgets
        add_action('widgets_init', function() {
            if (class_exists('BookInn_Widget_Booking_Form')) {
                register_widget('BookInn_Widget_Booking_Form');
            }
            if (class_exists('BookInn_Widget_OTA_Management')) {
                register_widget('BookInn_Widget_OTA_Management');
            }
            if (class_exists('BookInn_Widget_Reports')) {
                register_widget('BookInn_Widget_Reports');
            }
            if (class_exists('BookInn_Widget_Settings')) {
                register_widget('BookInn_Widget_Settings');
            }
            if (class_exists('BookInn_Widget_Frontend_Dashboard')) {
                register_widget('BookInn_Widget_Frontend_Dashboard');
            }
        });
        
        // Initialize admin
        if (is_admin()) {
            BookInn_Admin::instance();
        }
    }
    
    /**
     * What type of request is this?
     */
    public function is_request($type) {
        switch ($type) {
            case 'admin':
                return is_admin();
            case 'ajax':
                return defined('DOING_AJAX');
            case 'cron':
                return defined('DOING_CRON');
            case 'frontend':
                return (!is_admin() || defined('DOING_AJAX')) && !defined('DOING_CRON');
        }
    }


    /**
     * Plugin activation hook
     */
    public function activation() {
        // Include required files first
        include_once BOOKINN_PLUGIN_DIR . 'includes/class-bookinn-data-initializer.php';
        include_once BOOKINN_PLUGIN_DIR . 'includes/class-bookinn-post-types.php';
        
        // Create database tables using unified manager
        BookInn_Database_Manager::create_tables();
        
        // Cleanup obsolete tables from old schema
        BookInn_Database_Manager::cleanup_obsolete_tables();
        
        // Update schema version
        BookInn_Database_Manager::update_schema_version('2.0.0');
        
        // Initialize sample data if tables are empty
        if (class_exists('BookInn_Data_Initializer')) {
            $missing_tables = BookInn_Data_Initializer::check_tables_exist();
            
            if (empty($missing_tables)) {
                // Initialize sample data for demo
                BookInn_Data_Initializer::init_sample_data();
                
                // Set a flag to show admin notice
                set_transient('bookinn_activation_notice', 'success', 30);
            } else {
                // Set error notice
                set_transient('bookinn_activation_notice', 'missing_tables', 30);
                set_transient('bookinn_missing_tables', $missing_tables, 30);
            }
        }
        
        // Create default post types
        BookInn_Post_Types::register_post_types();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set default options
        $this->set_default_options();
        
        // Schedule cron events
        $this->schedule_cron_events();
    }
    
    /**
     * Plugin deactivation hook
     */
    public function deactivation() {
        // Clear scheduled cron events
        wp_clear_scheduled_hook('bookinn_daily_cleanup');
        wp_clear_scheduled_hook('bookinn_sync_ota');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_options = array(
            'currency' => 'EUR',
            'date_format' => 'd/m/Y',
            'time_format' => 'H:i',
            'timezone' => get_option('timezone_string', 'UTC'),
            'email_notifications' => true,
            'ota_sync_enabled' => false,
            'min_booking_days' => 1,
            'max_booking_days' => 365,
            'payment_methods' => array('paypal', 'offline'),
            'frontend_widget_enabled' => true
        );
        
        add_option('bookinn_settings', $default_options);
    }
    
    /**
     * Schedule cron events
     */
    private function schedule_cron_events() {
        if (!wp_next_scheduled('bookinn_daily_cleanup')) {
            wp_schedule_event(time(), 'daily', 'bookinn_daily_cleanup');
        }
        
        if (!wp_next_scheduled('bookinn_sync_ota')) {
            wp_schedule_event(time(), 'hourly', 'bookinn_sync_ota');
        }
    }
    
    /**
     * Called when plugins are loaded
     */
    public function plugins_loaded() {
        // Hook for when all plugins are loaded
        do_action('bookinn_loaded');
    }
    
    /**
     * Get plugin version
     */
    public function get_version() {
        return BOOKINN_VERSION;
    }
    
    /**
     * Get plugin URL
     */
    public function plugin_url() {
        return untrailingslashit(plugins_url('/', __FILE__));
    }
    
    /**
     * Get plugin path
     */
    public function plugin_path() {
        return untrailingslashit(plugin_dir_path(__FILE__));
    }
}

/**
 * Returns the main instance of BookInn
 */
function BookInn() {
    return BookInn::instance();
}

// Initialize the plugin
BookInn();
