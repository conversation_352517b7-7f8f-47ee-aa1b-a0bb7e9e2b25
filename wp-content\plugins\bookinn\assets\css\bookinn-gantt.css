/* Gantt Controls Row compatta: controlli a sinistra, legenda a destra */
.bookinn-gantt-controls-row {
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	gap: 16px;
	padding: 10px 16px 6px 16px;
	background: #fff;
	border-bottom: 1px solid #e2e8f0;
	flex-wrap: wrap;
}
.bookinn-gantt-controls-left {
	display: flex;
	align-items: flex-end;
	gap: 18px;
	flex-wrap: wrap;
}
.bookinn-gantt-controls-right {
	display: flex;
	align-items: center;
	gap: 10px;
	margin-left: auto;
	flex-wrap: wrap;
}
.bookinn-gantt-controls-right .bookinn-calendar-legend {
	gap: 10px;
}
.bookinn-gantt-controls-right .bookinn-legend-title {
	margin-right: 6px;
	font-size: 12px;
	color: #374151;
	font-weight: 500;
}
/* Fullscreen Gantt container */
.bookinn-gantt-fullscreen {
	position: fixed !important;
	top: 0;
	left: 0;
	width: 100vw !important;
	height: 100vh !important;
	max-width: 100vw !important;
	max-height: 100vh !important;
	background: #fff;
	z-index: 9999;
	box-shadow: none;
	border-radius: 0 !important;
	padding: 0 !important;
	overflow: auto !important;
	display: flex;
	flex-direction: column;
}
/* Card invisibili per separare Custom Period e Filtri */
.bookinn-card-group {
	background: transparent;
	border-radius: 10px;
	padding: 12px 18px 12px 12px;
	margin-right: 24px;
	display: flex;
	align-items: flex-end;
	box-sizing: border-box;
	box-shadow: none;
}
.bookinn-card-group:last-child {
	margin-right: 0;
}
.bookinn-card-period {
	min-width: 220px;
	max-width: 320px;
	flex: 1 1 220px;
}
.bookinn-card-filters {
	min-width: 320px;
	max-width: 520px;
	flex: 2 1 320px;
	gap: 18px;
}

/* Spacing tra gruppi filtri e bottoni in .bookinn-gantt-level-2 */
.bookinn-gantt-level-2 > .bookinn-filter-group,
.bookinn-gantt-level-2 > .bookinn-filter-actions {
	margin-right: 20px;
}
.bookinn-gantt-level-2 > .bookinn-filter-group:last-child,
.bookinn-gantt-level-2 > .bookinn-filter-actions:last-child {
	margin-right: 0;
}
/* BookInn Gantt Chart Styles */
.gantt-container {
	font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
	width: 100%;
	max-width: 100vw;
	margin: 0 auto;
	background: #fff;
	border-radius: 8px;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
	overflow-x: auto;
	overflow-y: hidden;
	padding: 0;
}
.gantt-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; text-align: center; }
.gantt-header h2 { margin: 0; font-size: 24px; font-weight: 300; }
.gantt-controls { padding: 15px 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6; }
.date-nav { display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 10px; }
.date-nav button { background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; transition: background 0.2s; }
.date-nav button:hover { background: #0056b3; }
.current-period { font-weight: bold; color: #495057; }
.gantt-chart {
	width: 100%;
	overflow-x: auto;
	max-height: 600px;
	overflow-y: auto;
	background: #fff;
}
.gantt-table {
	width: 100%;
	min-width: 900px;
	border-collapse: collapse;
	background: #fff;
}
.room-header {
	background: #e9ecef;
	font-weight: bold;
	padding: 12px 8px;
	border-right: 2px solid #adb5bd;
	min-width: 320px;
	width: 320px;
	position: sticky;
	left: 0;
	z-index: 100;
	box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}
.date-header {
	background: #f8f9fa;
	padding: 8px 4px;
	text-align: center;
	font-size: 12px;
	border-bottom: 1px solid #dee2e6;
	min-width: 80px;
	position: sticky;
	top: 0;
	z-index: 5;
}
.gantt-cell {
	height: 40px;
	border: 1px solid #e9ecef;
	position: relative;
	min-width: 80px;
	background: #fff;
}
.gantt-cell:hover { background: #f8f9fa; }
.booking {
	position: absolute;
	top: 4px;
	bottom: 4px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #444;
	font-size: 13px;
	font-weight: 600;
	cursor: grab;
	transition: transform 0.2s, box-shadow 0.2s;
	overflow: visible;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding: 0 8px;
	user-select: none;
	border: 2px solid #e0e0e0;
	background: #f7f7f7;
	box-shadow: 0 1px 4px rgba(0,0,0,0.04);
}
.resize-handle { position: absolute; top: 0; bottom: 0; width: 8px; cursor: col-resize; background: rgba(255,255,255,0.3); opacity: 0; transition: opacity 0.2s; border-radius: 4px; }
.resize-handle.left { left: -4px; border-top-right-radius: 0; border-bottom-right-radius: 0; }
.resize-handle.right { right: -4px; border-top-left-radius: 0; border-bottom-left-radius: 0; }
.booking:hover .resize-handle { opacity: 1; }
.booking.resizing { cursor: col-resize; border-color: #007bff; box-shadow: 0 0 15px rgba(0,123,255,0.5); }
.resize-preview { position: absolute; top: 4px; bottom: 4px; border: 2px dashed #007bff; background: rgba(0,123,255,0.1); border-radius: 4px; pointer-events: none; z-index: 999; }
.booking:hover { transform: translateY(-2px); box-shadow: 0 4px 12px rgba(0,0,0,0.3); z-index: 20; }
.booking.confirmed { background: linear-gradient(135deg, #28a745, #20c997); }
.booking.pending { background: linear-gradient(135deg, #ffc107, #fd7e14); }
.booking.checkedin { background: linear-gradient(135deg, #17a2b8, #6f42c1); }
.booking.cancelled { background: linear-gradient(135deg, #dc3545, #e83e8c); opacity: 0.7; }
.legend { display: flex; gap: 20px; padding: 15px 20px; background: #f8f9fa; flex-wrap: wrap; justify-content: center; }
.legend-item { display: flex; align-items: center; gap: 8px; font-size: 14px; }
.legend-color { width: 20px; height: 15px; border-radius: 3px; }
.tooltip { position: absolute; background: rgba(0,0,0,0.9); color: white; padding: 10px; border-radius: 4px; font-size: 12px; z-index: 1000; pointer-events: none; opacity: 0; transition: opacity 0.2s; max-width: 200px; }
.room-row:nth-child(even) .gantt-cell { background: #f8f9fa; }
@media (max-width: 900px) {
	.gantt-table { min-width: 600px; }
	.room-header { min-width: 120px; width: 120px; }
}
@media (max-width: 600px) {
	.gantt-header, .gantt-controls { padding: 8px 6px; }
	.legend { padding: 8px 6px; }
	.gantt-table { min-width: 400px; }
	.room-header { min-width: 80px; width: 80px; font-size: 12px; }
	.date-header { min-width: 40px; font-size: 10px; }
}

/* Widget Integration Styles - Multi-Level Header Structure with Proper Ordering */
/*
 * LAYOUT ORDER FIX: Uses CSS flexbox order property to ensure proper vertical layout:
 * order: 0 - Section header (title & description)
 * order: 1 - Legend row (booking status colors)
 * order: 2 - Level 1 controls (view mode & navigation)
 * order: 3 - Level 2 controls (filters & date picker)
 * order: 4 - Level 3 controls (filter actions)
 * order: 5 - Gantt content (chart appears last, at bottom)
 */
.bookinn-calendar-gantt-container {
	background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
	border-radius: 8px;
	border: 1px solid #e2e8f0;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.bookinn-section-header {
	padding: 16px;
	border-bottom: 1px solid #e2e8f0;
	order: 0; /* Ensure section header appears first */
	flex-shrink: 0;
}

.bookinn-section-description {
	font-size: 13px;
	color: #64748b;
	margin: 4px 0 0 0;
	font-weight: 400;
}

/* Level 3 - Booking Status Legend (Top Row) */
.bookinn-gantt-legend-row {
	background: #ffffff;
	border-bottom: 1px solid #e2e8f0;
	padding: 8px 16px;
	display: flex;
	align-items: center;
	gap: 12px;
	flex-wrap: wrap;
	order: 1;
	flex-shrink: 0;
}

.bookinn-legend-title {
	color: #374151;
	font-size: 12px;
	font-weight: 500;
	margin: 0;
}

.bookinn-calendar-legend {
	display: flex;
	align-items: center;
	gap: 16px;
	flex-wrap: wrap;
}

.bookinn-legend-item {
	display: flex;
	align-items: center;
	gap: 6px;
	font-size: 12px;
	color: #374151;
}

.bookinn-legend-color {
	width: 12px;
	height: 12px;
	border-radius: 2px;
	border: 1px solid rgba(0,0,0,0.1);
}

.bookinn-status-confirmed { background: #10b981; }
.bookinn-status-pending { background: #f59e0b; }
.bookinn-status-checked_in { background: #3b82f6; }
.bookinn-status-cancelled { background: #ef4444; }

/* Level 1 - View Controls and Month Navigation (Second Row) */
.bookinn-gantt-level-1 {
	background: #f8fafc;
	border-bottom: 1px solid #e2e8f0;
	padding: 12px 16px;
	display: flex;
	align-items: end;
	gap: 20px;
	flex-wrap: wrap;
	order: 2;
	flex-shrink: 0;
}

/* Level 2 - Filter Controls and Date Picker (Third Row) */
.bookinn-gantt-level-2 {
	background: #ffffff;
	border-bottom: 1px solid #e2e8f0;
	padding: 12px 16px;
	display: flex;
	align-items: flex-end;
	gap: 0 20px;
	flex-wrap: wrap;
	order: 3;
	flex-shrink: 0;
}

/* Level 3 - Filter Actions (Fourth Row) */
.bookinn-gantt-level-3 {
	background: #f8fafc;
	border-bottom: 1px solid #e2e8f0;
	padding: 12px 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px;
	order: 4;
	flex-shrink: 0;
}

/* Filter Group Styling */
.bookinn-filter-group {
	display: flex;
	flex-direction: column;
	gap: 6px;
	min-width: 140px;
	flex: 1 1 140px;
	max-width: 220px;
	box-sizing: border-box;
}

.bookinn-filter-group label {
	font-size: 11px;
	font-weight: 600;
	color: #374151;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	margin: 0;
	white-space: nowrap;
}

.bookinn-select {
	font-size: 12px;
	padding: 6px 10px;
	border: 1px solid #d1d5db;
	border-radius: 4px;
	background: white;
	color: #374151;
	min-height: 32px;
	cursor: pointer;
}

.bookinn-select:focus {
	outline: none;
	border-color: #3b82f6;
	box-shadow: 0 0 0 1px #3b82f6;
}

/* Month Navigation */
.bookinn-month-navigation {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-wrap: nowrap;
	white-space: nowrap;
}

.bookinn-nav-btn {
	width: 32px;
	height: 32px;
	border: 1px solid #d1d5db;
	background: white;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	transition: all 0.2s;
	color: #374151;
}

.bookinn-nav-btn:hover {
	background: #f3f4f6;
	border-color: #9ca3af;
}

.bookinn-nav-btn .bookinn-icon {
	font-size: 16px;
	font-weight: bold;
	line-height: 1;
}

.bookinn-current-period {
	padding: 6px 12px;
	background: #ffffff;
	border: 1px solid #d1d5db;
	border-radius: 4px;
	font-size: 12px;
	font-weight: 500;
	color: #374151;
	min-width: 180px;
	text-align: center;
	white-space: nowrap;
}

/* Date Range Inputs */
.bookinn-date-range {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-wrap: nowrap;
	white-space: nowrap;
}

.bookinn-date-input {
	padding: 6px 8px;
	border: 1px solid #d1d5db;
	border-radius: 4px;
	font-size: 11px;
	background: #ffffff;
	color: #374151;
	min-width: 100px;
	max-width: 110px;
	cursor: pointer;
}

.bookinn-date-input:focus {
	outline: none;
	border-color: #3b82f6;
	box-shadow: 0 0 0 1px #3b82f6;
}

.bookinn-date-separator {
	font-size: 11px;
	color: #6b7280;
	font-weight: 500;
}

/* Filter Actions */
.bookinn-filter-actions {
	display: flex;
	gap: 12px;
	align-items: flex-end;
	justify-content: flex-start;
	flex-wrap: wrap;
	margin-left: 0;
}

.bookinn-btn-sm {
	padding: 6px 12px;
	font-size: 12px;
	font-weight: 500;
	border-radius: 4px;
	border: 1px solid;
	cursor: pointer;
	transition: all 0.2s;
	text-decoration: none;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	min-height: 32px;
}

.bookinn-btn-primary {
	background: #3b82f6;
	border-color: #3b82f6;
	color: white;
}

.bookinn-btn-primary:hover {
	background: #2563eb;
	border-color: #2563eb;
}

.bookinn-btn-secondary {
	background: #ffffff;
	border-color: #d1d5db;
	color: #374151;
}

.bookinn-btn-secondary:hover {
	background: #f3f4f6;
	border-color: #9ca3af;
}

.bookinn-btn-outline {
	background: transparent;
	border-color: #d1d5db;
	color: #374151;
}

.bookinn-btn-outline:hover {
	background: #f3f4f6;
}

.bookinn-calendar-gantt-content {
	position: relative;
	display: flex;
	flex-direction: column;
	flex: 1;
	order: 5; /* Ensure Gantt content appears after all control levels (1-4) */
}

.bookinn-gantt-wrapper {
	background: white;
	min-height: 600px;
	overflow: auto;
	flex: 1;
}

.gantt-container.widget-integrated {
	margin: 0;
	padding: 0;
	border: none;
	background: transparent;
	box-shadow: none;
}

.gantt-container.widget-integrated .legend {
	display: none; /* Hide original legend as we moved it to top */
}

.gantt-container.widget-integrated .gantt-header {
	display: none; /* Hide original header as we have our own */
}

.gantt-container.widget-integrated .gantt-controls {
	display: none; /* Hide original controls as we have our own */
}

/* Responsive adjustments for Widget Gantt */
@media (max-width: 768px) {
	.bookinn-gantt-level-1,
	.bookinn-gantt-level-2,
	.bookinn-gantt-level-3 {
		flex-direction: column;
		align-items: stretch;
		gap: 12px;
	}
	
	.bookinn-filter-group {
		min-width: 100%;
		max-width: 100%;
		flex: 1;
	}
	
	.bookinn-gantt-wrapper {
		min-height: 400px;
	}
	
	.bookinn-month-navigation {
		justify-content: center;
		flex-wrap: wrap;
	}
	
	.bookinn-date-range {
		justify-content: center;
		flex-wrap: wrap;
	}
	
	.bookinn-date-input {
		min-width: 120px;
		max-width: 140px;
	}
	
	.bookinn-filter-actions {
		justify-content: center;
		width: 100%;
	}
	
	.bookinn-calendar-legend {
		justify-content: center;
	}
	
	.bookinn-current-period {
		min-width: 120px;
	}
}

@media (max-width: 480px) {
	.bookinn-gantt-level-1,
	.bookinn-gantt-level-2,
	.bookinn-gantt-level-3 {
		padding: 8px 12px;
	}
	
	.bookinn-filter-group {
		gap: 4px;
	}
	
	.bookinn-filter-group label {
		font-size: 10px;
	}
	
	.bookinn-select,
	.bookinn-date-input {
		font-size: 11px;
		padding: 4px 6px;
		min-height: 28px;
	}
	
	.bookinn-btn-sm {
		padding: 4px 8px;
		font-size: 11px;
		min-height: 28px;
	}
}
