<?php
class BookInn_Gantt_Page {
    public static function init() {
        add_shortcode('bookinn_gantt_demo', [__CLASS__, 'shortcode']);
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_assets']);
        add_action('wp_ajax_bookinn_get_rooms_and_bookings', [__CLASS__, 'ajax_get_rooms_and_bookings']);
    }
    public static function enqueue_assets() {
        wp_register_style('bookinn-gantt', plugins_url('assets/css/bookinn-gantt.css', dirname(__FILE__)), [], '1.0-layout-fix-' . time());
        wp_register_script('bookinn-gantt', plugins_url('assets/js/bookinn-gantt.js', dirname(__FILE__)), ['jquery'], '1.0', true);
        wp_localize_script('bookinn-gantt', 'BookInnGantt', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce'    => wp_create_nonce('bookinn_gantt_demo')
        ]);
    }
    public static function shortcode() {
        wp_enqueue_style('bookinn-gantt');
        wp_enqueue_script('bookinn-gantt');
        ob_start();
        ?>
        <div class="gantt-container">
            <div class="gantt-header"><h2>📅 Hotel Booking Planning</h2></div>
            <div class="gantt-controls">
                <div class="date-nav">
                    <button onclick="previousMonth()">◀ Previous Month</button>
                    <div class="current-period" id="currentPeriod"></div>
                    <button onclick="nextMonth()">Next Month ▶</button>
                    <button onclick="goToToday()">Today</button>
                    <span style="margin-left:20px;">
                        <input type="date" id="customStart" style="margin-right:2px;">
                        <input type="date" id="customEnd" style="margin-right:2px;">
                        <button onclick="
                            var s=document.getElementById('customStart').value;
                            var e=document.getElementById('customEnd').value;
                            if(s&&e){setCustomPeriod(s,e);}" 
                        >Custom Period</button>
                    </span>
                </div>
            </div>
            <div class="gantt-chart">
                <table class="gantt-table">
                    <thead><tr id="headerRow"><th class="room-header"></th></tr></thead>
                    <tbody id="ganttBody"></tbody>
                </table>
            </div>
            <div class="legend">
                <div class="legend-item"><div class="legend-color confirmed"></div><span>Confirmed</span></div>
                <div class="legend-item"><div class="legend-color pending"></div><span>Pending</span></div>
                <div class="legend-item"><div class="legend-color checkedin"></div><span>Checked-in</span></div>
                <div class="legend-item"><div class="legend-color cancelled"></div><span>Cancelled</span></div>
            </div>
        </div>
        <?php
        return ob_get_clean();
    }
    
    // New method for widget integration without header and controls
    public static function render_chart_only() {
        wp_enqueue_style('bookinn-gantt');
        wp_enqueue_script('bookinn-gantt');
        ob_start();
        ?>
        <div class="gantt-container widget-integrated">
            <div class="gantt-chart">
                <table class="gantt-table">
                    <thead><tr id="headerRow"><th class="room-header"></th></tr></thead>
                    <tbody id="ganttBody"></tbody>
                </table>
            </div>
            <div class="legend">
                <div class="legend-item"><div class="legend-color confirmed"></div><span>Confirmed</span></div>
                <div class="legend-item"><div class="legend-color pending"></div><span>Pending</span></div>
                <div class="legend-item"><div class="legend-color checkedin"></div><span>Checked-in</span></div>
                <div class="legend-item"><div class="legend-color cancelled"></div><span>Cancelled</span></div>
            </div>
        </div>
        <div class="tooltip" id="tooltip"></div>
        <?php
        return ob_get_clean();
    }
    public static function ajax_get_rooms_and_bookings() {
        check_ajax_referer('bookinn_gantt_demo', 'nonce');
        global $wpdb;
        $rooms = $wpdb->get_results("SELECT id, name, room_number, status FROM {$wpdb->prefix}bookinn_rooms", ARRAY_A);
        $bookings = $wpdb->get_results("
            SELECT b.id, b.room_id as roomId, b.guest_id, g.first_name, g.last_name,
                   b.check_in_date as checkIn, b.check_out_date as checkOut, b.status, b.adults, b.children, b.total_amount as totalAmount
            FROM {$wpdb->prefix}bookinn_bookings b
            LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
        ", ARRAY_A);
        // Costruisci guestName e guests
        foreach ($bookings as &$b) {
            $b['guestName'] = trim(($b['first_name'] ?? '') . ' ' . ($b['last_name'] ?? ''));
            $b['guests'] = intval($b['adults'] ?? 1) + intval($b['children'] ?? 0);
        }
        unset($b);
        wp_send_json_success(['rooms' => $rooms, 'bookings' => $bookings]);
    }
}
BookInn_Gantt_Page::init();
