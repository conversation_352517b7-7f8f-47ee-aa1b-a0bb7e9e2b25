<?php
class BookInn_Gantt_Page {
    public static function init() {
        add_shortcode('bookinn_gantt_demo', [__CLASS__, 'shortcode']);
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_assets']);
        add_action('wp_ajax_bookinn_get_rooms_and_bookings', [__CLASS__, 'ajax_get_rooms_and_bookings']);
        add_action('wp_ajax_bookinn_gantt_apply_filters', [__CLASS__, 'ajax_gantt_apply_filters']);
    }
    public static function enqueue_assets() {
        wp_register_style('bookinn-gantt', plugins_url('assets/css/bookinn-gantt.css', dirname(__FILE__)), [], '1.0-layout-fix-' . time());
        wp_register_script('bookinn-gantt', plugins_url('assets/js/bookinn-gantt.js', dirname(__FILE__)), ['jquery'], '1.0', true);
        wp_localize_script('bookinn-gantt', 'BookInnGantt', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce'    => wp_create_nonce('bookinn_gantt_demo')
        ]);
    }
    public static function shortcode($atts = array()) {
        // Parse shortcode attributes
        $atts = shortcode_atts(array(
            'show_header' => true,
            'show_controls' => true,
            'show_legend' => true,
            'container_class' => 'gantt-container'
        ), $atts);

        // Convert string boolean values to actual booleans
        $atts['show_header'] = filter_var($atts['show_header'], FILTER_VALIDATE_BOOLEAN);
        $atts['show_controls'] = filter_var($atts['show_controls'], FILTER_VALIDATE_BOOLEAN);
        $atts['show_legend'] = filter_var($atts['show_legend'], FILTER_VALIDATE_BOOLEAN);

        return self::render_gantt_chart($atts);
    }
    
    /**
     * Unified method for rendering Gantt chart with configurable options
     * @param array $options Configuration options for the Gantt chart
     * @return string HTML output
     */
    public static function render_gantt_chart($options = array()) {
        // Default options
        $defaults = array(
            'show_header' => true,
            'show_controls' => true,
            'show_legend' => true,
            'show_tooltip' => true,
            'container_class' => 'gantt-container',
            'chart_id' => 'ganttChart',
            'table_id' => 'ganttTable',
            'header_row_id' => 'headerRow',
            'body_id' => 'ganttBody'
        );

        $options = array_merge($defaults, $options);

        wp_enqueue_style('bookinn-gantt');
        wp_enqueue_script('bookinn-gantt');

        ob_start();
        ?>
        <div class="<?php echo esc_attr($options['container_class']); ?>" id="<?php echo esc_attr($options['chart_id']); ?>">
            <?php if ($options['show_header']): ?>
            <div class="gantt-header">
                <h2>📅 Hotel Booking Planning</h2>
            </div>
            <?php endif; ?>

            <?php if ($options['show_controls']): ?>
            <div class="gantt-controls">
                <div class="date-nav">
                    <button onclick="previousMonth()">◀ Previous Month</button>
                    <div class="current-period" id="currentPeriod"></div>
                    <button onclick="nextMonth()">Next Month ▶</button>
                    <button onclick="goToToday()">Today</button>
                    <span style="margin-left:20px;">
                        <input type="date" id="customStart" style="margin-right:2px;">
                        <input type="date" id="customEnd" style="margin-right:2px;">
                        <button onclick="
                            var s=document.getElementById('customStart').value;
                            var e=document.getElementById('customEnd').value;
                            if(s&&e){setCustomPeriod(s,e);}"
                        >Custom Period</button>
                    </span>
                </div>
            </div>
            <?php endif; ?>

            <div class="gantt-chart">
                <table class="gantt-table" id="<?php echo esc_attr($options['table_id']); ?>">
                    <thead>
                        <tr id="<?php echo esc_attr($options['header_row_id']); ?>">
                            <th class="room-header"></th>
                        </tr>
                    </thead>
                    <tbody id="<?php echo esc_attr($options['body_id']); ?>">
                        <!-- Rows will be populated via JavaScript -->
                    </tbody>
                </table>
            </div>

            <?php if ($options['show_legend']): ?>
            <div class="legend">
                <div class="legend-item"><div class="legend-color confirmed"></div><span>Confirmed</span></div>
                <div class="legend-item"><div class="legend-color pending"></div><span>Pending</span></div>
                <div class="legend-item"><div class="legend-color checkedin"></div><span>Checked-in</span></div>
                <div class="legend-item"><div class="legend-color cancelled"></div><span>Cancelled</span></div>
            </div>
            <?php endif; ?>
        </div>

        <?php if ($options['show_tooltip']): ?>
        <div class="tooltip" id="tooltip"></div>
        <?php endif; ?>
        <?php
        return ob_get_clean();
    }

    /**
     * Legacy method for widget integration (backward compatibility)
     */
    public static function render_chart_only() {
        return self::render_gantt_chart(array(
            'show_header' => false,
            'show_controls' => false,
            'container_class' => 'gantt-container widget-integrated'
        ));
    }
    public static function ajax_get_rooms_and_bookings() {
        check_ajax_referer('bookinn_gantt_demo', 'nonce');
        global $wpdb;
        $rooms = $wpdb->get_results("SELECT id, name, room_number, status FROM {$wpdb->prefix}bookinn_rooms", ARRAY_A);
        $bookings = $wpdb->get_results("
            SELECT b.id, b.room_id as roomId, b.guest_id, g.first_name, g.last_name,
                   b.check_in_date as checkIn, b.check_out_date as checkOut, b.status, b.adults, b.children, b.total_amount as totalAmount
            FROM {$wpdb->prefix}bookinn_bookings b
            LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
        ", ARRAY_A);
        // Costruisci guestName e guests
        foreach ($bookings as &$b) {
            $b['guestName'] = trim(($b['first_name'] ?? '') . ' ' . ($b['last_name'] ?? ''));
            $b['guests'] = intval($b['adults'] ?? 1) + intval($b['children'] ?? 0);
        }
        unset($b);
        wp_send_json_success(['rooms' => $rooms, 'bookings' => $bookings]);
    }

    /**
     * AJAX handler for applying Gantt filters (used by widget)
     */
    public static function ajax_gantt_apply_filters() {
        check_ajax_referer('bookinn_gantt_filters', 'nonce');

        $view_mode = sanitize_text_field($_POST['viewMode'] ?? 'month');
        $current_date = sanitize_text_field($_POST['currentDate'] ?? date('Y-m-d'));
        $filters = $_POST['filters'] ?? array();

        // Sanitize filters
        $room_type = sanitize_text_field($filters['roomType'] ?? '');
        $booking_status = sanitize_text_field($filters['bookingStatus'] ?? '');
        $room_status = sanitize_text_field($filters['roomStatus'] ?? '');
        $custom_start = sanitize_text_field($filters['customStart'] ?? '');
        $custom_end = sanitize_text_field($filters['customEnd'] ?? '');

        global $wpdb;

        // Build room query with filters
        $room_where = "1=1";
        $room_params = array();

        if (!empty($room_type)) {
            $room_where .= " AND room_type = %s";
            $room_params[] = $room_type;
        }

        if (!empty($room_status)) {
            $room_where .= " AND status = %s";
            $room_params[] = $room_status;
        }

        $rooms_query = "SELECT id, name, room_number, status FROM {$wpdb->prefix}bookinn_rooms WHERE {$room_where}";
        $rooms = $wpdb->get_results($wpdb->prepare($rooms_query, $room_params), ARRAY_A);

        // Build booking query with filters
        $booking_where = "1=1";
        $booking_params = array();

        if (!empty($booking_status)) {
            $booking_where .= " AND b.status = %s";
            $booking_params[] = $booking_status;
        }

        // Date range filters
        if (!empty($custom_start)) {
            $booking_where .= " AND b.check_out_date >= %s";
            $booking_params[] = $custom_start;
        }

        if (!empty($custom_end)) {
            $booking_where .= " AND b.check_in_date <= %s";
            $booking_params[] = $custom_end;
        }

        $bookings_query = "
            SELECT b.id, b.room_id as roomId, b.guest_id, g.first_name, g.last_name,
                   b.check_in_date as checkIn, b.check_out_date as checkOut, b.status,
                   b.adults, b.children, b.total_amount as totalAmount
            FROM {$wpdb->prefix}bookinn_bookings b
            LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
            WHERE {$booking_where}
        ";

        $bookings = $wpdb->get_results($wpdb->prepare($bookings_query, $booking_params), ARRAY_A);

        // Process bookings data
        foreach ($bookings as &$b) {
            $b['guestName'] = trim(($b['first_name'] ?? '') . ' ' . ($b['last_name'] ?? ''));
            $b['guests'] = intval($b['adults'] ?? 1) + intval($b['children'] ?? 0);
        }
        unset($b);

        // Generate filtered Gantt HTML
        ob_start();
        echo self::render_chart_only();
        $html = ob_get_clean();

        wp_send_json_success(array(
            'html' => $html,
            'rooms' => $rooms,
            'bookings' => $bookings,
            'applied_filters' => $filters
        ));
    }
}
BookInn_Gantt_Page::init();
