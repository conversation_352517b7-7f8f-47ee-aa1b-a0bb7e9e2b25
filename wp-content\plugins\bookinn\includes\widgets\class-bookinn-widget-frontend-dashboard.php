<?php
/**
 * BookInn Frontend Dashboard Widget
 * 
 * Main frontend interface with tabbed dashboard for hotel management
 */

if (!defined('ABSPATH')) {
    exit;
}

class BookInn_Widget_Frontend_Dashboard extends WP_Widget {
    
    /**
     * Constructor
     */
    public function __construct() {
        $widget_options = array(
            'classname' => 'bookinn-widget bookinn-frontend-dashboard-widget',
            'description' => __('Complete hotel management dashboard with tabs for bookings, rooms, calendar, and reports.', 'bookinn'),
            'customize_selective_refresh' => true,
        );
        
        $control_options = array(
            'width' => 600,
            'height' => 400
        );
        
        parent::__construct(
            'bookinn_frontend_dashboard',
            __('BookInn - Frontend Dashboard', 'bookinn'),
            $widget_options,
            $control_options
        );
        
        // Optimized single enqueue strategy
        add_action('wp_enqueue_scripts', array($this, 'enqueue_assets'), 10);
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));

        // Single fallback hook instead of multiple
        add_action('wp_footer', array($this, 'final_css_check'), 15);

        // Initialize Gantt AJAX handlers
        $this->init_gantt_ajax_handlers();
    }

    /**
     * Final CSS check - optimized single fallback
     */
    public function final_css_check() {
        if (!wp_style_is('bookinn-management-unified', 'done') || !wp_style_is('bookinn-management-unified', 'enqueued')) {
            $this->force_enqueue_assets();
        }
    }

    /**
     * Late enqueue check in footer
     */
    public function late_enqueue_check() {
        if (!wp_style_is('bookinn-management-unified', 'done')) {
            $this->force_enqueue_assets();
        }
    }

    /**
     * Emergency CSS check - absolute last resort
     */
    public function emergency_css_check() {
        if (!wp_style_is('bookinn-frontend-dashboard', 'done')) {
            // Check if widget is actually being displayed
            global $wp_registered_widgets;
            $widget_displayed = false;
            
            foreach ($wp_registered_widgets as $widget_id => $widget) {
                if (strpos($widget_id, 'bookinn_frontend_dashboard') !== false) {
                    $widget_displayed = true;
                    break;
                }
            }
            
            if ($widget_displayed) {
                $this->emergency_css_fallback();
            }
        }
    }

    /**
     * Enqueue frontend assets - Always enqueue when widget class is loaded
     */
    public function enqueue_assets() {
        // UNIFIED SYSTEM: Load only the unified CSS (legacy files removed)
        wp_enqueue_style('bookinn-management-unified',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-management-unified.css',
            array(), '1.0.1-' . date('YmdHis'));

        // Enqueue vendor libraries (minified versions)
        wp_enqueue_script('bookinn-chartjs', 
            BOOKINN_PLUGIN_URL . 'assets/vendor/chart.min.js', 
            array(), '4.4.0', true);
        wp_enqueue_script('bookinn-fullcalendar', 
            BOOKINN_PLUGIN_URL . 'assets/vendor/fullcalendar.min.js', 
            array('jquery'), '6.1.11', true);
        wp_enqueue_style('bookinn-fullcalendar', 
            BOOKINN_PLUGIN_URL . 'assets/vendor/fullcalendar.min.css', 
            array(), '6.1.11');
        wp_enqueue_script('bookinn-flatpickr', 
            BOOKINN_PLUGIN_URL . 'assets/vendor/flatpickr.min.js', 
            array('jquery'), '4.6.13', true);
        wp_enqueue_style('bookinn-flatpickr', 
            BOOKINN_PLUGIN_URL . 'assets/vendor/flatpickr.min.css', 
            array(), '4.6.13');

        // CRITICAL FIX: Enqueue unified management system to resolve JavaScript errors
        // Load order is critical to prevent conflicts and ensure proper initialization
        if (wp_script_is('jquery', 'registered')) {

            // 1. Core BookInn main file - Contains unified modal system and base functionality
            wp_enqueue_script('bookinn-main',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-main.js',
                array('jquery'), BOOKINN_VERSION, true);

            // 2. Unified management system - Main consolidated functionality
            // FIXES: Syntax error, AJAX authentication, room type editing, event handlers
            wp_enqueue_script('bookinn-management-unified',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-management-unified.js',
                array('jquery', 'bookinn-main'), time(), true);

            // 3. Migration helper - Provides backward compatibility and system health monitoring
            wp_enqueue_script('bookinn-migration-helper',
                BOOKINN_PLUGIN_URL . 'assets/js/bookinn-migration-helper.js',
                array('jquery', 'bookinn-main', 'bookinn-management-unified'), BOOKINN_VERSION, true);

            // Legacy dashboard file removed - functionality moved to unified system

            // CRITICAL FIX: Localize script with proper nonce configuration
            // This fixes 403 Forbidden AJAX errors by providing both nonce types
            wp_localize_script('bookinn-management-unified', 'bookinn_dashboard', array(
                'ajax_url' => admin_url('admin-ajax.php'),                    // WordPress AJAX endpoint
                'rest_url' => rest_url('bookinn/v1/'),                       // REST API endpoint (future use)
                'nonce' => wp_create_nonce('bookinn_dashboard_nonce'),       // Primary nonce - matches AJAX handler
                'ajax_nonce' => wp_create_nonce('bookinn_ajax'),             // Secondary nonce for management AJAX calls
                'management_nonce' => wp_create_nonce('bookinn_management_nonce'), // Specific nonce for management system
                'frontend_nonce' => wp_create_nonce('bookinn_frontend_nonce'), // Frontend specific nonce
                'rest_nonce' => wp_create_nonce('wp_rest'),                  // REST API nonce
                'strings' => array(
                    'loading' => __('Loading...', 'bookinn'),
                    'error' => __('Error loading data', 'bookinn'),
                    'save_success' => __('Saved successfully', 'bookinn'),
                    'delete_confirm' => __('Are you sure you want to delete this item?', 'bookinn'),
                    'booking_saved' => __('Booking saved successfully', 'bookinn'),
                    'room_updated' => __('Room updated successfully', 'bookinn'),
                )
            ));
        }
    }

    /**
     * Force assets enqueue on widget display
     */
    public function force_enqueue_assets() {
        // Force enqueue unified CSS immediately
        wp_enqueue_style('bookinn-management-unified',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-management-unified.css',
            array(), BOOKINN_VERSION);
            
        // Add inline critical CSS to prevent FOUC - Enhanced Professional Style
        $inline_css = "
        .bookinn-dashboard-container {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06);
            margin: 12px 0;
            overflow: hidden;
            border: 1px solid #e2e8f0;
            line-height: 1.5;
        }
        .bookinn-tab-navigation {
            background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
            color: #fff;
            position: sticky;
            top: 0;
            z-index: 90;
        }
        .bookinn-tab-link {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            color: rgba(255, 255, 255, 0.85);
            text-decoration: none;
            font-weight: 500;
            font-size: 11px;
            min-height: 34px;
            border-bottom: 2px solid transparent;
            transition: all 0.15s ease-in-out;
        }
        .bookinn-tab-link:hover {
            color: #fff;
            background: rgba(255, 255, 255, 0.08);
        }
        .bookinn-tab-link.active {
            color: #fff;
            background: rgba(255, 255, 255, 0.12);
            border-bottom-color: rgba(255, 255, 255, 0.9);
            font-weight: 600;
        }
        .bookinn-tab-panel {
            display: none;
            padding: 20px;
        }
        .bookinn-tab-panel.active {
            display: block;
        }
        .bookinn-dashboard-content {
            display: grid;
            grid-template-columns: 1fr 280px;
            gap: 16px;
            padding: 16px;
            min-height: calc(85vh - 88px);
        }
        .bookinn-main-area {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06);
            border: 1px solid #e2e8f0;
        }
        .bookinn-metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .bookinn-metric-card {
            background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06);
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }
        .bookinn-metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(to bottom, #1e40af, #0f766e);
        }
        .bookinn-metric-value {
            font-size: 18px;
            font-weight: 700;
            color: #0f172a;
            margin: 0 0 4px 0;
            line-height: 1;
            font-family: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
        }
        .bookinn-metric-label {
            font-size: 11px;
            color: #475569;
            margin: 0 0 6px 0;
            font-weight: 500;
        }
        .bookinn-sidebar {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        .bookinn-sidebar-widget {
            background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
            border-radius: 8px;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.06);
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        @media (max-width: 1024px) {
            .bookinn-dashboard-content {
                grid-template-columns: 1fr;
            }
            .bookinn-metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 768px) {
            .bookinn-metrics-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            .bookinn-tab-panel {
                padding: 16px;
            }
            .bookinn-dashboard-content {
                padding: 12px;
                gap: 12px;
            }
        }
        /* Sub-tabs for room management */
        .bookinn-sub-tabs {
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            overflow: hidden;
        }
        .bookinn-sub-tab-nav {
            display: flex;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
            padding: 0;
        }
        .bookinn-sub-tab-link {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            text-decoration: none;
            color: #64748b;
            font-weight: 500;
            font-size: 11px;
            border-bottom: 2px solid transparent;
            transition: all 0.15s ease-in-out;
            min-height: 32px;
        }
        .bookinn-sub-tab-link:hover {
            color: #1e40af;
            background: rgba(30, 64, 175, 0.05);
        }
        .bookinn-sub-tab-link.active {
            color: #1e40af;
            background: white;
            border-bottom-color: #1e40af;
            font-weight: 600;
        }
        .bookinn-sub-tab-panel {
            display: none;
            padding: 16px;
        }
        .bookinn-sub-tab-panel.active {
            display: block;
        }
        

        ";
        wp_add_inline_style('bookinn-frontend-dashboard', $inline_css);
        
        // Enqueue the Gantt CSS for widget integration styles
        wp_enqueue_style(
            'bookinn-gantt-widget',
            BOOKINN_PLUGIN_URL . 'assets/css/bookinn-gantt.css',
            array('bookinn-frontend-dashboard'),
            BOOKINN_VERSION . '-gantt-fix-' . time() // Cache bust for layout fixes
        );
    }

    /**
     * Check if CSS is loaded and load if necessary
     */
    public function ensure_css_loaded() {
        // Check if our CSS is already enqueued
        if (!wp_style_is('bookinn-management-unified', 'enqueued')) {
            // CSS not loaded, force it
            $this->force_enqueue_assets();
        }
    }

    /**
     * Emergency CSS fallback - inline all critical styles
     */
    private function emergency_css_fallback() {
        $css_file = BOOKINN_PLUGIN_URL . 'assets/css/bookinn-management-unified.css';
        $css_path = str_replace(BOOKINN_PLUGIN_URL, BOOKINN_PLUGIN_PATH, $css_file);
        if (file_exists($css_path)) {
            $css_content = file_get_contents($css_path);
            echo '<style type="text/css" id="bookinn-emergency-css">' . $css_content . '</style>';
        }
    }

    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        if ('widgets.php' === $hook) {
            wp_enqueue_style('bookinn-widget-admin',
                BOOKINN_PLUGIN_URL . 'assets/css/bookinn-widget-admin.css',
                array(), BOOKINN_VERSION);
        }
    }

    /**
     * Display the widget content
     */
    public function widget($args, $instance) {
        // Check user permissions
        if (!$this->check_permissions()) {
            echo '<div class="bookinn-error">' . __('You do not have permission to access this dashboard.', 'bookinn') . '</div>';
            return;
        }

        // Ensure CSS is loaded with multiple fallback strategies
        $this->ensure_css_loaded();
        
        // Emergency fallback if CSS still not loaded
        if (!wp_style_is('bookinn-frontend-dashboard', 'done')) {
            $this->emergency_css_fallback();
        }

        echo $args['before_widget'];

        if (!empty($instance['title'])) {
            echo $args['before_title'] . apply_filters('widget_title', $instance['title']) . $args['after_title'];
        }

        $this->render_dashboard($instance);

        echo $args['after_widget'];
    }

    /**
     * Widget form in admin
     */
    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Hotel Dashboard', 'bookinn');
        $show_sidebar = isset($instance['show_sidebar']) ? (bool) $instance['show_sidebar'] : true;
        $default_tab = !empty($instance['default_tab']) ? $instance['default_tab'] : 'dashboard';
        $user_roles = !empty($instance['user_roles']) ? $instance['user_roles'] : array('administrator');
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Title:', 'bookinn'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text" 
                   value="<?php echo esc_attr($title); ?>">
        </p>
        
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('default_tab')); ?>"><?php _e('Default Tab:', 'bookinn'); ?></label>
            <select class="widefat" id="<?php echo esc_attr($this->get_field_id('default_tab')); ?>" 
                    name="<?php echo esc_attr($this->get_field_name('default_tab')); ?>">
                <option value="dashboard" <?php selected($default_tab, 'dashboard'); ?>><?php _e('Dashboard', 'bookinn'); ?></option>
                <option value="bookings" <?php selected($default_tab, 'bookings'); ?>><?php _e('Bookings', 'bookinn'); ?></option>
                <option value="rooms" <?php selected($default_tab, 'rooms'); ?>><?php _e('Rooms', 'bookinn'); ?></option>
                <option value="calendar" <?php selected($default_tab, 'calendar'); ?>><?php _e('Calendar', 'bookinn'); ?></option>
                <option value="reports" <?php selected($default_tab, 'reports'); ?>><?php _e('Reports', 'bookinn'); ?></option>
            </select>
        </p>
        
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_sidebar); ?> 
                   id="<?php echo esc_attr($this->get_field_id('show_sidebar')); ?>" 
                   name="<?php echo esc_attr($this->get_field_name('show_sidebar')); ?>" />
            <label for="<?php echo esc_attr($this->get_field_id('show_sidebar')); ?>"><?php _e('Show Sidebar', 'bookinn'); ?></label>
        </p>
        
        <p>
            <label><?php _e('User Roles (who can access):', 'bookinn'); ?></label><br>
            <?php
            $available_roles = wp_roles()->get_names();
            foreach ($available_roles as $role_key => $role_name) {
                $checked = in_array($role_key, $user_roles) ? 'checked' : '';
                ?>
                <label style="display: block; margin: 2px 0;">
                    <input type="checkbox" name="<?php echo esc_attr($this->get_field_name('user_roles')); ?>[]" 
                           value="<?php echo esc_attr($role_key); ?>" <?php echo $checked; ?>>
                    <?php echo esc_html($role_name); ?>
                </label>
                <?php
            }
            ?>
        </p>
        <?php
    }

    /**
     * Update widget settings
     */
    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['show_sidebar'] = isset($new_instance['show_sidebar']) ? (bool) $new_instance['show_sidebar'] : false;
        $instance['default_tab'] = (!empty($new_instance['default_tab'])) ? sanitize_text_field($new_instance['default_tab']) : 'dashboard';
        $instance['user_roles'] = (!empty($new_instance['user_roles']) && is_array($new_instance['user_roles'])) 
            ? array_map('sanitize_text_field', $new_instance['user_roles']) : array('administrator');
        
        return $instance;
    }

    /**
     * Check user permissions
     */
    private function check_permissions() {
        if (!is_user_logged_in()) {
            return false;
        }
        
        $user = wp_get_current_user();
        $allowed_roles = array('administrator', 'editor'); // Default roles
        
        // Get widget instance to check configured roles
        $widget_instances = $this->get_settings();
        if (!empty($widget_instances)) {
            foreach ($widget_instances as $instance) {
                if (!empty($instance['user_roles'])) {
                    $allowed_roles = $instance['user_roles'];
                    break;
                }
            }
        }
        
        return array_intersect($allowed_roles, $user->roles) ? true : false;
    }

    /**
     * Render main dashboard
     */
    private function render_dashboard($instance) {
        $default_tab = $instance['default_tab'] ?? 'dashboard';
        $show_sidebar = $instance['show_sidebar'] ?? true;
        ?>
        <div class="bookinn-dashboard-container" data-default-tab="<?php echo esc_attr($default_tab); ?>">
            <!-- Dashboard Header -->
            <div class="bookinn-dashboard-header">
                <div class="bookinn-header-content">
                    <h2 class="bookinn-dashboard-title"><?php _e('Hotel Management Dashboard', 'bookinn'); ?></h2>
                    <div class="bookinn-header-actions">
                        <div class="bookinn-user-menu">
                            <span class="bookinn-user-name"><?php echo esc_html(wp_get_current_user()->display_name); ?></span>
                            <div class="bookinn-notifications">
                                <span class="bookinn-notification-badge" id="bookinn-notification-count">0</span>
                                <i class="fa-solid fa-bell bookinn-fa"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <nav class="bookinn-tab-navigation" role="tablist" aria-label="<?php esc_attr_e('Dashboard Navigation', 'bookinn'); ?>">
                <div class="bookinn-nav-container">
                    <a href="#tab-dashboard" class="bookinn-tab-link active" data-tab="dashboard"
                       role="tab" aria-selected="true" aria-controls="tab-dashboard" tabindex="0">
                        <i class="fa-solid fa-gauge bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Dashboard', 'bookinn'); ?></span>
                    </a>
                    <a href="#tab-bookings" class="bookinn-tab-link" data-tab="bookings"
                       role="tab" aria-selected="false" aria-controls="tab-bookings" tabindex="-1">
                        <i class="fa-solid fa-calendar-check bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Bookings', 'bookinn'); ?></span>
                    </a>
                    <a href="#tab-rooms" class="bookinn-tab-link" data-tab="rooms"
                       role="tab" aria-selected="false" aria-controls="tab-rooms" tabindex="-1">
                        <i class="fa-solid fa-bed bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Rooms', 'bookinn'); ?></span>
                    </a>
                    <a href="#tab-calendar" class="bookinn-tab-link" data-tab="calendar"
                       role="tab" aria-selected="false" aria-controls="tab-calendar" tabindex="-1">
                        <i class="fa-solid fa-calendar-days bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Calendar', 'bookinn'); ?></span>
                    </a>
                    <a href="#tab-reports" class="bookinn-tab-link" data-tab="reports"
                       role="tab" aria-selected="false" aria-controls="tab-reports" tabindex="-1">
                        <i class="fa-solid fa-chart-line bookinn-fa" aria-hidden="true"></i>
                        <span><?php _e('Reports', 'bookinn'); ?></span>
                    </a>
                    <button class="bookinn-nav-toggle" id="bookinn-nav-toggle"
                            aria-label="<?php esc_attr_e('Toggle navigation menu', 'bookinn'); ?>"
                            aria-expanded="false">
                        <i class="fa-solid fa-bars bookinn-fa" aria-hidden="true"></i>
                    </button>
                </div>
            </nav>

            <!-- Main Content Area -->
            <div class="bookinn-dashboard-content">
                <div class="bookinn-main-area">
                    <!-- Tab Content Panels -->
                    <div class="bookinn-tab-panels">
                        <!-- Dashboard Tab -->
                        <div class="bookinn-tab-panel active" id="tab-dashboard"
                             role="tabpanel" aria-labelledby="tab-dashboard-link">
                            <?php $this->render_dashboard_tab(); ?>
                        </div>
                        
                        <!-- Bookings Tab -->
                        <div class="bookinn-tab-panel" id="tab-bookings"
                             role="tabpanel" aria-labelledby="tab-bookings-link" aria-hidden="true">
                            <?php $this->render_bookings_tab(); ?>
                        </div>
                        
                        <!-- Rooms Tab -->
                        <div class="bookinn-tab-panel" id="tab-rooms"
                             role="tabpanel" aria-labelledby="tab-rooms-link" aria-hidden="true">
                            <?php $this->render_rooms_tab(); ?>
                        </div>
                        
                        <!-- Calendar Tab with Sub-tabs -->
                        <div class="bookinn-tab-panel" id="tab-calendar"
                             role="tabpanel" aria-labelledby="tab-calendar-link" aria-hidden="true">
                            <div class="bookinn-calendar-tabs">
                                <div class="bookinn-sub-tab-nav">
                                    <a href="#calendar-main" class="bookinn-sub-tab-link active" data-tab="calendar-main">
                                        <?php _e('Calendar', 'bookinn'); ?>
                                    </a>
                                    <a href="#calendar-gantt" class="bookinn-sub-tab-link" data-tab="calendar-gantt">
                                        <?php _e('Booking Gantt Reservations', 'bookinn'); ?>
                                    </a>
                                </div>
                                <!-- Sub-tab Panels -->
                                <div class="bookinn-sub-tab-panel active" id="calendar-main">
                                    <?php $this->render_calendar_tab(); ?>
                                </div>
                                <div class="bookinn-sub-tab-panel" id="calendar-gantt">
                                    <div class="bookinn-calendar-gantt-container">
                                        <!-- HEADER: Section Title & Description -->
                                        <div class="bookinn-section-header" style="display: flex; align-items: flex-start; justify-content: space-between; gap: 12px;">
                                            <div style="flex:1;">
                                                <h3 style="margin-bottom: 0;"><?php _e('Booking Gantt Reservations', 'bookinn'); ?></h3>
                                                <p class="bookinn-section-description" style="margin-top: 4px; margin-bottom: 0; color: #6b7280; font-size: 13px; font-weight: 400;">
                                                    <?php _e('Interactive timeline view of room reservations with drag & drop functionality', 'bookinn'); ?>
                                                </p>
                                            </div>
                                            <button class="bookinn-gantt-fullscreen-btn" title="Fullscreen" onclick="bookinnToggleGanttFullscreen(event)" style="background: none; border: none; cursor: pointer; padding: 6px; margin-left: 8px;">
                                                <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <rect x="3" y="3" width="6" height="2" rx="1" fill="#374151"/>
                                                    <rect x="3" y="3" width="2" height="6" rx="1" fill="#374151"/>
                                                    <rect x="13" y="3" width="6" height="2" rx="1" fill="#374151"/>
                                                    <rect x="17" y="3" width="2" height="6" rx="1" fill="#374151"/>
                                                    <rect x="3" y="17" width="6" height="2" rx="1" fill="#374151"/>
                                                    <rect x="3" y="13" width="2" height="6" rx="1" fill="#374151"/>
                                                    <rect x="13" y="17" width="6" height="2" rx="1" fill="#374151"/>
                                                    <rect x="17" y="13" width="2" height="6" rx="1" fill="#374151"/>
                                                </svg>
                                            </button>
                                        </div>


                                        <!-- Level 1 - Controls (View Mode, Period) + Legend allineata a destra -->
                                        <div class="bookinn-gantt-controls-row">
                                            <div class="bookinn-gantt-controls-left">
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('View Mode:', 'bookinn'); ?></label>
                                                    <select id="gantt-view-mode" class="bookinn-select" onchange="handleViewChange()">
                                                        <option value="month"><?php _e('Monthly Grid', 'bookinn'); ?></option>
                                                        <option value="week"><?php _e('Weekly View', 'bookinn'); ?></option>
                                                        <option value="custom"><?php _e('Custom Range', 'bookinn'); ?></option>
                                                    </select>
                                                </div>
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Period:', 'bookinn'); ?></label>
                                                    <div class="bookinn-month-navigation">
                                                        <button type="button" onclick="previousMonth()" class="bookinn-nav-btn" title="<?php _e('Previous Month', 'bookinn'); ?>">
                                                            <i class="bookinn-icon">‹</i>
                                                        </button>
                                                        <div class="bookinn-current-period" id="currentPeriod"><?php _e('Loading...', 'bookinn'); ?></div>
                                                        <button type="button" onclick="nextMonth()" class="bookinn-nav-btn" title="<?php _e('Next Month', 'bookinn'); ?>">
                                                            <i class="bookinn-icon">›</i>
                                                        </button>
                                                        <button type="button" onclick="goToToday()" class="bookinn-btn bookinn-btn-sm bookinn-btn-outline"><?php _e('Today', 'bookinn'); ?></button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="bookinn-gantt-controls-right">
                                                <div class="bookinn-legend-title"><?php _e('Booking Status Legend:', 'bookinn'); ?></div>
                                                <div class="bookinn-calendar-legend">
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-confirmed"></div>
                                                        <span><?php _e('Confirmed', 'bookinn'); ?></span>
                                                    </div>
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-pending"></div>
                                                        <span><?php _e('Pending', 'bookinn'); ?></span>
                                                    </div>
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-checked_in"></div>
                                                        <span><?php _e('Checked In', 'bookinn'); ?></span>
                                                    </div>
                                                    <div class="bookinn-legend-item">
                                                        <div class="bookinn-legend-color bookinn-status-cancelled"></div>
                                                        <span><?php _e('Cancelled', 'bookinn'); ?></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Level 2 - Filter Controls, Date Picker & Actions (all inline) -->
                                        <div class="bookinn-gantt-level-2">
                                            <div class="bookinn-card-group bookinn-card-period">
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Custom Period:', 'bookinn'); ?></label>
                                                    <div class="bookinn-date-range">
                                                        <input type="date" id="customStart" class="bookinn-date-input" onchange="handleDateChange()">
                                                        <span class="bookinn-date-separator"><?php _e('to', 'bookinn'); ?></span>
                                                        <input type="date" id="customEnd" class="bookinn-date-input" onchange="handleDateChange()">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="bookinn-card-group bookinn-card-filters">
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Room Type:', 'bookinn'); ?></label>
                                                    <select id="gantt-room-type-filter" class="bookinn-select" onchange="handleFilterChange()">
                                                        <option value=""><?php _e('All Types', 'bookinn'); ?></option>
                                                        <?php $this->render_room_type_options(); ?>
                                                    </select>
                                                </div>
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Booking Status:', 'bookinn'); ?></label>
                                                    <select id="gantt-booking-status-filter" class="bookinn-select" onchange="handleFilterChange()">
                                                        <option value=""><?php _e('All', 'bookinn'); ?></option>
                                                        <option value="confirmed"><?php _e('Confirmed', 'bookinn'); ?></option>
                                                        <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                                                        <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                                                        <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                                                    </select>
                                                </div>
                                                <div class="bookinn-filter-group">
                                                    <label><?php _e('Room Status:', 'bookinn'); ?></label>
                                                    <select id="gantt-room-status-filter" class="bookinn-select" onchange="handleFilterChange()">
                                                        <option value=""><?php _e('All', 'bookinn'); ?></option>
                                                        <option value="available"><?php _e('Available', 'bookinn'); ?></option>
                                                        <option value="occupied"><?php _e('Occupied', 'bookinn'); ?></option>
                                                        <option value="maintenance"><?php _e('Maintenance', 'bookinn'); ?></option>
                                                        <option value="out_of_order"><?php _e('Out of Order', 'bookinn'); ?></option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="bookinn-filter-actions">
                                                <button type="button" onclick="applyGanttFilters()" class="bookinn-btn bookinn-btn-primary bookinn-btn-sm">
                                                    <?php _e('Apply Filters', 'bookinn'); ?>
                                                </button>
                                                <button type="button" onclick="resetGanttFilters()" class="bookinn-btn bookinn-btn-secondary bookinn-btn-sm">
                                                    <?php _e('Reset Filters', 'bookinn'); ?>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Gantt Content -->
                                        <div class="bookinn-calendar-gantt-content">
                                            <div class="bookinn-gantt-wrapper">
                                                <?php echo $this->render_gantt_chart(); ?>
                                            </div>
                                        </div>
                                        
                                        <script>
                                        // Global variables for Gantt control
                                        let currentGanttDate = new Date();
                                        let ganttViewMode = 'month';
                                        let ganttFilters = {
                                            roomType: '',
                                            bookingStatus: '',
                                            roomStatus: '',
                                            customStart: '',
                                            customEnd: ''
                                        };
                                        
                                        // Initialize Gantt controls
                                        document.addEventListener('DOMContentLoaded', function() {
                                            updatePeriodDisplay();
                                            initializeDateInputs();
                                        });
                                        
                                        // View mode change handler
                                        function handleViewChange() {
                                            ganttViewMode = document.getElementById('gantt-view-mode').value;
                                            updatePeriodDisplay();
                                            refreshGanttDisplay();
                                            console.log('View mode changed to:', ganttViewMode);
                                        }
                                        
                                        // Navigation functions
                                        function navigatePrevious() {
                                            if (ganttViewMode === 'month') {
                                                currentGanttDate.setMonth(currentGanttDate.getMonth() - 1);
                                            } else if (ganttViewMode === 'week') {
                                                currentGanttDate.setDate(currentGanttDate.getDate() - 7);
                                            }
                                            updatePeriodDisplay();
                                            refreshGanttDisplay();
                                        }
                                        
                                        function navigateNext() {
                                            if (ganttViewMode === 'month') {
                                                currentGanttDate.setMonth(currentGanttDate.getMonth() + 1);
                                            } else if (ganttViewMode === 'week') {
                                                currentGanttDate.setDate(currentGanttDate.getDate() + 7);
                                            }
                                            updatePeriodDisplay();
                                            refreshGanttDisplay();
                                        }
                                        
                                        function navigateToday() {
                                            currentGanttDate = new Date();
                                            updatePeriodDisplay();
                                            refreshGanttDisplay();
                                        }
                                        
                                        // Update period display
                                        function updatePeriodDisplay() {
                                            const periodElement = document.getElementById('currentPeriod');
                                            if (!periodElement) return;
                                            
                                            let displayText = '';
                                            const locale = '<?php echo str_replace('_', '-', get_locale()); ?>'; // Fix locale format for JavaScript
                                            
                                            try {
                                                if (ganttViewMode === 'month') {
                                                    displayText = currentGanttDate.toLocaleDateString(locale, { 
                                                        year: 'numeric', 
                                                        month: 'long' 
                                                    });
                                                } else if (ganttViewMode === 'week') {
                                                    const weekStart = new Date(currentGanttDate);
                                                    weekStart.setDate(currentGanttDate.getDate() - currentGanttDate.getDay());
                                                    const weekEnd = new Date(weekStart);
                                                    weekEnd.setDate(weekStart.getDate() + 6);
                                                    displayText = weekStart.toLocaleDateString(locale) + ' - ' + weekEnd.toLocaleDateString(locale);
                                                } else if (ganttViewMode === 'custom' && ganttFilters.customStart && ganttFilters.customEnd) {
                                                    displayText = ganttFilters.customStart + ' - ' + ganttFilters.customEnd;
                                                } else {
                                                    displayText = currentGanttDate.toLocaleDateString(locale);
                                                }
                                            } catch (error) {
                                                console.warn('Locale error, falling back to default:', error);
                                                // Fallback to default locale if there's an error
                                                if (ganttViewMode === 'month') {
                                                    displayText = currentGanttDate.toLocaleDateString('en-US', { 
                                                        year: 'numeric', 
                                                        month: 'long' 
                                                    });
                                                } else if (ganttViewMode === 'week') {
                                                    const weekStart = new Date(currentGanttDate);
                                                    weekStart.setDate(currentGanttDate.getDate() - currentGanttDate.getDay());
                                                    const weekEnd = new Date(weekStart);
                                                    weekEnd.setDate(weekStart.getDate() + 6);
                                                    displayText = weekStart.toLocaleDateString() + ' - ' + weekEnd.toLocaleDateString();
                                                } else if (ganttViewMode === 'custom' && ganttFilters.customStart && ganttFilters.customEnd) {
                                                    displayText = ganttFilters.customStart + ' - ' + ganttFilters.customEnd;
                                                } else {
                                                    displayText = currentGanttDate.toLocaleDateString();
                                                }
                                            }
                                            
                                            periodElement.textContent = displayText;
                                        }
                                        
                                        // Initialize date inputs with current values
                                        function initializeDateInputs() {
                                            const today = new Date();
                                            const startInput = document.getElementById('customStart');
                                            const endInput = document.getElementById('customEnd');
                                            
                                            if (startInput && !startInput.value) {
                                                startInput.value = today.toISOString().split('T')[0];
                                            }
                                            if (endInput && !endInput.value) {
                                                const nextMonth = new Date(today);
                                                nextMonth.setMonth(today.getMonth() + 1);
                                                endInput.value = nextMonth.toISOString().split('T')[0];
                                            }
                                        }
                                        
                                        // Date change handler
                                        function handleDateChange() {
                                            const customStart = document.getElementById('customStart').value;
                                            const customEnd = document.getElementById('customEnd').value;
                                            
                                            if (customStart && customEnd) {
                                                ganttFilters.customStart = customStart;
                                                ganttFilters.customEnd = customEnd;
                                                ganttViewMode = 'custom';
                                                document.getElementById('gantt-view-mode').value = 'custom';
                                                updatePeriodDisplay();
                                            }
                                        }
                                        
                                        // Filter change handler
                                        function handleFilterChange() {
                                            // Auto-apply filters on change for better UX
                                            applyGanttFilters();
                                        }
                                        
                                        // Apply all filters
                                        function applyGanttFilters() {
                                            // Collect all filter values
                                            ganttFilters.roomType = document.getElementById('gantt-room-type-filter').value;
                                            ganttFilters.bookingStatus = document.getElementById('gantt-booking-status-filter').value;
                                            ganttFilters.roomStatus = document.getElementById('gantt-room-status-filter').value;
                                            ganttFilters.customStart = document.getElementById('customStart').value;
                                            ganttFilters.customEnd = document.getElementById('customEnd').value;
                                            
                                            // Apply custom period if set
                                            if (ganttFilters.customStart && ganttFilters.customEnd) {
                                                if (typeof setCustomPeriod === 'function') {
                                                    setCustomPeriod(ganttFilters.customStart, ganttFilters.customEnd);
                                                }
                                            }
                                            
                                            // Send AJAX request to get filtered data and update the existing Gantt chart
                                            jQuery.ajax({
                                                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                                                type: 'POST',
                                                dataType: 'json',
                                                data: {
                                                    action: 'bookinn_widget_gantt_apply_filters',
                                                    nonce: '<?php echo wp_create_nonce('bookinn_gantt_widget'); ?>',
                                                    viewMode: ganttViewMode,
                                                    currentDate: currentGanttDate.toISOString().split('T')[0],
                                                    filters: ganttFilters
                                                },
                                                success: function(response) {
                                                    if (response.success && response.data) {
                                                        // Update the global hotelData with filtered results
                                                        if (typeof hotelData !== 'undefined') {
                                                            hotelData.rooms = response.data.rooms || [];
                                                            hotelData.bookings = response.data.bookings || [];

                                                            // Re-render the Gantt chart with filtered data using widget-specific IDs
                                                            if (typeof renderGanttChart === 'function') {
                                                                renderGanttChart({
                                                                    headerRowId: 'widgetHeaderRow',
                                                                    bodyId: 'widgetGanttBody',
                                                                    tableId: 'widgetGanttTable'
                                                                });
                                                            }

                                                            console.log('Widget Gantt chart updated with filtered data:', ganttFilters);
                                                        } else {
                                                            console.warn('hotelData not available, cannot update chart');
                                                        }
                                                    } else {
                                                        console.warn('Filter application failed:', response.data);
                                                    }
                                                },
                                                error: function(xhr, status, error) {
                                                    console.error('AJAX error applying filters:', error);
                                                }
                                            });
                                        }
                                        
                                        // Reset all filters
                                        function resetGanttFilters() {
                                            // Reset filter values
                                            document.getElementById('gantt-room-type-filter').value = '';
                                            document.getElementById('gantt-booking-status-filter').value = '';
                                            document.getElementById('gantt-room-status-filter').value = '';
                                            document.getElementById('gantt-view-mode').value = 'month';
                                            
                                            // Reset date inputs to defaults
                                            initializeDateInputs();
                                            
                                            // Reset internal state
                                            ganttViewMode = 'month';
                                            currentGanttDate = new Date();
                                            ganttFilters = {
                                                roomType: '',
                                                bookingStatus: '',
                                                roomStatus: '',
                                                customStart: '',
                                                customEnd: ''
                                            };
                                            
                                            updatePeriodDisplay();
                                            refreshGanttDisplay();
                                            console.log('Filters reset');
                                        }
                                        
                                        // Refresh Gantt display
                                        function refreshGanttDisplay() {
                                            // Call existing Gantt refresh functions
                                            if (typeof refreshGanttChart === 'function') {
                                                refreshGanttChart();
                                            } else if (typeof fetchRoomsAndBookings === 'function') {
                                                fetchRoomsAndBookings();
                                            } else if (typeof window.ganttChart !== 'undefined' && window.ganttChart.refresh) {
                                                window.ganttChart.refresh();
                                            }
                                        }
                                        
                                        // Legacy function support
                                        function previousMonth() { navigatePrevious(); }
                                        function nextMonth() { navigateNext(); }
                                        function goToToday() { navigateToday(); }
                                        function setCustomPeriod(start, end) {
                                            document.getElementById('customStart').value = start;
                                            document.getElementById('customEnd').value = end;
                                            handleDateChange();
                                        }

                                        // Initialize Widget Gantt Chart
                                        document.addEventListener('DOMContentLoaded', function() {
                                            // Override BookInnGantt settings for widget
                                            if (typeof BookInnGantt !== 'undefined') {
                                                BookInnGantt.ajax_url = '<?php echo admin_url('admin-ajax.php'); ?>';
                                                BookInnGantt.nonce = '<?php echo wp_create_nonce('bookinn_gantt_widget'); ?>';
                                            }

                                            // Initialize widget Gantt data loading
                                            if (typeof fetchRoomsAndBookings === 'function') {
                                                // Override the fetch function to use widget endpoints
                                                window.fetchWidgetRoomsAndBookings = function() {
                                                    jQuery.ajax({
                                                        url: '<?php echo admin_url('admin-ajax.php'); ?>',
                                                        type: 'POST',
                                                        dataType: 'json',
                                                        data: {
                                                            action: 'bookinn_widget_get_rooms_and_bookings',
                                                            nonce: '<?php echo wp_create_nonce('bookinn_gantt_widget'); ?>'
                                                        },
                                                        success: function(response) {
                                                            if(response.success && response.data) {
                                                                let rooms = Array.isArray(response.data.rooms) ? response.data.rooms : [];
                                                                let bookings = Array.isArray(response.data.bookings) ? response.data.bookings : [];

                                                                // Process bookings for widget
                                                                bookings = bookings.map(function(b) {
                                                                    b.id = b.id ? String(b.id) : '';
                                                                    b.roomId = b.roomId ? String(b.roomId) : '';
                                                                    b.guestName = b.guestName ? String(b.guestName) : '';
                                                                    b.status = b.status ? String(b.status) : 'confirmed';
                                                                    b.guests = b.guests ? parseInt(b.guests) : 1;
                                                                    b.totalAmount = b.totalAmount ? parseFloat(b.totalAmount) : 0;
                                                                    return b;
                                                                });

                                                                hotelData = { rooms, bookings };

                                                                // Render widget Gantt with specific IDs
                                                                if (typeof renderGanttChart === 'function') {
                                                                    renderGanttChart({
                                                                        headerRowId: 'widgetHeaderRow',
                                                                        bodyId: 'widgetGanttBody',
                                                                        tableId: 'widgetGanttTable'
                                                                    });
                                                                }
                                                            } else {
                                                                console.error('Widget Gantt: Failed to load data:', response);
                                                            }
                                                        },
                                                        error: function(xhr, status, error){
                                                            console.error('Widget Gantt AJAX error:', status, error);
                                                        }
                                                    });
                                                };

                                                // Load initial data for widget Gantt
                                                fetchWidgetRoomsAndBookings();
                                            }
                                        });
                                        </script>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Reports Tab -->
                        <div class="bookinn-tab-panel" id="tab-reports"
                             role="tabpanel" aria-labelledby="tab-reports-link" aria-hidden="true">
                            <?php $this->render_reports_tab(); ?>
                        </div>
                    </div>
                </div>
                
                <?php if ($show_sidebar): ?>
                <!-- Sidebar -->
                <div class="bookinn-sidebar">
                    <?php $this->render_sidebar(); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <?php
    }

    /**
     * Render Dashboard Tab
     */
    private function render_dashboard_tab() {
        global $wpdb;

        // Get dashboard metrics
        $metrics = $this->get_dashboard_metrics();
        ?>
        <div class="bookinn-dashboard-overview">
            <!-- Metrics Cards -->
            <div class="bookinn-metrics-grid">
                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($metrics['total_bookings']); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Total Bookings', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change positive">+<?php echo esc_html($metrics['bookings_change']); ?>%</span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-euro-sign bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value">€<?php echo esc_html(number_format($metrics['total_revenue'], 2)); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Total Revenue', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change positive">+<?php echo esc_html($metrics['revenue_change']); ?>%</span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-percent bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($metrics['occupancy_rate']); ?>%</h3>
                        <p class="bookinn-metric-label"><?php _e('Occupancy Rate', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change <?php echo $metrics['occupancy_change'] >= 0 ? 'positive' : 'negative'; ?>">
                            <?php echo $metrics['occupancy_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['occupancy_change']); ?>%
                        </span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-bed bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($metrics['available_rooms']); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Available Rooms', 'bookinn'); ?></p>
                        <span class="bookinn-metric-subtitle"><?php _e('Today', 'bookinn'); ?></span>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="bookinn-charts-section">
                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h3><?php _e('Revenue Trend', 'bookinn'); ?></h3>
                        <div class="bookinn-chart-controls">
                            <select id="bookinn-revenue-period">
                                <option value="7"><?php _e('Last 7 days', 'bookinn'); ?></option>
                                <option value="30" selected><?php _e('Last 30 days', 'bookinn'); ?></option>
                                <option value="90"><?php _e('Last 3 months', 'bookinn'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="bookinn-revenue-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h3><?php _e('Occupancy Forecast', 'bookinn'); ?></h3>
                        <div class="bookinn-chart-controls">
                            <select id="bookinn-forecast-period">
                                <option value="7"><?php _e('Next 7 days', 'bookinn'); ?></option>
                                <option value="30" selected><?php _e('Next 30 days', 'bookinn'); ?></option>
                                <option value="90"><?php _e('Next 3 months', 'bookinn'); ?></option>
                            </select>
                        </div>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="bookinn-forecast-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Bottom Row: Recent Activity and Charts -->
            <div class="bookinn-dashboard-bottom-row">
                <!-- Recent Activity (Compact) -->
                <div class="bookinn-recent-activity bookinn-compact">
                    <div class="bookinn-activity-header">
                        <h3><?php _e('Recent Activity', 'bookinn'); ?></h3>
                        <a href="#tab-bookings" class="bookinn-view-all"><?php _e('View All', 'bookinn'); ?></a>
                    </div>
                    <div class="bookinn-activity-list" id="bookinn-recent-activity">
                        <?php $this->render_recent_activity(); ?>
                    </div>
                </div>

                <!-- Booking Analytics Charts -->
                <div class="bookinn-booking-analytics">
                    <!-- Booking Days Chart -->
                    <div class="bookinn-chart-card">
                        <div class="bookinn-chart-header">
                            <h3><?php _e('Booking Days', 'bookinn'); ?></h3>
                            <select id="booking-days-period" class="bookinn-select-sm">
                                <option value="7"><?php _e('Last 7 days', 'bookinn'); ?></option>
                                <option value="30" selected><?php _e('Last 30 days', 'bookinn'); ?></option>
                                <option value="90"><?php _e('Last 90 days', 'bookinn'); ?></option>
                            </select>
                        </div>
                        <div class="bookinn-chart-container">
                            <canvas id="bookinn-booking-days-chart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <!-- Booking Rates Chart -->
                    <div class="bookinn-chart-card">
                        <div class="bookinn-chart-header">
                            <h3><?php _e('Booking Rates', 'bookinn'); ?></h3>
                            <select id="booking-rates-period" class="bookinn-select-sm">
                                <option value="7"><?php _e('Last 7 days', 'bookinn'); ?></option>
                                <option value="30" selected><?php _e('Last 30 days', 'bookinn'); ?></option>
                                <option value="90"><?php _e('Last 90 days', 'bookinn'); ?></option>
                            </select>
                        </div>
                        <div class="bookinn-chart-container">
                            <canvas id="bookinn-booking-rates-chart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Get dashboard metrics - Enhanced with more analytics
     */
    private function get_dashboard_metrics() {
        global $wpdb;

        $today = date('Y-m-d');
        $last_month = date('Y-m-d', strtotime('-30 days'));
        $prev_month = date('Y-m-d', strtotime('-60 days'));
        $last_week = date('Y-m-d', strtotime('-7 days'));
        $last_year = date('Y-m-d', strtotime('-365 days'));

        // Enhanced booking metrics
        $total_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s",
            $last_month
        ) ?: 0;

        $prev_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND created_at < %s",
            $prev_month, $last_month
        ) ?: 0;

        $weekly_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s",
            $last_week
        ) ?: 0;

        $bookings_change = $prev_bookings > 0 ? round((($total_bookings - $prev_bookings) / $prev_bookings) * 100, 1) : 0;

        // Enhanced revenue metrics
        $total_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_month
        ) ?: 0;

        $prev_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND created_at < %s AND status IN ('confirmed', 'checked_out')",
            $prev_month, $last_month
        ) ?: 0;

        $weekly_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_week
        ) ?: 0;

        $revenue_change = $prev_revenue > 0 ? round((($total_revenue - $prev_revenue) / $prev_revenue) * 100, 1) : 0;

        // Advanced occupancy calculations
        $total_rooms = BookInn_Database_Manager::get_var("SELECT COUNT(*) FROM {rooms} WHERE is_active = 1") ?: 1;
        $occupied_rooms = BookInn_Database_Manager::get_var(
            "SELECT COUNT(DISTINCT room_id) FROM {bookings} WHERE check_in_date <= %s AND check_out_date > %s AND status IN ('confirmed', 'checked_in')",
            $today, $today
        ) ?: 0;

        $occupancy_rate = round(($occupied_rooms / $total_rooms) * 100, 1);
        
        // Weekly occupancy for comparison (fixed SQL)
        $weekly_avg_occupancy = BookInn_Database_Manager::get_var(
            "SELECT AVG(daily_occupancy) FROM ("
            . " SELECT COUNT(DISTINCT b.room_id) / %d * 100 as daily_occupancy"
            . " FROM (SELECT 0 as day_offset UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) d"
            . " LEFT JOIN {bookings} b"
            . " ON b.check_in_date <= DATE_SUB(CURDATE(), INTERVAL d.day_offset DAY)"
            . " AND b.check_out_date > DATE_SUB(CURDATE(), INTERVAL d.day_offset DAY)"
            . " AND b.status IN ('confirmed', 'checked_in')"
            . " GROUP BY d.day_offset"
            . ") weekly_occupancy",
            $total_rooms
        ) ?: 0;

        $occupancy_change = round($occupancy_rate - $weekly_avg_occupancy, 1);

        // Available rooms today
        $available_rooms = $total_rooms - $occupied_rooms;

        // Additional KPIs
        $avg_daily_rate = $total_revenue > 0 && $total_bookings > 0 ? round($total_revenue / $total_bookings, 2) : 0;
        $revpar = round(($total_revenue / 30) / $total_rooms, 2); // Revenue Per Available Room
        
        // Cancellation rate
        $cancelled_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND status = 'cancelled'",
            $last_month
        ) ?: 0;
        
        $cancellation_rate = $total_bookings > 0 ? round(($cancelled_bookings / $total_bookings) * 100, 1) : 0;

        // Average length of stay
        $avg_stay_length = BookInn_Database_Manager::get_var(
            "SELECT AVG(DATEDIFF(check_out_date, check_in_date)) FROM {bookings} 
             WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_month
        ) ?: 0;

        // Guest satisfaction (mock data - replace with real survey data)
        $guest_satisfaction = 4.2; // Out of 5

        return array(
            'total_bookings' => $total_bookings,
            'bookings_change' => $bookings_change,
            'weekly_bookings' => $weekly_bookings,
            'total_revenue' => $total_revenue,
            'revenue_change' => $revenue_change,
            'weekly_revenue' => $weekly_revenue,
            'occupancy_rate' => $occupancy_rate,
            'occupancy_change' => $occupancy_change,
            'available_rooms' => $available_rooms,
            'avg_daily_rate' => $avg_daily_rate,
            'revpar' => $revpar,
            'cancellation_rate' => $cancellation_rate,
            'avg_stay_length' => round($avg_stay_length, 1),
            'guest_satisfaction' => $guest_satisfaction
        );
    }

    /**
     * Get reports metrics for the reports tab
     */
    private function get_reports_metrics() {
        global $wpdb;

        $today = date('Y-m-d');
        $last_month = date('Y-m-d', strtotime('-30 days'));
        $prev_month = date('Y-m-d', strtotime('-60 days'));
        $last_week = date('Y-m-d', strtotime('-7 days'));

        // Current period metrics (last 30 days)
        $total_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s",
            $last_month
        ) ?: 0;

        $total_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_month
        ) ?: 0;

        $cancelled_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND status = 'cancelled'",
            $last_month
        ) ?: 0;

        $avg_stay_length = BookInn_Database_Manager::get_var(
            "SELECT AVG(DATEDIFF(check_out_date, check_in_date)) FROM {bookings} 
             WHERE created_at >= %s AND status IN ('confirmed', 'checked_out')",
            $last_month
        ) ?: 0;

        // Previous period metrics (30-60 days ago)
        $prev_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND created_at < %s",
            $prev_month, $last_month
        ) ?: 0;

        $prev_revenue = BookInn_Database_Manager::get_var(
            "SELECT SUM(total_amount) FROM {bookings} WHERE created_at >= %s AND created_at < %s AND status IN ('confirmed', 'checked_out')",
            $prev_month, $last_month
        ) ?: 0;

        $prev_cancelled_bookings = BookInn_Database_Manager::get_var(
            "SELECT COUNT(*) FROM {bookings} WHERE created_at >= %s AND created_at < %s AND status = 'cancelled'",
            $prev_month, $last_month
        ) ?: 0;

        $prev_avg_stay = BookInn_Database_Manager::get_var(
            "SELECT AVG(DATEDIFF(check_out_date, check_in_date)) FROM {bookings} 
             WHERE created_at >= %s AND created_at < %s AND status IN ('confirmed', 'checked_out')",
            $prev_month, $last_month
        ) ?: 0;

        // Calculate changes
        $bookings_change = $prev_bookings > 0 ? round((($total_bookings - $prev_bookings) / $prev_bookings) * 100, 1) : 0;
        $revenue_change = $prev_revenue > 0 ? round((($total_revenue - $prev_revenue) / $prev_revenue) * 100, 1) : 0;

        // Occupancy calculations
        $total_rooms = BookInn_Database_Manager::get_var("SELECT COUNT(*) FROM {rooms} WHERE is_active = 1") ?: 1;
        $occupied_rooms = BookInn_Database_Manager::get_var(
            "SELECT COUNT(DISTINCT room_id) FROM {bookings} WHERE check_in_date <= %s AND check_out_date > %s AND status IN ('confirmed', 'checked_in')",
            $today, $today
        ) ?: 0;

        $occupancy_rate = round(($occupied_rooms / $total_rooms) * 100, 1);

        // Previous period occupancy (simplified calculation)
        $prev_occupancy_rate = BookInn_Database_Manager::get_var(
            "SELECT AVG(daily_occupancy) FROM ("
            . " SELECT COUNT(DISTINCT b.room_id) / %d * 100 as daily_occupancy"
            . " FROM (SELECT 0 as day_offset UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6) d"
            . " LEFT JOIN {bookings} b"
            . " ON b.check_in_date <= DATE_SUB(%s, INTERVAL d.day_offset DAY)"
            . " AND b.check_out_date > DATE_SUB(%s, INTERVAL d.day_offset DAY)"
            . " AND b.status IN ('confirmed', 'checked_in')"
            . " GROUP BY d.day_offset"
            . ") prev_occupancy",
            $total_rooms, $prev_month, $prev_month
        ) ?: 0;

        $occupancy_change = round($occupancy_rate - $prev_occupancy_rate, 1);

        // ADR (Average Daily Rate) calculations
        $avg_daily_rate = $total_bookings > 0 ? round($total_revenue / $total_bookings, 2) : 0;
        $prev_avg_daily_rate = $prev_bookings > 0 ? round($prev_revenue / $prev_bookings, 2) : 0;
        $adr_change = $prev_avg_daily_rate > 0 ? round((($avg_daily_rate - $prev_avg_daily_rate) / $prev_avg_daily_rate) * 100, 1) : 0;

        // RevPAR (Revenue Per Available Room) calculations
        $revpar = round(($total_revenue / 30) / $total_rooms, 2);
        $prev_revpar = round(($prev_revenue / 30) / $total_rooms, 2);
        $revpar_change = $prev_revpar > 0 ? round((($revpar - $prev_revpar) / $prev_revpar) * 100, 1) : 0;

        // Cancellation rates
        $cancellation_rate = $total_bookings > 0 ? round(($cancelled_bookings / $total_bookings) * 100, 1) : 0;
        $prev_cancellation_rate = $prev_bookings > 0 ? round(($prev_cancelled_bookings / $prev_bookings) * 100, 1) : 0;
        $cancellation_change = round($cancellation_rate - $prev_cancellation_rate, 1);

        // Stay length changes
        $stay_change = $prev_avg_stay > 0 ? round((($avg_stay_length - $prev_avg_stay) / $prev_avg_stay) * 100, 1) : 0;

        return array(
            'total_bookings' => $total_bookings,
            'prev_bookings' => $prev_bookings,
            'bookings_change' => $bookings_change,
            'total_revenue' => $total_revenue,
            'prev_revenue' => $prev_revenue,
            'revenue_change' => $revenue_change,
            'occupancy_rate' => $occupancy_rate,
            'occupancy_change' => $occupancy_change,
            'avg_daily_rate' => $avg_daily_rate,
            'adr_change' => $adr_change,
            'revpar' => $revpar,
            'revpar_change' => $revpar_change,
            'cancellation_rate' => $cancellation_rate,
            'prev_cancellation_rate' => $prev_cancellation_rate,
            'cancellation_change' => $cancellation_change,
            'avg_stay_length' => round($avg_stay_length, 1),
            'prev_avg_stay' => round($prev_avg_stay, 1),
            'stay_change' => $stay_change
        );
    }

    /**
     * Render recent activity
     */
    private function render_recent_activity() {
        $recent_bookings = BookInn_Database_Manager::get_results(
            "SELECT b.*, g.first_name as guest_name, g.email as guest_email, r.room_number
             FROM {bookings} b
             LEFT JOIN {guests} g ON b.guest_id = g.id
             LEFT JOIN {rooms} r ON b.room_id = r.id
             ORDER BY b.created_at DESC
             LIMIT 5"
        );

        if (empty($recent_bookings)) {
            echo '<p class="bookinn-no-data">' . __('No recent activity', 'bookinn') . '</p>';
            return;
        }

        foreach ($recent_bookings as $booking) {
            $guest_name = $booking->guest_name;
            $status_class = 'bookinn-status-' . $booking->status;
            ?>
            <div class="bookinn-activity-item">
                <div class="bookinn-activity-icon">
                    <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                </div>
                <div class="bookinn-activity-content">
                    <p class="bookinn-activity-title">
                        <?php printf(__('New booking by %s', 'bookinn'), esc_html($guest_name)); ?>
                    </p>
                    <p class="bookinn-activity-details">
                        <?php printf(__('Room %s • %s to %s', 'bookinn'),
                            esc_html($booking->room_number),
                            esc_html(date('M j', strtotime($booking->check_in_date))),
                            esc_html(date('M j', strtotime($booking->check_out_date)))
                        ); ?>
                    </p>
                    <span class="bookinn-activity-status <?php echo esc_attr($status_class); ?>">
                        <?php echo esc_html(ucfirst($booking->status)); ?>
                    </span>
                </div>
                <div class="bookinn-activity-time">
                    <?php echo esc_html(human_time_diff(strtotime($booking->created_at), current_time('timestamp'))); ?> <?php _e('ago', 'bookinn'); ?>
                </div>
            </div>
            <?php
        }
    }

    /**
     * Render Bookings Tab
     */
    private function render_bookings_tab() {
        ?>
        <div class="bookinn-bookings-container">
            <!-- Booking Actions Header -->
            <div class="bookinn-section-header">
                <h3><?php _e('Bookings Management', 'bookinn'); ?></h3>
                <div class="bookinn-section-actions">
                    <button class="bookinn-btn bookinn-btn-primary" id="bookinn-add-booking">
                        <i class="fa-solid fa-plus bookinn-fa"></i>
                        <?php _e('New Booking', 'bookinn'); ?>
                    </button>
                    <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-booking-filters">
                        <i class="fa-solid fa-filter bookinn-fa"></i>
                        <?php _e('Filters', 'bookinn'); ?>
                    </button>
                    <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-refresh-bookings">
                        <i class="fa-solid fa-rotate bookinn-fa"></i>
                        <?php _e('Refresh', 'bookinn'); ?>
                    </button>
                </div>
            </div>

            <!-- Booking Filters (Hidden by default) -->
            <div id="bookinn-list-filters" class="bookinn-list-filters" style="display: none;">
                <div class="bookinn-form-grid">
                    <div class="bookinn-form-group">
                        <label for="filter-status"><?php _e('Status', 'bookinn'); ?></label>
                        <select id="filter-status" class="bookinn-select">
                            <option value=""><?php _e('All Statuses', 'bookinn'); ?></option>
                            <option value="confirmed"><?php _e('Confirmed', 'bookinn'); ?></option>
                            <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                            <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                            <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                            <option value="checked_out"><?php _e('Checked Out', 'bookinn'); ?></option>
                        </select>
                    </div>
                    <div class="bookinn-form-group">
                        <label for="filter-date-from"><?php _e('From Date', 'bookinn'); ?></label>
                        <input type="date" id="filter-date-from" class="bookinn-input">
                    </div>
                    <div class="bookinn-form-group">
                        <label for="filter-date-to"><?php _e('To Date', 'bookinn'); ?></label>
                        <input type="date" id="filter-date-to" class="bookinn-input">
                    </div>
                    <div class="bookinn-form-group">
                        <label for="filter-room"><?php _e('Room', 'bookinn'); ?></label>
                        <select id="filter-room" class="bookinn-select">
                            <option value=""><?php _e('All Rooms', 'bookinn'); ?></option>
                            <!-- Room options will be populated dynamically -->
                        </select>
                    </div>
                </div>
                <div class="bookinn-filter-actions">
                    <button id="apply-filters" class="bookinn-btn bookinn-btn-primary">
                        <i class="fa-solid fa-filter bookinn-fa"></i>
                        <?php _e('Apply Filters', 'bookinn'); ?>
                    </button>
                    <button id="clear-filters" class="bookinn-btn bookinn-btn-secondary">
                        <i class="fa-solid fa-rotate bookinn-fa"></i>
                        <?php _e('Clear Filters', 'bookinn'); ?>
                    </button>
                </div>
            </div>

            <!-- Bookings List -->
            <div class="bookinn-bookings-list">
                <div class="bookinn-list-filters" id="bookinn-list-filters" style="display: none;">
                    <div class="bookinn-filter-row">
                        <select id="filter-status" class="bookinn-select">
                            <option value=""><?php _e('All Statuses', 'bookinn'); ?></option>
                            <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                            <option value="confirmed"><?php _e('Confirmed', 'bookinn'); ?></option>
                            <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                            <option value="checked_out"><?php _e('Checked Out', 'bookinn'); ?></option>
                            <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                        </select>

                        <input type="date" id="filter-date-from" class="bookinn-input" placeholder="<?php _e('From Date', 'bookinn'); ?>">
                        <input type="date" id="filter-date-to" class="bookinn-input" placeholder="<?php _e('To Date', 'bookinn'); ?>">

                        <select id="filter-room" class="bookinn-select">
                            <option value=""><?php _e('All Rooms', 'bookinn'); ?></option>
                            <!-- Rooms will be loaded via AJAX -->
                        </select>

                        <button class="bookinn-btn bookinn-btn-primary" id="apply-filters">
                            <?php _e('Apply', 'bookinn'); ?>
                        </button>
                    </div>
                </div>

                <div class="bookinn-table-container">
                    <table class="bookinn-table" id="bookinn-bookings-table">
                        <thead>
                            <tr>
                                <th><?php _e('Booking ID', 'bookinn'); ?></th>
                                <th><?php _e('Guest', 'bookinn'); ?></th>
                                <th><?php _e('Room', 'bookinn'); ?></th>
                                <th><?php _e('Check-in', 'bookinn'); ?></th>
                                <th><?php _e('Check-out', 'bookinn'); ?></th>
                                <th><?php _e('Status', 'bookinn'); ?></th>
                                <th><?php _e('Total', 'bookinn'); ?></th>
                                <th><?php _e('Actions', 'bookinn'); ?></th>
                            </tr>
                        </thead>
                        <tbody id="bookings-table-body">
                            <?php $this->render_bookings_table_rows(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render bookings table rows
     */
    private function render_bookings_table_rows() {
        $bookings = BookInn_Database_Manager::get_results("
            SELECT b.*, g.first_name as guest_name, g.email as guest_email, r.room_number, rt.name as room_type
            FROM {bookings} b
            LEFT JOIN {guests} g ON b.guest_id = g.id
            LEFT JOIN {rooms} r ON b.room_id = r.id
            LEFT JOIN {room_types} rt ON r.room_type_id = rt.id
            ORDER BY b.created_at DESC
            LIMIT 20
        ");

        if (empty($bookings)) {
            echo '<tr><td colspan="8" class="bookinn-no-data">' . __('No bookings found', 'bookinn') . '</td></tr>';
            return;
        }

        foreach ($bookings as $booking) {
            $guest_name = $booking->guest_name;
            $status_class = 'bookinn-status-' . $booking->status;
            ?>
            <tr data-booking-id="<?php echo esc_attr($booking->id); ?>">
                <td>
                    <strong>#<?php echo esc_html($booking->id); ?></strong>
                </td>
                <td>
                    <div class="bookinn-guest-info">
                        <strong><?php echo esc_html($guest_name); ?></strong>
                        <small><?php echo esc_html($booking->guest_email); ?></small>
                    </div>
                </td>
                <td>
                    <div class="bookinn-room-info">
                        <strong><?php echo esc_html($booking->room_number); ?></strong>
                        <small><?php echo esc_html(ucfirst($booking->room_type)); ?></small>
                    </div>
                </td>
                <td><?php echo esc_html(date('M j, Y', strtotime($booking->check_in_date))); ?></td>
                <td><?php echo esc_html(date('M j, Y', strtotime($booking->check_out_date))); ?></td>
                <td>
                    <span class="bookinn-status-badge <?php echo esc_attr($status_class); ?>">
                        <?php echo esc_html(ucfirst(str_replace('_', ' ', $booking->status))); ?>
                    </span>
                </td>
                <td>
                    <strong>€<?php echo esc_html(number_format($booking->total_amount, 2)); ?></strong>
                </td>
                <td>
                    <div class="bookinn-actions">
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-view-booking" data-booking-id="<?php echo esc_attr($booking->id); ?>">
                            <?php _e('View', 'bookinn'); ?>
                        </button>
                         <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-booking" data-booking-id="<?php echo esc_attr($booking->id); ?>">
                            <?php _e('Edit', 'bookinn'); ?>
                        </button>
                        <?php if ($booking->status === 'pending'): ?>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-primary bookinn-confirm-booking" data-booking-id="<?php echo esc_attr($booking->id); ?>">
                            <?php _e('Confirm', 'bookinn'); ?>
                        </button>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
            <?php
        }
    }

    /**
     * Render Rooms Tab
     */
    private function render_rooms_tab() {
        ?>
        <div class="bookinn-rooms-container">
            <!-- Room Sub-tabs -->
            <div class="bookinn-sub-tabs">
                <div class="bookinn-sub-tab-nav">
                    <a href="#rooms-list" class="bookinn-sub-tab-link active" data-tab="rooms-list">
                        <?php _e('Rooms List', 'bookinn'); ?>
                    </a>
                    <a href="#room-types" class="bookinn-sub-tab-link" data-tab="room-types">
                        <?php _e('Room Types', 'bookinn'); ?>
                    </a>
                </div>

                <!-- Rooms List Sub-tab -->
                <div class="bookinn-sub-tab-panel active" id="rooms-list">
                    <!-- Rooms Header -->
                    <div class="bookinn-section-header">
                        <h3><?php _e('Rooms Management', 'bookinn'); ?></h3>
                        <div class="bookinn-section-actions">
                            <button class="bookinn-btn bookinn-btn-primary" id="bookinn-add-room">
                                <i class="fa-solid fa-plus bookinn-fa"></i>
                                <?php _e('Add Room', 'bookinn'); ?>
                            </button>
                            <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-room-filters">
                                <i class="fa-solid fa-filter bookinn-fa"></i>
                                <?php _e('Filters', 'bookinn'); ?>
                            </button>
                            <button class="bookinn-btn bookinn-btn-secondary" id="bookinn-refresh-rooms">
                                <i class="fa-solid fa-rotate bookinn-fa"></i>
                                <?php _e('Refresh', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Room Filters (Hidden by default) -->
                    <div id="bookinn-room-list-filters" class="bookinn-list-filters" style="display: none;">
                        <div class="bookinn-form-grid">
                            <div class="bookinn-form-group">
                                <label for="rooms-status-filter"><?php _e('Status', 'bookinn'); ?></label>
                                <select id="rooms-status-filter" class="bookinn-select">
                                    <option value=""><?php _e('All Status', 'bookinn'); ?></option>
                                    <option value="available"><?php _e('Available', 'bookinn'); ?></option>
                                    <option value="occupied"><?php _e('Occupied', 'bookinn'); ?></option>
                                    <option value="maintenance"><?php _e('Maintenance', 'bookinn'); ?></option>
                                    <option value="cleaning"><?php _e('Cleaning', 'bookinn'); ?></option>
                                    <option value="out_of_order"><?php _e('Out of Order', 'bookinn'); ?></option>
                                </select>
                            </div>
                            <div class="bookinn-form-group">
                                <label for="rooms-number-filter"><?php _e('Room Number', 'bookinn'); ?></label>
                                <input type="text" id="rooms-number-filter" class="bookinn-input" placeholder="<?php _e('Search by room number', 'bookinn'); ?>">
                            </div>
                            <div class="bookinn-form-group">
                                <label for="rooms-type-filter"><?php _e('Room Type', 'bookinn'); ?></label>
                                <select id="rooms-type-filter" class="bookinn-select">
                                    <option value=""><?php _e('All Types', 'bookinn'); ?></option>
                                    <!-- Room types will be loaded via AJAX -->
                                </select>
                            </div>
                        </div>
                        <div class="bookinn-filter-actions">
                            <button id="apply-room-filters" class="bookinn-btn bookinn-btn-primary">
                                <i class="fa-solid fa-filter bookinn-fa"></i>
                                <?php _e('Apply Filters', 'bookinn'); ?>
                            </button>
                            <button id="clear-room-filters" class="bookinn-btn bookinn-btn-secondary">
                                <i class="fa-solid fa-rotate bookinn-fa"></i>
                                <?php _e('Clear Filters', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Rooms Table -->
                    <div class="bookinn-table-container">
                        <table class="bookinn-table" id="bookinn-rooms-table">
                            <thead>
                                <tr>
                                    <th><?php _e('Room Number', 'bookinn'); ?></th>
                                    <th><?php _e('Type', 'bookinn'); ?></th>
                                    <th><?php _e('Floor', 'bookinn'); ?></th>
                                    <th><?php _e('Status', 'bookinn'); ?></th>
                                    <th><?php _e('Active', 'bookinn'); ?></th>
                                    <th><?php _e('Price/Night', 'bookinn'); ?></th>
                                    <th><?php _e('Actions', 'bookinn'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $this->render_rooms_table_rows(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Room Types Sub-tab -->
                <div class="bookinn-sub-tab-panel" id="room-types">
                    <!-- Room Types Header -->
                    <div class="bookinn-section-header">
                        <h3><?php _e('Room Types Management', 'bookinn'); ?></h3>
                        <div class="bookinn-section-actions">
                            <button class="bookinn-btn bookinn-btn-primary" id="bookinn-add-room-type">
                                <?php _e('Add Room Type', 'bookinn'); ?>
                            </button>
                        </div>
                    </div>

                    <!-- Room Types Table -->
                    <div class="bookinn-table-container">
                        <table class="bookinn-table" id="bookinn-room-types-table">
                            <thead>
                                <tr>
                                    <th><?php _e('Type Name', 'bookinn'); ?></th>
                                    <th><?php _e('Description', 'bookinn'); ?></th>
                                    <th><?php _e('Base Price', 'bookinn'); ?></th>
                                    <th><?php _e('Max Guests', 'bookinn'); ?></th>
                                    <th><?php _e('Actions', 'bookinn'); ?></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $this->render_room_types_table_rows(); ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Room Editor Modal - Enhanced Accessibility -->
            <div class="bookinn-modal" 
                 id="bookinn-room-modal" 
                 role="dialog" 
                 aria-labelledby="room-modal-title" 
                 aria-hidden="true"
                 tabindex="-1"
                 style="display: none;">
                <div class="bookinn-modal-content" role="document">
                    <div class="bookinn-modal-header">
                        <h4 id="room-modal-title" tabindex="0"><?php _e('Edit Room', 'bookinn'); ?></h4>
                        <button class="bookinn-close-modal" 
                                id="close-room-modal"
                                aria-label="<?php _e('Close modal', 'bookinn'); ?>"
                                type="button">×</button>
                    </div>
                    <div class="bookinn-modal-body">
                        <form id="room-form" aria-labelledby="room-modal-title">
                            <div class="bookinn-form-grid">
                                <div class="bookinn-form-group">
                                    <label for="room-number"><?php _e('Room Number', 'bookinn'); ?></label>
                                    <input type="text" 
                                           id="room-number" 
                                           class="bookinn-input" 
                                           aria-describedby="room-number-help"
                                           required>
                                    <small id="room-number-help" class="bookinn-help-text">
                                        <?php _e('Enter the room number (e.g., 101, 202)', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="room-type"><?php _e('Room Type', 'bookinn'); ?></label>
                                    <select id="room-type" 
                                            class="bookinn-select" 
                                            aria-describedby="room-type-help"
                                            required>
                                        <?php $this->render_room_type_options(); ?>
                                    </select>
                                    <small id="room-type-help" class="bookinn-help-text">
                                        <?php _e('Select the room category', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="room-floor"><?php _e('Floor', 'bookinn'); ?></label>
                                    <input type="number" 
                                           id="room-floor" 
                                           class="bookinn-input" 
                                           aria-describedby="room-floor-help"
                                           min="0" 
                                           max="50">
                                    <small id="room-floor-help" class="bookinn-help-text">
                                        <?php _e('Floor number (0 for ground floor)', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label><?php _e('Status', 'bookinn'); ?></label>
                                    <select id="room-status" class="bookinn-select">
                                        <option value="available"><?php _e('Available', 'bookinn'); ?></option>
                                        <option value="occupied"><?php _e('Occupied', 'bookinn'); ?></option>
                                        <option value="maintenance"><?php _e('Maintenance', 'bookinn'); ?></option>
                                        <option value="out_of_order"><?php _e('Out of Order', 'bookinn'); ?></option>
                                    </select>
                                </div>
                            </div>
                            <div class="bookinn-form-group">
                                <label><?php _e('Room Name (Optional)', 'bookinn'); ?></label>
                                <input type="text" id="room-name" class="bookinn-input">
                            </div>
                            <input type="hidden" id="room-id" value="">
                        </form>
                    </div>
                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary bookinn-close-modal"><?php _e('Cancel', 'bookinn'); ?></button>
                        <button class="bookinn-btn bookinn-btn-primary" id="save-room"><?php _e('Save Room', 'bookinn'); ?></button>
                    </div>
                </div>
            </div>

            <!-- Room Type Editor Modal - Enhanced Accessibility -->
            <div class="bookinn-modal" 
                 id="bookinn-room-type-modal" 
                 role="dialog" 
                 aria-labelledby="room-type-modal-title" 
                 aria-hidden="true"
                 tabindex="-1"
                 style="display: none;">
                <div class="bookinn-modal-content" role="document">
                    <div class="bookinn-modal-header">
                        <h4 id="room-type-modal-title" tabindex="0"><?php _e('Edit Room Type', 'bookinn'); ?></h4>
                        <button class="bookinn-close-modal" 
                                id="close-room-type-modal"
                                aria-label="<?php _e('Close modal', 'bookinn'); ?>"
                                type="button">×</button>
                    </div>
                    <div class="bookinn-modal-body">
                        <form id="room-type-form" aria-labelledby="room-type-modal-title">
                            <div class="bookinn-form-grid">
                                <div class="bookinn-form-group">
                                    <label for="room-type-name"><?php _e('Type Name', 'bookinn'); ?></label>
                                    <input type="text" 
                                           id="room-type-name" 
                                           class="bookinn-input" 
                                           aria-describedby="room-type-name-help"
                                           required>
                                    <small id="room-type-name-help" class="bookinn-help-text">
                                        <?php _e('Name of the room category (e.g., Standard, Deluxe)', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="room-type-price"><?php _e('Base Price (€)', 'bookinn'); ?></label>
                                    <input type="number" 
                                           id="room-type-price" 
                                           class="bookinn-input" 
                                           aria-describedby="room-type-price-help"
                                           min="0" 
                                           step="0.01" 
                                           required>
                                    <small id="room-type-price-help" class="bookinn-help-text">
                                        <?php _e('Base price per night in euros', 'bookinn'); ?>
                                    </small>
                                </div>
                                <div class="bookinn-form-group">
                                    <label for="room-type-max-guests"><?php _e('Max Guests', 'bookinn'); ?></label>
                                    <input type="number" 
                                           id="room-type-max-guests" 
                                           class="bookinn-input" 
                                           aria-describedby="room-type-max-guests-help"
                                           min="1" 
                                           max="10" 
                                           required>
                                    <small id="room-type-max-guests-help" class="bookinn-help-text">
                                        <?php _e('Maximum number of guests allowed', 'bookinn'); ?>
                                    </small>
                                </div>
                            </div>
                            <div class="bookinn-form-group">
                                <label for="room-type-description"><?php _e('Description', 'bookinn'); ?></label>
                                <textarea id="room-type-description" class="bookinn-textarea" rows="3"></textarea>
                            </div>
                            <input type="hidden" id="room-type-id" value="">
                        </form>
                    </div>
                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary bookinn-close-modal"><?php _e('Cancel', 'bookinn'); ?></button>
                        <button class="bookinn-btn bookinn-btn-primary" id="save-room-type"><?php _e('Save Room Type', 'bookinn'); ?></button>
                    </div>
                </div>
            </div>

            <!-- New Booking Modal -->
            <div class="bookinn-modal" 
                 id="bookinn-new-booking-modal" 
                 role="dialog" 
                 aria-labelledby="new-booking-modal-title" 
                 aria-hidden="true"
                 tabindex="-1"
                 style="display: none;">
                <div class="bookinn-modal-content bookinn-modal-lg" role="document">
                    <div class="bookinn-modal-header">
                        <h4 id="new-booking-modal-title" tabindex="0"><?php _e('New Booking', 'bookinn'); ?></h4>
                        <button class="bookinn-close-modal" 
                                aria-label="<?php _e('Close modal', 'bookinn'); ?>"
                                type="button">×</button>
                    </div>
                    <div class="bookinn-modal-body">
                        <form id="new-booking-form" aria-labelledby="new-booking-modal-title">
                            <div class="bookinn-form-section">
                                <h5><i class="fas fa-calendar"></i> <?php _e('Booking Details', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid">
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-checkin"><?php _e('Check-in Date', 'bookinn'); ?> *</label>
                                        <input type="date" id="new-booking-checkin" name="check_in_date" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-checkout"><?php _e('Check-out Date', 'bookinn'); ?> *</label>
                                        <input type="date" id="new-booking-checkout" name="check_out_date" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-adults"><?php _e('Adults', 'bookinn'); ?> *</label>
                                        <select id="new-booking-adults" name="adults" class="bookinn-select" required>
                                            <option value="1">1</option>
                                            <option value="2" selected>2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                        </select>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-children"><?php _e('Children', 'bookinn'); ?></label>
                                        <select id="new-booking-children" name="children" class="bookinn-select">
                                            <option value="0" selected>0</option>
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bookinn-form-section">
                                <h5><i class="fas fa-user"></i> <?php _e('Guest Information', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid">
                                    <div class="bookinn-form-group">
                                        <label for="new-guest-first-name"><?php _e('First Name', 'bookinn'); ?> *</label>
                                        <input type="text" id="new-guest-first-name" name="guest_first_name" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-guest-last-name"><?php _e('Last Name', 'bookinn'); ?> *</label>
                                        <input type="text" id="new-guest-last-name" name="guest_last_name" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-guest-email"><?php _e('Email', 'bookinn'); ?> *</label>
                                        <input type="email" id="new-guest-email" name="guest_email" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-guest-phone"><?php _e('Phone', 'bookinn'); ?> *</label>
                                        <input type="tel" id="new-guest-phone" name="guest_phone" class="bookinn-input" required>
                                    </div>
                                </div>
                            </div>

                            <div class="bookinn-form-section">
                                <h5><i class="fas fa-bed"></i> <?php _e('Room Selection', 'bookinn'); ?></h5>
                                <div class="bookinn-form-group">
                                    <label for="new-booking-room"><?php _e('Room', 'bookinn'); ?> *</label>
                                    <select id="new-booking-room" name="room_id" class="bookinn-select" required>
                                        <option value=""><?php _e('Select Room', 'bookinn'); ?></option>
                                        <!-- Rooms will be loaded via AJAX -->
                                    </select>
                                </div>
                                <div class="bookinn-form-group full-width">
                                    <label for="new-booking-notes"><?php _e('Special Requests', 'bookinn'); ?></label>
                                    <textarea id="new-booking-notes" name="notes" class="bookinn-textarea" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="bookinn-form-section">
                                <h5><i class="fas fa-credit-card"></i> <?php _e('Payment', 'bookinn'); ?></h5>
                                <div class="bookinn-form-grid">
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-total"><?php _e('Total Amount (€)', 'bookinn'); ?> *</label>
                                        <input type="number" id="new-booking-total" name="total_amount" class="bookinn-input" step="0.01" min="0" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-payment-method"><?php _e('Payment Method', 'bookinn'); ?> *</label>
                                        <select id="new-booking-payment-method" name="payment_method" class="bookinn-select" required>
                                            <option value=""><?php _e('Select Payment Method', 'bookinn'); ?></option>
                                            <option value="credit_card"><?php _e('Credit Card', 'bookinn'); ?></option>
                                            <option value="cash"><?php _e('Cash', 'bookinn'); ?></option>
                                            <option value="bank_transfer"><?php _e('Bank Transfer', 'bookinn'); ?></option>
                                        </select>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-status"><?php _e('Status', 'bookinn'); ?> *</label>
                                        <select id="new-booking-status" name="status" class="bookinn-select" required>
                                            <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                                            <option value="confirmed" selected><?php _e('Confirmed', 'bookinn'); ?></option>
                                            <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                                            <option value="checked_out"><?php _e('Checked Out', 'bookinn'); ?></option>
                                            <option value="cancelled"><?php _e('Cancelled', 'bookinn'); ?></option>
                                            <option value="no_show"><?php _e('No Show', 'bookinn'); ?></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary bookinn-close-modal"><?php _e('Cancel', 'bookinn'); ?></button>
                        <button class="bookinn-btn bookinn-btn-success" id="save-new-booking"><?php _e('Create Booking', 'bookinn'); ?></button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render rooms table rows
     */
    private function render_rooms_table_rows() {
        $rooms = BookInn_Database_Manager::get_results("
            SELECT r.*, rt.name as room_type_name, rt.base_price, rt.max_guests
            FROM {rooms} r
            LEFT JOIN {room_types} rt ON r.room_type_id = rt.id
            WHERE r.is_active = 1
            ORDER BY r.room_number
        ");

        if (empty($rooms)) {
            echo '<tr><td colspan="6" class="bookinn-no-data">' . __('No rooms found. Add your first room to get started.', 'bookinn') . '</td></tr>';
            return;
        }

        foreach ($rooms as $room) {
            $status_class = 'bookinn-status-' . $room->status;
            $status_text = ucfirst(str_replace('_', ' ', $room->status));
            ?>
            <tr data-room-id="<?php echo esc_attr($room->id); ?>" data-room-number="<?php echo esc_attr($room->room_number); ?>" data-room-type="<?php echo esc_attr($room->room_type_name); ?>" data-base-price="<?php echo esc_attr($room->base_price); ?>">
                <td>
                    <strong><?php echo esc_html($room->room_number); ?></strong>
                    <?php if ($room->name): ?>
                        <br><small><?php echo esc_html($room->name); ?></small>
                    <?php endif; ?>
                </td>
                <td><?php echo esc_html($room->room_type_name); ?></td>
                <td><?php echo esc_html($room->floor ?: '-'); ?></td>
                <td>
                    <span class="bookinn-status-badge <?php echo esc_attr($status_class); ?>">
                        <?php echo esc_html($status_text); ?>
                    </span>
                </td>
                <td>€<?php echo esc_html(number_format($room->base_price, 2)); ?></td>
                <td>
                    <div class="bookinn-actions">
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-room" data-room-id="<?php echo esc_attr($room->id); ?>">
                            <?php _e('Edit', 'bookinn'); ?>
                        </button>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-view-room-calendar" data-room-id="<?php echo esc_attr($room->id); ?>">
                            <?php _e('Calendar', 'bookinn'); ?>
                        </button>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-success bookinn-quick-book-room" 
                                data-room-id="<?php echo esc_attr($room->id); ?>" 
                                data-room-number="<?php echo esc_attr($room->room_number); ?>"
                                data-room-type="<?php echo esc_attr($room->room_type_name); ?>">
                            <?php _e('Quick Book', 'bookinn'); ?>
                        </button>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bookinn-delete-room" data-room-id="<?php echo esc_attr($room->id); ?>">
                            <?php _e('Delete', 'bookinn'); ?>
                        </button>
                    </div>
                </td>
            </tr>
            <?php
        }
    }

    /**
     * Render room types table rows
     */
    private function render_room_types_table_rows() {
        $room_types = BookInn_Database_Manager::get_results("
            SELECT rt.*, COUNT(r.id) as room_count
            FROM {room_types} rt
            LEFT JOIN {rooms} r ON r.room_type_id = rt.id AND r.is_active = 1
            WHERE rt.is_active = 1
            GROUP BY rt.id
            ORDER BY rt.name
        ");

        if (empty($room_types)) {
            echo '<tr><td colspan="5" class="bookinn-no-data">' . __('No room types found. Add your first room type to get started.', 'bookinn') . '</td></tr>';
            return;
        }

        foreach ($room_types as $type) {
            ?>
            <tr data-room-type-id="<?php echo esc_attr($type->id); ?>">
                <td>
                    <strong><?php echo esc_html($type->name); ?></strong>
                    <br><small><?php printf(__('%d rooms', 'bookinn'), $type->room_count); ?></small>
                </td>
                <td><?php echo esc_html($type->description ?: '-'); ?></td>
                <td>€<?php echo esc_html(number_format($type->base_price, 2)); ?></td>
                <td><?php echo esc_html($type->max_guests); ?></td>
                <td>
                    <div class="bookinn-actions">
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-room-type" data-room-type-id="<?php echo esc_attr($type->id); ?>">
                            <?php _e('Edit', 'bookinn'); ?>
                        </button>
                        <?php if ($type->room_count == 0): ?>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bookinn-delete-room-type" data-room-type-id="<?php echo esc_attr($type->id); ?>">
                            <?php _e('Delete', 'bookinn'); ?>
                        </button>
                        <?php else: ?>
                        <span class="bookinn-text-muted" title="<?php _e('Cannot delete room type with assigned rooms', 'bookinn'); ?>">
                            <?php printf(__('(%d rooms)', 'bookinn'), $type->room_count); ?>
                        </span>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
            <?php
        }
    }

    /**
     * Render rooms grid
     */
    private function render_rooms_grid() {
        $rooms = BookInn_Database_Manager::get_results("
            SELECT r.*, rt.name as room_type_name, rt.base_price, rt.max_guests
            FROM {rooms} r
            LEFT JOIN {room_types} rt ON r.room_type_id = rt.id
            WHERE r.is_active = 1
            ORDER BY r.room_number
        ");

        if (empty($rooms)) {
            echo '<div class="bookinn-no-data">' . __('No rooms found. Add your first room to get started.', 'bookinn') . '</div>';
            return;
        }

        foreach ($rooms as $room) {
            $status_class = 'bookinn-room-' . $room->status;
            $status_text = ucfirst(str_replace('_', ' ', $room->status));
            ?>
            <div class="bookinn-room-card <?php echo esc_attr($status_class); ?>" data-room-id="<?php echo esc_attr($room->id); ?>">
                <div class="bookinn-room-header">
                    <h4 class="bookinn-room-number"><?php echo esc_html($room->room_number); ?></h4>
                    <span class="bookinn-room-status"><?php echo esc_html($status_text); ?></span>
                </div>

                <div class="bookinn-room-info">
                    <p class="bookinn-room-type"><?php echo esc_html($room->room_type_name); ?></p>
                    <?php if ($room->name): ?>
                    <p class="bookinn-room-name"><?php echo esc_html($room->name); ?></p>
                    <?php endif; ?>
                    <p class="bookinn-room-details">
                        <?php 
                        $floor = isset($room->floor) && $room->floor ? $room->floor : 0;
                        $max_guests = isset($room->max_guests) && $room->max_guests ? $room->max_guests : 2;
                        printf(__('Floor %d • Max %d guests', 'bookinn'), $floor, $max_guests); 
                        ?>
                    </p>
                    <p class="bookinn-room-price">€<?php echo esc_html(number_format($room->base_price, 2)); ?>/night</p>
                </div>

                <div class="bookinn-room-actions">
                    <button class="bookinn-btn-icon bookinn-edit-room" data-room-id="<?php echo esc_attr($room->id); ?>" title="<?php _e('Edit Room', 'bookinn'); ?>">
                        <i class="fa-solid fa-pen-to-square bookinn-fa"></i>
                    </button>
                    <button class="bookinn-btn-icon bookinn-room-calendar" data-room-id="<?php echo esc_attr($room->id); ?>" title="<?php _e('View Calendar', 'bookinn'); ?>">
                        <i class="fa-solid fa-calendar-days bookinn-fa"></i>
                    </button>
                    <?php if ($room->status === 'available'): ?>
                    <button class="bookinn-btn-icon bookinn-quick-book" data-room-id="<?php echo esc_attr($room->id); ?>" title="<?php _e('Quick Book', 'bookinn'); ?>">
                        <i class="fa-solid fa-plus bookinn-fa"></i>
                    </button>
                    <?php endif; ?>
                </div>
            </div>
            <?php
        }
    }

    /**
     * Render room type options
     */
    private function render_room_type_options() {
        $room_types = BookInn_Database_Manager::get_results("
            SELECT name as room_type, base_price
            FROM {room_types}
            WHERE is_active = 1
            ORDER BY name
        ");

        foreach ($room_types as $type) {
            printf(
                '<option value="%s">%s (€%s/night)</option>',
                esc_attr($type->room_type),
                esc_html(ucfirst($type->room_type)),
                esc_html(number_format($type->base_price, 2))
            );
        }
    }

    /**
     * Render Calendar Tab
     */
    private function render_calendar_tab() {
        ?>
        <div class="bookinn-section-header">
             <h3><?php _e('Booking Calendar', 'bookinn'); ?></h3>
        </div>
        <div class="bookinn-calendar-container">
            <!-- Calendar Header -->
            

            <!-- Calendar Filters -->
            <div class="bookinn-calendar-filters">
                <div class="filter-group">
                    <label><?php _e('Room Filter:', 'bookinn'); ?></label>
                    <select id="room-filter" class="bookinn-select">
                        <option value=""><?php _e('All Rooms', 'bookinn'); ?></option>
                        <?php $this->render_room_options(); ?>
                    </select>
                </div>

                <div class="filter-group">
                    <label><?php _e('Room Type Filter:', 'bookinn'); ?></label>
                    <select id="room-type-filter" class="bookinn-select">
                        <option value=""><?php _e('All Types', 'bookinn'); ?></option>
                        <!-- Room type options will be populated via JavaScript -->
                    </select>
                </div>

                <div class="filter-group">
                    <label><?php _e('Status Filter:', 'bookinn'); ?></label>
                    <select id="status-filter" class="bookinn-select">
                        <option value=""><?php _e('All Statuses', 'bookinn'); ?></option>
                        <option value="confirmed"><?php _e('Confirmed', 'bookinn'); ?></option>
                        <option value="pending"><?php _e('Pending', 'bookinn'); ?></option>
                        <option value="checked_in"><?php _e('Checked In', 'bookinn'); ?></option>
                    </select>
                </div>

                <div class="filter-group">
                    <label><?php _e('Availability Filter:', 'bookinn'); ?></label>
                    <select id="availability-filter" class="bookinn-select">
                        <option value="bookings"><?php _e('Show Bookings', 'bookinn'); ?></option>
                        <option value="availability"><?php _e('Show Availability', 'bookinn'); ?></option>
                        <option value="both"><?php _e('Show Both', 'bookinn'); ?></option>
                    </select>
                </div>

                <div class="bookinn-calendar-legend">
                    <span class="bookinn-legend-item">
                        <span class="bookinn-legend-color bookinn-status-confirmed"></span>
                        <?php _e('Confirmed', 'bookinn'); ?>
                    </span>
                    <span class="bookinn-legend-item">
                        <span class="bookinn-legend-color bookinn-status-pending"></span>
                        <?php _e('Pending', 'bookinn'); ?>
                    </span>
                    <span class="bookinn-legend-item">
                        <span class="bookinn-legend-color bookinn-status-checked_in"></span>
                        <?php _e('Checked In', 'bookinn'); ?>
                    </span>
                </div>
            </div>

            <!-- FullCalendar Container -->
            <div class="bookinn-calendar-wrapper">
                <div id="bookinn-calendar" class="bookinn-calendar-widget"></div>
            </div>

            <!-- Calendar Event Modal -->
            <div class="bookinn-modal" id="bookinn-event-modal" style="display: none;">
                <div class="bookinn-modal-content">
                    <div class="bookinn-modal-header">
                        <h4 id="event-modal-title"><?php _e('Booking Details', 'bookinn'); ?></h4>
                        <button class="bookinn-close-modal" id="close-event-modal">
                            <i class="fa-solid fa-xmark bookinn-fa"></i>
                        </button>
                    </div>

                    <div class="bookinn-modal-body" id="event-modal-body">
                        <!-- Event details will be loaded here -->
                    </div>

                    <div class="bookinn-modal-footer">
                        <button class="bookinn-btn bookinn-btn-secondary" id="close-event-details">
                            <?php _e('Close', 'bookinn'); ?>
                        </button>
                        <button class="bookinn-btn bookinn-btn-primary" id="edit-booking-from-calendar">
                            <?php _e('Edit Booking', 'bookinn'); ?>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render room options for filters
     */
    private function render_room_options() {
        $rooms = BookInn_Database_Manager::get_results("
            SELECT r.id, r.room_number, r.name, rt.name as room_type
            FROM {rooms} r
            LEFT JOIN {room_types} rt ON r.room_type_id = rt.id
            WHERE r.is_active = 1
            ORDER BY r.room_number
        ");

        foreach ($rooms as $room) {
            $display_name = $room->room_number;
            if ($room->name) {
                $display_name .= ' - ' . $room->name;
            }
            if ($room->room_type) {
                $display_name .= ' (' . ucfirst($room->room_type) . ')';
            }

            printf(
                '<option value="%d">%s</option>',
                esc_attr($room->id),
                esc_html($display_name)
            );
        }
    }    
    
    /**
     * Render Reports Tab
     */
    private function render_reports_tab() {
        // Get reports metrics
        $metrics = $this->get_reports_metrics();
        ?>
        <!-- Reports Container -->
        <div class="bookinn-reports-container">
            <!-- Reports Header -->
            <div class="bookinn-section-header">
                <h3><?php _e('Analytics & Reports', 'bookinn'); ?></h3>
                <div class="bookinn-section-actions">
                    <select id="report-period" class="bookinn-select">
                        <option value="7"><?php _e('Last 7 days', 'bookinn'); ?></option>
                        <option value="30" selected><?php _e('Last 30 days', 'bookinn'); ?></option>
                        <option value="90"><?php _e('Last 3 months', 'bookinn'); ?></option>
                        <option value="365"><?php _e('Last year', 'bookinn'); ?></option>
                    </select>
                    <button class="bookinn-btn bookinn-btn-primary" id="export-reports">
                        <i class="fa-solid fa-download bookinn-fa"></i>
                        <?php _e('Export', 'bookinn'); ?>
                    </button>
                </div>
            </div>

            <!-- Key Performance Indicators -->
            <div class="bookinn-metrics-grid">
                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-euro-sign bookinn-fa"></i>
                </div>
                        <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value">€<?php echo esc_html(number_format($metrics['total_revenue'], 2)); ?></h3>
                        <p class="bookinn-metric-label"><?php _e('Revenue Performance', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change <?php echo $metrics['revenue_change'] >= 0 ? 'positive' : 'negative'; ?>">
                                <?php echo $metrics['revenue_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['revenue_change']); ?>%</span>
                        </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-percent bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                        <h3 class="bookinn-metric-value"><?php echo esc_html($metrics['occupancy_rate']); ?>%</h3>
                        <p class="bookinn-metric-label"><?php _e('Occupancy Rate', 'bookinn'); ?></p>
                        <span class="bookinn-metric-change <?php echo $metrics['occupancy_change'] >= 0 ? 'positive' : 'negative'; ?>">
                            <?php echo $metrics['occupancy_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['occupancy_change']); ?>%
                        </span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                    <h3 class="bookinn-metric-value">€<?php echo esc_html(number_format($metrics['avg_daily_rate'], 2)); ?></h3>
                    <p class="bookinn-metric-label"><?php _e('Average Daily Rate', 'bookinn'); ?></p>  
                    <span class="bookinn-metric-change <?php echo $metrics['adr_change'] >= 0 ? 'positive' : 'negative'; ?>">
                        <?php echo $metrics['adr_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['adr_change']); ?>%</span>
                    </div>
                </div>

                <div class="bookinn-metric-card">
                    <div class="bookinn-metric-icon">
                        <i class="fa-solid fa-calendar-check bookinn-fa"></i>
                    </div>
                    <div class="bookinn-metric-content">
                    <h3 class="bookinn-metric-value">€<?php echo esc_html(number_format($metrics['revpar'], 2)); ?></h3>
                    <p class="bookinn-metric-label"><?php _e('RevPAR', 'bookinn'); ?></p> 
                    <span class="bookinn-metric-change <?php echo $metrics['revpar_change'] >= 0 ? 'positive' : 'negative'; ?>">
                        <?php echo $metrics['revpar_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['revpar_change']); ?>%</span>
                   </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="bookinn-reports-charts bookinn-charts-grid">
                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h4><?php _e('Revenue by Month', 'bookinn'); ?></h4>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="revenue-by-month-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h4><?php _e('Occupancy Trends', 'bookinn'); ?></h4>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="occupancy-trends-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h4><?php _e('Room Type Performance', 'bookinn'); ?></h4>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="room-type-performance-chart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="bookinn-chart-container">
                    <div class="bookinn-chart-header">
                        <h4><?php _e('Booking Sources', 'bookinn'); ?></h4>
                    </div>
                    <div class="bookinn-chart-wrapper">
                        <canvas id="booking-sources-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Additional Reports Table -->
            <div class="bookinn-reports-table">
                <div class="bookinn-section-header">
                    <h4><?php _e('Detailed Analytics', 'bookinn'); ?></h4>
                </div>
                <div class="bookinn-table-wrapper">
                    <table class="bookinn-table">
                        <thead>
                            <tr>
                                <th><?php _e('Metric', 'bookinn'); ?></th>
                                <th><?php _e('Current Period', 'bookinn'); ?></th>
                                <th><?php _e('Previous Period', 'bookinn'); ?></th>
                                <th><?php _e('Change', 'bookinn'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><?php _e('Total Bookings', 'bookinn'); ?></td>
                                <td><?php echo esc_html($metrics['total_bookings']); ?></td>
                                <td><?php echo esc_html($metrics['prev_bookings']); ?></td>
                                <td><span class="<?php echo $metrics['bookings_change'] >= 0 ? 'positive' : 'negative'; ?>"><?php echo $metrics['bookings_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['bookings_change']); ?>%</span></td>
                            </tr>
                            <tr>
                                <td><?php _e('Total Revenue', 'bookinn'); ?></td>
                                <td>€<?php echo esc_html(number_format($metrics['total_revenue'], 2)); ?></td>
                                <td>€<?php echo esc_html(number_format($metrics['prev_revenue'], 2)); ?></td>
                                <td><span class="<?php echo $metrics['revenue_change'] >= 0 ? 'positive' : 'negative'; ?>"><?php echo $metrics['revenue_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['revenue_change']); ?>%</span></td>
                            </tr>
                            <tr>
                                <td><?php _e('Average Stay Length', 'bookinn'); ?></td>
                                <td><?php echo esc_html($metrics['avg_stay_length']); ?> <?php _e('days', 'bookinn'); ?></td>
                                <td><?php echo esc_html($metrics['prev_avg_stay']); ?> <?php _e('days', 'bookinn'); ?></td>
                                <td><span class="<?php echo $metrics['stay_change'] >= 0 ? 'positive' : 'negative'; ?>"><?php echo $metrics['stay_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['stay_change']); ?>%</span></td>
                            </tr>
                            <tr>
                                <td><?php _e('Cancellation Rate', 'bookinn'); ?></td>
                                <td><?php echo esc_html($metrics['cancellation_rate']); ?>%</td>
                                <td><?php echo esc_html($metrics['prev_cancellation_rate']); ?>%</td>
                                <td><span class="<?php echo $metrics['cancellation_change'] <= 0 ? 'positive' : 'negative'; ?>"><?php echo $metrics['cancellation_change'] >= 0 ? '+' : ''; ?><?php echo esc_html($metrics['cancellation_change']); ?>%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render Sidebar
     */
    private function render_sidebar() {
        ?>
        <div class="bookinn-sidebar-content">
            <!-- Quick Actions Widget -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Quick Actions', 'bookinn'); ?></h4>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-quick-actions">
                        <button class="bookinn-quick-action" id="quick-new-booking">
                            <i class="fa-solid fa-plus bookinn-fa"></i>
                            <span><?php _e('New Booking', 'bookinn'); ?></span>
                        </button>
                        <button class="bookinn-quick-action" id="quick-check-in">
                            <i class="fa-solid fa-right-to-bracket bookinn-fa"></i>
                            <span><?php _e('Check In', 'bookinn'); ?></span>
                        </button>
                        <button class="bookinn-quick-action" id="quick-check-out">
                            <i class="fa-solid fa-right-from-bracket bookinn-fa"></i>
                            <span><?php _e('Check Out', 'bookinn'); ?></span>
                        </button>
                        <button class="bookinn-quick-action" id="quick-room-status">
                            <i class="fa-solid fa-gear bookinn-fa"></i>
                            <span><?php _e('Room Status', 'bookinn'); ?></span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Today's Arrivals -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Today\'s Arrivals', 'bookinn'); ?></h4>
                    <span class="bookinn-widget-count" id="arrivals-count">0</span>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-arrivals-list" id="todays-arrivals">
                        <?php $this->render_todays_arrivals(); ?>
                    </div>
                </div>
            </div>

            <!-- Today's Departures -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Today\'s Departures', 'bookinn'); ?></h4>
                    <span class="bookinn-widget-count" id="departures-count">0</span>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-departures-list" id="todays-departures">
                        <?php $this->render_todays_departures(); ?>
                    </div>
                </div>
            </div>

            <!-- Notifications -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Notifications', 'bookinn'); ?></h4>
                    <span class="bookinn-widget-count" id="notifications-count">0</span>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-notifications-list" id="notifications-list">
                        <?php $this->render_notifications(); ?>
                    </div>
                </div>
            </div>

            <!-- Room Status Overview -->
            <div class="bookinn-sidebar-widget">
                <div class="bookinn-widget-header">
                    <h4><?php _e('Room Status', 'bookinn'); ?></h4>
                </div>
                <div class="bookinn-widget-content">
                    <div class="bookinn-room-status-overview" id="room-status-overview">
                        <?php $this->render_room_status_overview(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render today's arrivals
     */
    private function render_todays_arrivals() {
        $today = date('Y-m-d');
        $arrivals = BookInn_Database_Manager::get_results(
            "SELECT b.*, g.first_name, g.last_name, r.room_number
            FROM {bookings} b
            LEFT JOIN {guests} g ON b.guest_id = g.id
            LEFT JOIN {rooms} r ON b.room_id = r.id
            WHERE b.check_in_date = '$today' AND b.status IN ('confirmed', 'pending')
            ORDER BY b.created_at DESC"
        );

        if (empty($arrivals)) {
            echo '<p class="bookinn-no-data">' . __('No arrivals today', 'bookinn') . '</p>';
            return;
        }

        foreach ($arrivals as $arrival) {
            $guest_name = $arrival->first_name . ' ' . $arrival->last_name;
            ?>
            <div class="bookinn-arrival-item">
                <div class="bookinn-guest-info">
                    <strong><?php echo esc_html($guest_name); ?></strong>
                    <small><?php echo esc_html($arrival->room_number); ?></small>
                </div>
                <button class="bookinn-btn-small bookinn-check-in-btn" data-booking-id="<?php echo esc_attr($arrival->id); ?>">
                    <?php _e('Check In', 'bookinn'); ?>
                </button>
            </div>
            <?php
        }
    }

    /**
     * Render today's departures
     */
    private function render_todays_departures() {
        $today = date('Y-m-d');
        $departures = BookInn_Database_Manager::get_results(
            "SELECT b.*, g.first_name, g.last_name, r.room_number
            FROM {bookings} b
            LEFT JOIN {guests} g ON b.guest_id = g.id
            LEFT JOIN {rooms} r ON b.room_id = r.id
            WHERE b.check_out_date = '$today' AND b.status = 'checked_in'
            ORDER BY b.created_at DESC"
        );

        if (empty($departures)) {
            echo '<p class="bookinn-no-data">' . __('No departures today', 'bookinn') . '</p>';
            return;
        }

        foreach ($departures as $departure) {
            $guest_name = $departure->first_name . ' ' . $departure->last_name;
            ?>
            <div class="bookinn-departure-item">
                <div class="bookinn-guest-info">
                    <strong><?php echo esc_html($guest_name); ?></strong>
                    <small><?php echo esc_html($departure->room_number); ?></small>
                </div>
                <button class="bookinn-btn-small bookinn-check-out-btn" data-booking-id="<?php echo esc_attr($departure->id); ?>">
                    <?php _e('Check Out', 'bookinn'); ?>
                </button>
            </div>
            <?php
        }
    }

    /**
     * Render notifications
     */
    private function render_notifications() {
        // Mock notifications - in real implementation, these would come from database
        $notifications = array(
            array(
                'type' => 'warning',
                'message' => __('Room 101 needs maintenance', 'bookinn'),
                'time' => '2 hours ago'
            ),
            array(
                'type' => 'info',
                'message' => __('New booking received', 'bookinn'),
                'time' => '1 hour ago'
            ),
            array(
                'type' => 'success',
                'message' => __('Payment confirmed for booking #123', 'bookinn'),
                'time' => '30 minutes ago'
            )
        );

        if (empty($notifications)) {
            echo '<p class="bookinn-no-data">' . __('No notifications', 'bookinn') . '</p>';
            return;
        }

        foreach ($notifications as $notification) {
            ?>
            <div class="bookinn-notification-item bookinn-notification-<?php echo esc_attr($notification['type']); ?>">
                <div class="bookinn-notification-content">
                    <p><?php echo esc_html($notification['message']); ?></p>
                    <small><?php echo esc_html($notification['time']); ?></small>
                </div>
            </div>
            <?php
        }
    }

    /**
     * Render room status overview
     */
    private function render_room_status_overview() {
        $status_counts = BookInn_Database_Manager::get_results("
            SELECT status, COUNT(*) as count
            FROM {rooms}
            WHERE is_active = 1
            GROUP BY status
        ");

        $statuses = array(
            'available' => array('label' => __('Available', 'bookinn'), 'count' => 0, 'color' => 'green'),
            'occupied' => array('label' => __('Occupied', 'bookinn'), 'count' => 0, 'color' => 'red'),
            'maintenance' => array('label' => __('Maintenance', 'bookinn'), 'count' => 0, 'color' => 'orange'),
            'out_of_order' => array('label' => __('Out of Order', 'bookinn'), 'count' => 0, 'color' => 'gray')
        );

        foreach ($status_counts as $status) {
            if (isset($statuses[$status->status])) {
                $statuses[$status->status]['count'] = $status->count;
            }
        }

        foreach ($statuses as $key => $status) {
            ?>
            <div class="bookinn-status-item">
                <span class="bookinn-status-indicator bookinn-status-<?php echo esc_attr($key); ?>"></span>
                <span class="bookinn-status-label"><?php echo esc_html($status['label']); ?></span>
                <span class="bookinn-status-count"><?php echo esc_html($status['count']); ?></span>
            </div>
            <?php
        }
    }

    /**
     * Render Gantt chart for the widget (standalone implementation)
     */
    private function render_gantt_chart() {
        // Enqueue Gantt assets
        wp_enqueue_style('bookinn-gantt', plugins_url('assets/css/bookinn-gantt.css', BOOKINN_PLUGIN_FILE), [], '1.0');
        wp_enqueue_script('bookinn-gantt', plugins_url('assets/js/bookinn-gantt.js', BOOKINN_PLUGIN_FILE), ['jquery'], '1.0', true);

        // Localize script for AJAX
        wp_localize_script('bookinn-gantt', 'BookInnGantt', [
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce'    => wp_create_nonce('bookinn_gantt_widget')
        ]);

        ob_start();
        ?>
        <div class="gantt-container widget-integrated" id="widgetGanttChart">
            <div class="gantt-chart">
                <table class="gantt-table" id="widgetGanttTable">
                    <thead>
                        <tr id="widgetHeaderRow">
                            <th class="room-header"></th>
                        </tr>
                    </thead>
                    <tbody id="widgetGanttBody">
                        <!-- Rows will be populated via JavaScript -->
                    </tbody>
                </table>
            </div>

            <div class="legend">
                <div class="legend-item"><div class="legend-color confirmed"></div><span>Confirmed</span></div>
                <div class="legend-item"><div class="legend-color pending"></div><span>Pending</span></div>
                <div class="legend-item"><div class="legend-color checkedin"></div><span>Checked-in</span></div>
                <div class="legend-item"><div class="legend-color cancelled"></div><span>Cancelled</span></div>
            </div>
        </div>

        <div class="tooltip" id="tooltip"></div>
        <?php
        return ob_get_clean();
    }

    /**
     * Initialize Gantt AJAX handlers
     */
    private function init_gantt_ajax_handlers() {
        add_action('wp_ajax_bookinn_widget_get_rooms_and_bookings', array($this, 'ajax_get_rooms_and_bookings'));
        add_action('wp_ajax_bookinn_widget_gantt_apply_filters', array($this, 'ajax_gantt_apply_filters'));
    }

    /**
     * AJAX handler to get rooms and bookings for widget Gantt
     */
    public function ajax_get_rooms_and_bookings() {
        check_ajax_referer('bookinn_gantt_widget', 'nonce');

        global $wpdb;

        try {
            $rooms = $wpdb->get_results("SELECT id, name, room_number, status FROM {$wpdb->prefix}bookinn_rooms", ARRAY_A);
            $bookings = $wpdb->get_results("
                SELECT b.id, b.room_id as roomId, b.guest_id, g.first_name, g.last_name,
                       b.check_in_date as checkIn, b.check_out_date as checkOut, b.status,
                       b.adults, b.children, b.total_amount as totalAmount
                FROM {$wpdb->prefix}bookinn_bookings b
                LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
            ", ARRAY_A);

            // Process bookings data
            foreach ($bookings as &$b) {
                $b['guestName'] = trim(($b['first_name'] ?? '') . ' ' . ($b['last_name'] ?? ''));
                $b['guests'] = intval($b['adults'] ?? 1) + intval($b['children'] ?? 0);
            }
            unset($b);

            wp_send_json_success(['rooms' => $rooms, 'bookings' => $bookings]);

        } catch (Exception $e) {
            error_log('BookInn Widget Gantt Error: ' . $e->getMessage());
            wp_send_json_error('Failed to load Gantt data: ' . $e->getMessage());
        }
    }

    /**
     * AJAX handler to apply filters to widget Gantt
     */
    public function ajax_gantt_apply_filters() {
        check_ajax_referer('bookinn_gantt_widget', 'nonce');

        $view_mode = sanitize_text_field($_POST['viewMode'] ?? 'month');
        $current_date = sanitize_text_field($_POST['currentDate'] ?? date('Y-m-d'));
        $filters = $_POST['filters'] ?? array();

        // Sanitize filters
        $room_type = sanitize_text_field($filters['roomType'] ?? '');
        $booking_status = sanitize_text_field($filters['bookingStatus'] ?? '');
        $room_status = sanitize_text_field($filters['roomStatus'] ?? '');
        $custom_start = sanitize_text_field($filters['customStart'] ?? '');
        $custom_end = sanitize_text_field($filters['customEnd'] ?? '');

        global $wpdb;

        try {
            // Build room query with filters
            $room_where = "1=1";
            $room_params = array();

            if (!empty($room_type)) {
                $room_where .= " AND room_type = %s";
                $room_params[] = $room_type;
            }

            if (!empty($room_status)) {
                $room_where .= " AND status = %s";
                $room_params[] = $room_status;
            }

            $rooms_query = "SELECT id, name, room_number, status FROM {$wpdb->prefix}bookinn_rooms WHERE {$room_where}";
            if (!empty($room_params)) {
                $rooms = $wpdb->get_results($wpdb->prepare($rooms_query, $room_params), ARRAY_A);
            } else {
                $rooms = $wpdb->get_results($rooms_query, ARRAY_A);
            }

            // Build booking query with filters
            $booking_where = "1=1";
            $booking_params = array();

            if (!empty($booking_status)) {
                $booking_where .= " AND b.status = %s";
                $booking_params[] = $booking_status;
            }

            // Date range filters
            if (!empty($custom_start)) {
                $booking_where .= " AND b.check_out_date >= %s";
                $booking_params[] = $custom_start;
            }

            if (!empty($custom_end)) {
                $booking_where .= " AND b.check_in_date <= %s";
                $booking_params[] = $custom_end;
            }

            $bookings_query = "
                SELECT b.id, b.room_id as roomId, b.guest_id, g.first_name, g.last_name,
                       b.check_in_date as checkIn, b.check_out_date as checkOut, b.status,
                       b.adults, b.children, b.total_amount as totalAmount
                FROM {$wpdb->prefix}bookinn_bookings b
                LEFT JOIN {$wpdb->prefix}bookinn_guests g ON b.guest_id = g.id
                WHERE {$booking_where}
            ";

            if (!empty($booking_params)) {
                $bookings = $wpdb->get_results($wpdb->prepare($bookings_query, $booking_params), ARRAY_A);
            } else {
                $bookings = $wpdb->get_results($bookings_query, ARRAY_A);
            }

            // Process bookings data
            foreach ($bookings as &$b) {
                $b['guestName'] = trim(($b['first_name'] ?? '') . ' ' . ($b['last_name'] ?? ''));
                $b['guests'] = intval($b['adults'] ?? 1) + intval($b['children'] ?? 0);
            }
            unset($b);

            // Return filtered data
            wp_send_json_success(array(
                'rooms' => $rooms,
                'bookings' => $bookings,
                'applied_filters' => $filters,
                'view_mode' => $view_mode,
                'current_date' => $current_date
            ));

        } catch (Exception $e) {
            error_log('BookInn Widget Gantt Apply Filters Error: ' . $e->getMessage());
            wp_send_json_error('Failed to apply filters: ' . $e->getMessage());
        }
    }
}
