/**
 * BookInn Management Unified JS
 *
 * Modal, Form, and Style Management Overview:
 *
 * 1. Modal System (Room, Room Type, Booking):
 *    - Uses a unified global modal system: window.BookInn.Core.ModalSystem
 *    - Methods: showRoomModal, showRoomTypeModal, showBookingModal
 *    - Ensures only one modal instance per type exists in the DOM at a time
 *    - All modals use .is-active class and aria-modal for accessibility
 *    - Close actions (Cancel, X, overlay click) are bound via ModalSystem for consistency
 *    - Focus is trapped within the modal for accessibility
 *    - Methods hideRoomModal, hideRoomTypeModal, etc., use the same global system for closing
 *
 * 2. Form Handling:
 *    - Each modal has a dedicated form (room, room type, booking)
 *    - Methods: populateRoomForm, clearRoomForm, populateRoomTypeForm, clearRoomTypeForm, etc.
 *    - Data is loaded and saved via AJAX, with loading and error states managed in the modal
 *    - Validation and feedback are provided before submission
 *
 * 3. Button and Style Consistency:
 *    - All modal actions use .bookinn-btn and .bookinn-btn-secondary/.bookinn-btn-primary classes
 *    - Cancel and close buttons use .bookinn-close-modal or .bookinn-modal-close for event binding
 *    - Styles ensure proper z-index, spacing, and responsive layout
 *    - Accessibility: aria attributes, focus management, and keyboard navigation
 *
 * 4. Event Binding:
 *    - Modal close events are bound centrally by ModalSystem.bindModalEvents
 *    - Prevents duplicate event handlers and ensures reliable close behavior
 *    - Overlay/background click and Escape key close the modal
 *
 * 5. DOM Cleanup:
 *    - Before creating/appending a modal, any existing modal of the same type is removed
 *    - Prevents duplicate modals and event binding issues
 *
 * 6. Fallbacks:
 *    - If the global modal system is unavailable, fallback logic adds/removes .is-active and .bookinn-modal-open
 *
 * This structure ensures a consistent, accessible, and maintainable modal and form experience across all management sections.
 */



(function($, window, document, undefined) {
    'use strict';
    
    // === DEBUG: Override e Visibilità === 
    console.log('🚀 BookInn Unified Loading - Version 1.0.1');
    console.log('📅 Calendar room type filter: ACTIVE');
    console.log('📊 Reports visibility protection: ACTIVE');

    // Ensure global BookInn object exists
    window.BookInn = window.BookInn || {};

    // Prevent multiple initializations but preserve existing object
    if (window.BookInn.ManagementUnified && window.BookInn.ManagementUnified._initialized) {
        console.log('BookInn Management Unified: Already initialized, skipping...');
        return;
    }

    // Store any existing object temporarily
    const existingManagementUnified = window.BookInn.ManagementUnified || {};

    /**
     * Unified Management System
     */
    const ManagementUnifiedObject = {
        
        // Initialization flag
        _initialized: false,
        
        // Configuration
        config: {
            ajaxUrl: (typeof bookinn_dashboard !== 'undefined') ? bookinn_dashboard.ajax_url : '/wp-admin/admin-ajax.php',
            nonce: (typeof bookinn_dashboard !== 'undefined') ? bookinn_dashboard.nonce : '',
            ajaxNonce: (typeof bookinn_dashboard !== 'undefined') ? bookinn_dashboard.ajax_nonce : '',
            managementNonce: (typeof bookinn_dashboard !== 'undefined') ? bookinn_dashboard.management_nonce : '',
            frontendNonce: (typeof bookinn_dashboard !== 'undefined') ? bookinn_dashboard.frontend_nonce : '',
            restUrl: (typeof bookinn_dashboard !== 'undefined') ? bookinn_dashboard.rest_url : '/wp-json/bookinn/v1/',
            restNonce: (typeof bookinn_dashboard !== 'undefined') ? bookinn_dashboard.rest_nonce : '',
            strings: (typeof bookinn_dashboard !== 'undefined') ? bookinn_dashboard.strings : {
                loading: 'Loading...',
                error: 'Error loading data',
                no_data: 'No data available',
                no_recent_activity: 'No recent activity',
                save_success: 'Saved successfully'
            }
        },

        // Data cache
        cache: {
            rooms: null,
            roomTypes: null,
            bookings: null
        },

        // Chart instances
        charts: {},

        // Loading flags to prevent duplicate calls
        loadingFlags: {
            dashboard: false,
            bookings: false,
            rooms: false,
            calendar: false,
            reports: false
        },

        // Calendar instance
        calendar: null,

        // Modal Management System - Migrated from frontend-dashboard
        ModalManager: {
            activeModal: null,
            focusBeforeModal: null,

            // Clean up duplicate modals and prevent ID conflicts
            cleanupDuplicateModals: function() {
                try {
                    // Remove any existing modals to prevent duplicates and ID conflicts
                    $('#bookinn-room-modal, #bookinn-room-type-modal, #bookinn-booking-modal, #bookinn-booking-view-modal, #bookinn-booking-edit-modal, #room-status-modal, #bookinn-room-types-management-modal').each(function() {
                        $(this).removeClass('bookinn-modal-open is-active').hide().remove();
                    });

                    // Remove orphaned backdrops and overlays
                    $('.bookinn-modal-backdrop, .bookinn-modal-overlay').remove();

                    // Reset body scroll
                    $('body').removeClass('bookinn-modal-open');

                    // Clear any duplicate event handlers
                    $(document).off('.modalManager');

                    console.log('BookInn Management Unified: Cleaned up duplicate modals and prevented ID conflicts');
                } catch (error) {
                    console.error('BookInn Management Unified: Error cleaning up modals:', error);
                }
            },

            show: function(modalElement, options = {}) {
                try {
                    this.cleanupDuplicateModals();

                    const $modal = $(modalElement);
                    if (!$modal.length) {
                        console.error('BookInn Management Unified: Modal element not found:', modalElement);
                        return;
                    }

                    // Store focus
                    this.focusBeforeModal = document.activeElement;
                    this.activeModal = $modal[0];

                    // Preserve scroll position
                    this.preserveScrollPosition();

                    // Set accessibility attributes for active modal
                    $modal.attr('aria-modal', 'true');
                    
                    // Show modal
                    $modal.addClass('is-active').show();
                    $('body').addClass('bookinn-modal-open');

                    // Bind events
                    this.bindModalEvents($modal);

                    // Focus management
                    setTimeout(() => {
                        const firstFocusable = $modal.find('input, button, select, textarea, [tabindex]:not([tabindex="-1"])').first();
                        if (firstFocusable.length) {
                            firstFocusable.focus();
                        }
                    }, 100);

                    console.log('BookInn Management Unified: Modal shown:', modalElement);
                } catch (error) {
                    console.error('BookInn Management Unified: Error showing modal:', error);
                }
            },

            hide: function(modalElement, options = {}) {
                try {
                    const $modal = $(modalElement);
                    if (!$modal.length) return;

                    $modal.removeClass('is-active').hide();
                    $('body').removeClass('bookinn-modal-open');

                    // Remove accessibility attributes
                    $modal.removeAttr('aria-modal');

                    // Restore scroll position
                    this.restoreScrollPosition();

                    // Restore focus
                    if (this.focusBeforeModal) {
                        this.focusBeforeModal.focus();
                        this.focusBeforeModal = null;
                    }

                    this.activeModal = null;
                    $modal.off('.modalManager');

                    console.log('BookInn Management Unified: Modal hidden:', modalElement);
                } catch (error) {
                    console.error('BookInn Management Unified: Error hiding modal:', error);
                }
            },

            bindModalEvents: function($modal) {
                // Unbind previous events
                $modal.off('.modalManager');

                // Close button click - support both close button classes
                $modal.on('click.modalManager', '.bookinn-close-modal, .bookinn-modal-close', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    this.hide($modal);
                });

                // Background click to close
                $modal.on('click.modalManager', (e) => {
                    if (e.target === $modal[0]) {
                        this.hide($modal);
                    }
                });

                // Trap focus within modal
                $modal.on('keydown.modalManager', (e) => {
                    if (e.key === 'Escape') {
                        this.hide($modal);
                        return;
                    }

                    if (e.key === 'Tab') {
                        const focusableElements = $modal.find('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
                        const firstFocusable = focusableElements.first()[0];
                        const lastFocusable = focusableElements.last()[0];

                        if (e.shiftKey && document.activeElement === firstFocusable) {
                            e.preventDefault();
                            lastFocusable.focus();
                        } else if (!e.shiftKey && document.activeElement === lastFocusable) {
                            e.preventDefault();
                            firstFocusable.focus();
                        }
                    }
                });
            },

            // Scrollbar preservation methods
            preserveScrollPosition: function() {
                try {
                    // Store current scroll position
                    this.scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

                    // Calculate scrollbar width
                    const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;

                    // Store original body padding
                    this.originalBodyPadding = document.body.style.paddingRight;

                    // Apply scrollbar compensation
                    if (scrollbarWidth > 0) {
                        document.body.style.paddingRight = scrollbarWidth + 'px';
                    }

                    console.log('BookInn Management Unified: Scroll position preserved:', this.scrollPosition, 'Scrollbar width:', scrollbarWidth);
                } catch (error) {
                    console.error('BookInn Management Unified: Error preserving scroll position:', error);
                }
            },

            restoreScrollPosition: function() {
                try {
                    // Restore body padding
                    if (this.originalBodyPadding !== undefined) {
                        document.body.style.paddingRight = this.originalBodyPadding;
                        this.originalBodyPadding = undefined;
                    }

                    // No need to restore scroll position since we're not using position: fixed
                    this.scrollPosition = undefined;

                    console.log('BookInn Management Unified: Scroll position restored');
                } catch (error) {
                    console.error('BookInn Management Unified: Error restoring scroll position:', error);
                }
            },

            destroy: function(modalElement) {
                try {
                    const $modal = $(modalElement);
                    if (!$modal.length) return;

                    this.hide(modalElement);
                    $modal.remove();

                    console.log('BookInn Management Unified: Modal destroyed:', modalElement);
                } catch (error) {
                    console.error('BookInn Management Unified: Error destroying modal:', error);
                }
            }
        },

        /**
         * Initialize the unified management system
         */
        init: function() {
            if (this._initialized) {
                console.log('BookInn Management Unified: Already initialized');
                return;
            }

            console.log('BookInn Management Unified: Initializing...');
            
            // Debug: Show nonce configuration
            console.log('BookInn Management Unified: Nonce configuration:', {
                nonce: this.config.nonce.substr(0, 10) + '...',
                ajaxNonce: this.config.ajaxNonce.substr(0, 10) + '...',
                managementNonce: this.config.managementNonce.substr(0, 10) + '...',
                frontendNonce: this.config.frontendNonce.substr(0, 10) + '...',
                ajaxUrl: this.config.ajaxUrl
            });

            // DEBUG: Function existence check
            console.log('BookInn Management Unified: Function validation:', {
                'checkRoomAvailability': typeof this.checkRoomAvailability,
                'showQuickBookWithRoom': typeof this.showQuickBookWithRoom,
                'clearRoomFilters': typeof this.clearRoomFilters,
                'initEventHandlers': typeof this.initEventHandlers,
                'Object context': this
            });

            try {
                // Check dependencies
                if (!this.checkDependencies()) {
                    console.error('BookInn Management Unified: Missing dependencies');
                    return;
                }

                // Initialize core components
                this.initEventHandlers();
                this.initTabs();
                this.initModals();
                this.initDataLoading();
                this.initCharts();

                // Load initial content
                this.loadInitialContent();
                
                this._initialized = true;
                console.log('BookInn Management Unified: Initialization complete');
                
            } catch (error) {
                console.error('BookInn Management Unified: Initialization failed:', error);
            }
        },

        /**
         * Check for required dependencies
         */
        checkDependencies: function() {
            const required = [
                { name: 'jQuery', check: () => typeof $ !== 'undefined' },
                { name: 'BookInn.Core', check: () => window.BookInn && window.BookInn.Core },
                { name: 'Dashboard Container', check: () => $('.bookinn-dashboard-container, .bookinn-management-widget').length > 0 }
            ];

            for (const dep of required) {
                if (!dep.check()) {
                    console.error(`BookInn Management Unified: Missing dependency: ${dep.name}`);
                    return false;
                }
            }
            
            return true;
        },

        /**
         * Initialize event handlers - prevent duplicates
         */
        initEventHandlers: function() {
            // Show Room (read-only) handler
            $(document).on('click', '.bookinn-show-room', function(e) {
                e.preventDefault();
                const roomId = $(this).data('room-id');
                // Find room data from cache
                const room = window.BookInn.ManagementUnified.cache.rooms.find(r => r.id == roomId);
                if (room) {
                    window.BookInn.ManagementUnified.showRoomReadOnlyModal(room);
                }
            });

            // Cancel button in show room modal
            $(document).on('click', '#cancel-show-room, #bookinn-room-show-modal .bookinn-modal-close', function(e) {
                e.preventDefault();
                $('#bookinn-room-show-modal').removeClass('is-active').hide();
                $('body').removeClass('bookinn-modal-open');
                setTimeout(function() { $('#bookinn-room-show-modal').remove(); }, 300);
            });

            // Edit button in show room modal
            $(document).on('click', '#edit-from-show-room', function(e) {
                e.preventDefault();
                const $modal = $('#bookinn-room-show-modal');
                const roomId = $modal.data('room-id');
                console.log('BookInn DEBUG: Edit button clicked');
                console.log('BookInn DEBUG: Modal exists:', $modal.length > 0);
                console.log('BookInn DEBUG: Modal HTML data-room-id attr:', $modal.attr('data-room-id'));
                console.log('BookInn DEBUG: Modal jQuery data room-id:', $modal.data('room-id'));
                console.log('BookInn DEBUG: typeof roomId:', typeof roomId, 'value:', roomId);
                $('#bookinn-room-show-modal').removeClass('is-active').hide();
                $('body').removeClass('bookinn-modal-open');
                setTimeout(function() { $('#bookinn-room-show-modal').remove(); }, 300);
                if (roomId) {
                    window.BookInn.ManagementUnified.editRoom(roomId);
                } else {
                    console.error('BookInn ERROR: No room ID found for edit');
                }
            });
            const self = this;

            // Unbind all previous event handlers to prevent duplicates
            $(document).off('.bookinnManagement');
            
            // Tab navigation
            $(document).on('click.bookinnManagement', '.bookinn-tab-link', function(e) {
                e.preventDefault();
                const tabId = $(this).data('tab');
                self.switchTab(tabId);
            });

            // Sub-tab navigation
            $(document).on('click.bookinnManagement', '.bookinn-sub-tab-link', function(e) {
                e.preventDefault();
                const subTabId = $(this).data('tab');
                self.switchSubTab(subTabId);
            });

            // Room management actions
            $(document).on('click.bookinnManagement', '#bookinn-add-room', function(e) {
                e.preventDefault();
                console.log('BookInn: Add room button clicked');
                self.showRoomModal();
            });

            $(document).on('click.bookinnManagement', '.bookinn-edit-room', function(e) {
                e.preventDefault();
                const roomId = $(this).data('room-id');
                console.log('BookInn: Edit room button clicked, roomId:', roomId);
                self.editRoom(roomId);
            });

            $(document).on('click.bookinnManagement', '#bookinn-add-room-type', function(e) {
                e.preventDefault();
                console.log('BookInn: Add room type button clicked');
                self.showRoomTypeModal();
            });

            $(document).on('click.bookinnManagement', '.bookinn-edit-room-type', function(e) {
                e.preventDefault();
                const roomTypeId = $(this).data('room-type-id');
                console.log('BookInn: Edit room type button clicked, roomTypeId:', roomTypeId);
                self.editRoomType(roomTypeId);
            });

            // Room types management button
            $(document).on('click', '#bookinn-room-types', function(e) {
                e.preventDefault();
                self.showRoomTypesManagementModal();
            });

            // Save actions
            $(document).on('click', '#save-room', function(e) {
                e.preventDefault();
                self.saveRoom();
            });

            $(document).on('click', '#save-room-type', function(e) {
                e.preventDefault();
                self.saveRoomType();
            });

            $(document).on('click', '#save-new-booking', function(e) {
                e.preventDefault();
                self.saveNewBooking();
            });

            // Delete actions
            $(document).on('click', '.bookinn-delete-room', function(e) {
                e.preventDefault();
                const roomId = $(this).data('room-id');
                self.deleteRoom(roomId);
            });

            $(document).on('click', '.bookinn-delete-room-type', function(e) {
                e.preventDefault();
                const roomTypeId = $(this).data('room-type-id');
                self.deleteRoomType(roomTypeId);
            });

            // Room availability filter handlers - REMOVED OBSOLETE HANDLER
            // The check-room-availability button is now handled in initializeAvailabilityCheck()

            $(document).on('click', '#clear-room-filters', function(e) {
                e.preventDefault();
                console.log('BookInn: Clear filters clicked - context check:', {
                    'self': typeof self,
                    'self.clearRoomFilters': typeof self.clearRoomFilters
                });
                
                if (typeof self !== 'undefined' && typeof self.clearRoomFilters === 'function') {
                    self.clearRoomFilters();
                } else if (window.BookInn && window.BookInn.ManagementUnified && typeof window.BookInn.ManagementUnified.clearRoomFilters === 'function') {
                    console.log('BookInn: Using global reference for clearRoomFilters');
                    window.BookInn.ManagementUnified.clearRoomFilters();
                } else {
                    console.error('BookInn: clearRoomFilters function not found');
                }
            });

            // Quick Book room with pre-selection - FIXED CONTEXT
            $(document).on('click', '.bookinn-quick-book-room', function(e) {
                e.preventDefault();
                const roomId = $(this).data('room-id');
                const roomNumber = $(this).data('room-number');
                const roomType = $(this).data('room-type');
                
                console.log('BookInn: Quick book clicked - context check:', {
                    'self': typeof self,
                    'self.showQuickBookWithRoom': typeof self.showQuickBookWithRoom,
                    'roomId': roomId,
                    'roomNumber': roomNumber,
                    'roomType': roomType
                });
                
                if (typeof self !== 'undefined' && typeof self.showQuickBookWithRoom === 'function') {
                    self.showQuickBookWithRoom(roomId, roomNumber, roomType);
                } else if (window.BookInn && window.BookInn.ManagementUnified && typeof window.BookInn.ManagementUnified.showQuickBookWithRoom === 'function') {
                    console.log('BookInn: Using global reference for showQuickBookWithRoom');
                    window.BookInn.ManagementUnified.showQuickBookWithRoom(roomId, roomNumber, roomType);
                } else {
                    console.error('BookInn: showQuickBookWithRoom function not found');
                    alert('Error: Quick booking function not available. Please refresh the page.');
                }
            });

            // Calendar event handlers
            $(document).on('click', '.fc-event', function(e) {
                e.preventDefault();
                const eventId = $(this).data('event-id');
                if (eventId) {
                    self.showBookingDetails(eventId);
                }
            });

            // Calendar date click for standard booking modal
            $(document).on('click', '.fc-daygrid-day', function(e) {
                if (e.ctrlKey || e.metaKey) { // Only on Ctrl/Cmd + click
                    e.preventDefault();
                    const date = $(this).data('date');
                    if (date) {
                        self.showBookingModal(); // Use standard modal instead of quick book
                    }
                }
            });

            // Room type management from modal
            $(document).on('click', '#bookinn-add-room-type-in-modal', function(e) {
                e.preventDefault();
                self.showRoomTypeModal();
            });

            // Quick action event handlers
            $(document).on('click', '.bookinn-view-room-calendar, .bookinn-room-calendar', function(e) {
                e.preventDefault();
                const roomId = $(this).data('room-id');
                if (roomId) {
                    self.openRoomCalendar(roomId);
                }
            });

            // New Booking button handler - Standard modal only
            $(document).on('click.bookinnManagement', '#bookinn-add-booking, .bookinn-add-booking', function(e) {
                e.preventDefault();
                console.log('New booking button clicked - using standard modal');
                self.showBookingModal();
            });

            // Quick Booking button handler - Standard modal only  
            $(document).on('click.bookinnManagement', '#quick-new-booking, .quick-new-booking', function(e) {
                e.preventDefault();
                console.log('Quick booking button clicked - using standard modal');
                self.showBookingModal();
            });

            // Edit booking functionality removed

            // New Booking button - using standard modal

            // Delete booking
            $(document).on('click', '#bookinn-delete-booking', function(e) {
                e.preventDefault();
                const bookingId = $('#edit-booking-id').val();
                if (bookingId && confirm('Are you sure you want to delete this booking? This action cannot be undone.')) {
                    // Handle deletion via standard AJAX
                    self.deleteBooking(bookingId);
                }
            });

            // Load available rooms when dates change in new booking modal
            $(document).on('click', '.bookinn-room-card .select-room', function(e) {
                e.preventDefault();
                $('.bookinn-room-card').removeClass('selected');
                $(this).closest('.bookinn-room-card').addClass('selected');
                
                // Update summary
                const roomName = $(this).closest('.bookinn-room-card').find('h4').text();
                const roomPrice = $(this).closest('.bookinn-room-card').data('price');
                $('.bookinn-selected-room').text(roomName);
                $('.bookinn-room-price').text('$' + roomPrice);
                
                // Calculate total (simplified)
                const nights = 1; // This should be calculated based on dates
                const total = roomPrice * nights;
                $('.bookinn-total-amount').text('$' + total);
            });

            // Utility Functions

            // Load available rooms when dates change in new booking modal
            $(document).on('change', '#new-booking-checkin, #new-booking-checkout, #new-booking-adults, #new-booking-children', function() {
                console.log('BookInn: Date/guest field changed, triggering room loading');
                console.log('BookInn: Field that changed:', $(this).attr('id'), 'New value:', $(this).val());

                // Add delay to ensure datepicker has updated the input value
                setTimeout(function() {
                    if (self.loadAvailableRoomsForNewBooking && typeof self.loadAvailableRoomsForNewBooking === 'function') {
                        self.loadAvailableRoomsForNewBooking();
                    } else {
                        console.error('BookInn: loadAvailableRoomsForNewBooking function not available');
                    }
                }, 5000); // Increased delay from 100ms to 1000ms
            });

            // Calculate total when room selection changes
            $(document).on('change', '#new-booking-room-select-v2', function() {
                self.calculateBookingTotalFromRoomSelection();
            });



            // New Booking button handler
            // Standard modal handlers only

            // Apply filters button handler
            $(document).on('click.bookinnManagement', '#apply-filters', function(e) {
                e.preventDefault();
                console.log('Apply filters clicked');
                console.log('BookInn DEBUG: Current filter values:', {
                    status: $('#filter-status').val(),
                    dateFrom: $('#filter-date-from').val(),
                    dateTo: $('#filter-date-to').val(),
                    roomId: $('#filter-room').val()
                });
                self.applyBookingFilters();
            });

            // Clear filters button handler
            $(document).on('click.bookinnManagement', '#clear-filters', function(e) {
                e.preventDefault();
                console.log('Clear filters clicked');
                self.clearBookingFilters();
            });

            // Filter toggle button
            $(document).on('click.bookinnManagement', '#bookinn-booking-filters', function(e) {
                e.preventDefault();
                console.log('Filter toggle clicked');
                const $filterPanel = $('#bookinn-list-filters');
                if ($filterPanel.is(':visible')) {
                    $filterPanel.slideUp();
                } else {
                    $filterPanel.slideDown();
                    // Load rooms when opening filter panel
                    self.loadRoomsForFilter();
                }
            });

            // Refresh bookings button handler
            $(document).on('click.bookinnManagement', '#bookinn-refresh-bookings', function(e) {
                e.preventDefault();
                console.log('Refresh bookings clicked');
                self.refreshBookingsTable();
            });

            // Room filters toggle button
            $(document).on('click.bookinnManagement', '#bookinn-room-filters', function(e) {
                e.preventDefault();
                console.log('Room filter toggle clicked');
                const $filterPanel = $('#bookinn-room-list-filters');
                if ($filterPanel.is(':visible')) {
                    $filterPanel.slideUp();
                } else {
                    $filterPanel.slideDown();
                }
            });

            // Apply room filters button handler
            $(document).on('click.bookinnManagement', '#apply-room-filters', function(e) {
                e.preventDefault();
                console.log('Apply room filters clicked');
                if (typeof self !== 'undefined' && typeof self.performRoomSearch === 'function') {
                    self.performRoomSearch();
                } else if (window.BookInn && window.BookInn.ManagementUnified && typeof window.BookInn.ManagementUnified.performRoomSearch === 'function') {
                    window.BookInn.ManagementUnified.performRoomSearch();
                } else {
                    console.error('Room search function not found');
                }
            });

            // Clear room filters button handler
            $(document).on('click.bookinnManagement', '#clear-room-filters', function(e) {
                e.preventDefault();
                console.log('Clear room filters clicked');
                // Clear all filter inputs
                $('#rooms-status-filter').val('');
                $('#rooms-number-filter').val('');
                $('#rooms-type-filter').val('');
                // Trigger room search to refresh the list
                if (typeof self !== 'undefined' && typeof self.performRoomSearch === 'function') {
                    self.performRoomSearch();
                } else if (window.BookInn && window.BookInn.ManagementUnified && typeof window.BookInn.ManagementUnified.performRoomSearch === 'function') {
                    window.BookInn.ManagementUnified.performRoomSearch();
                }
            });

            // Refresh rooms button handler
            $(document).on('click.bookinnManagement', '#bookinn-refresh-rooms', function(e) {
                e.preventDefault();
                console.log('Refresh rooms clicked');
                if (typeof self !== 'undefined' && typeof self.performRoomSearch === 'function') {
                    self.performRoomSearch();
                } else if (window.BookInn && window.BookInn.ManagementUnified && typeof window.BookInn.ManagementUnified.performRoomSearch === 'function') {
                    window.BookInn.ManagementUnified.performRoomSearch();
                }
            });

            // Duplicate handler removed - using unified handler above

            // Chart filter handlers
            $(document).on('change', '#bookinn-revenue-period', function(e) {
                const period = $(this).val();
                console.log('Revenue chart period changed:', period);
                self.loadChartData('revenue', period);
            });

            $(document).on('change', '#bookinn-forecast-period', function(e) {
                const period = $(this).val();
                console.log('Forecast chart period changed:', period);
                self.loadChartData('forecast', period);
            });

            $(document).on('change', '.bookinn-chart-filter', function(e) {
                const chartType = $(this).data('chart');
                const period = $(this).val();
                console.log('Chart filter changed:', chartType, period);
                self.loadChartData(chartType, period);
            });

            // Booking Days chart filter
            $(document).on('change', '#booking-days-period', function(e) {
                const period = $(this).val();
                console.log('Booking Days chart period changed:', period);
                self.loadChartData('booking-days', period);
            });

            // Booking Rates chart filter
            $(document).on('change', '#booking-rates-period', function(e) {
                const period = $(this).val();
                console.log('Booking Rates chart period changed:', period);
                self.loadChartData('booking-rates', period);
            });

            // Report period change handler
            $(document).on('change', '#report-period', function(e) {
                const period = $(this).val();
                console.log('Report period changed:', period);
                // Reports data loading is now handled by PHP server-side rendering
            });

            // Global Escape key handler for modals
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && self.ModalManager.activeModal) {
                    e.preventDefault();
                    self.ModalManager.hide(self.ModalManager.activeModal);
                }
            });

            // View booking handler
            $(document).on('click', '.bookinn-view-booking', function(e) {
                e.preventDefault();
                const bookingId = $(this).data('booking-id');
                console.log('View booking clicked:', bookingId);
                self.viewBooking(bookingId);
            });

            // Edit booking handler (table or view modal)
            $(document).on('click', '.bookinn-edit-booking', function(e) {
                e.preventDefault();
                const $btn = $(this);
                // Se il bottone è dentro il modal view, usa bookingData già presente
                if ($btn.closest('#bookinn-booking-view-modal').length > 0) {
                    const bookingData = $('#bookinn-booking-view-modal').data('booking-data');
                    if (bookingData) {
                        // Chiudi la modale view
                        if (window.BookInn && window.BookInn.Core && window.BookInn.Core.ModalSystem && typeof window.BookInn.Core.ModalSystem.hide === 'function') {
                            window.BookInn.Core.ModalSystem.hide('#bookinn-booking-view-modal');
                        } else {
                            $('#bookinn-booking-view-modal').removeClass('is-active');
                            $('body').removeClass('bookinn-modal-open');
                        }
                        // Mostra la modale edit con i dati già presenti
                        setTimeout(function() {
                            window.BookInn.ManagementUnified.showBookingEditModal(bookingData);
                        }, 100);
                    } else {
                        alert('Error: No booking data found in view modal');
                    }
                    return;
                }
                // Altrimenti, gestione classica da tabella
                const bookingId = $btn.data('booking-id');
                if (!bookingId) {
                    alert('Error: No booking ID found');
                    return;
                }
                // Crea e mostra subito la modale edit in stato loading
                $('#bookinn-booking-edit-modal').remove();
                const loadingHtml = `
                    <div id="bookinn-booking-edit-modal" class="bookinn-modal is-active">
                        <div class="bookinn-modal-overlay"></div>
                        <div class="bookinn-modal-container bookinn-modal-wide" style="min-height:200px;display:flex;align-items:center;justify-content:center;">
                            <div class="bookinn-modal-loader" style="text-align:center;width:100%;">
                                <div class="spinner" style="margin:0 auto 1em auto;width:2em;height:2em;border:4px solid #ccc;border-top:4px solid #3498db;border-radius:50%;animation:spin 1s linear infinite;"></div>
                                <div>Loading booking data...</div>
                            </div>
                        </div>
                    </div>
                `;
                $('body').append(loadingHtml);
                $('body').addClass('bookinn-modal-open');
                self.loadBookingData(bookingId).then(function(bookingData) {
                    $('#bookinn-booking-edit-modal').remove();
                    self.showBookingEditModal(bookingData);
                }).catch(function(error) {
                    $('#bookinn-booking-edit-modal').remove();
                    $('body').removeClass('bookinn-modal-open');
                    alert('Error loading booking data for edit: ' + error);
                });
            });

            // Check-in/Check-out handlers
            $(document).on('click', '.bookinn-checkin-btn, [data-action="checkin"]', function(e) {
                e.preventDefault();
                const bookingId = $(this).data('booking-id');
                console.log('Check-in clicked for booking:', bookingId);
                self.performCheckin(bookingId);
            });

            $(document).on('click', '.bookinn-checkout-btn, [data-action="checkout"]', function(e) {
                e.preventDefault();
                const bookingId = $(this).data('booking-id');
                console.log('Check-out clicked for booking:', bookingId);
                self.performCheckout(bookingId);
            });

            // Room status management
            $(document).on('click', '.bookinn-room-status-btn, [data-action="room-status"]', function(e) {
                e.preventDefault();
                const roomId = $(this).data('room-id');
                console.log('Room status clicked for room:', roomId);
                self.showRoomStatusModal(roomId);
            });

            console.log('BookInn Management Unified: Event handlers initialized');
            console.log('[BookInn DEBUG] Edit booking event handler bound to: .bookinn-edit-booking');
        },

        /**
         * Initialize tab system
         */
        initTabs: function() {
            // Set initial active tab
            const $firstTab = $('.bookinn-tab-link').first();
            if ($firstTab.length) {
                const firstTabId = $firstTab.data('tab');
                this.switchTab(firstTabId);
            }
        },

        /**
         * Initialize modal system
         */
        initModals: function() {
            // Use the unified modal system from BookInn.Core
            if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                console.log('BookInn Management Unified: Using unified modal system');
            } else {
                console.warn('BookInn Management Unified: Unified modal system not available, falling back');
            }
        },

        /**
         * Initialize data loading
         */
        initDataLoading: function() {
            // Set up auto-refresh if needed
            this.setupAutoRefresh();
        },

        /**
         * Load initial content for all tabs
         */
        loadInitialContent: function() {
            // Load static content first for immediate display
            this.loadStaticDashboard();
            this.loadStaticBookings();
            this.loadStaticRooms();

            // Delay calendar loading to ensure DOM is ready
            setTimeout(() => {
                this.loadStaticCalendar();
            }, 100);

            // Reports are now rendered by PHP, no JavaScript loading needed

            // Then load dynamic content in background
            setTimeout(() => {
                this.loadDynamicContent();
            }, 500);
        },

        /**
         * Switch between main tabs
         */
        switchTab: function(tabId) {
            console.log('BookInn Management Unified: Switching to tab:', tabId);

            // Update tab navigation
            $('.bookinn-tab-link').removeClass('active');
            $(`.bookinn-tab-link[data-tab="${tabId}"]`).addClass('active');

            // Update tab content - use correct selectors
            $('.bookinn-tab-panel').removeClass('active');
            $(`#tab-${tabId}`).addClass('active');

            // Update ARIA attributes for accessibility
            $('.bookinn-tab-link').attr('aria-selected', 'false');
            $(`.bookinn-tab-link[data-tab="${tabId}"]`).attr('aria-selected', 'true');

            $('.bookinn-tab-panel').attr('aria-hidden', 'true');
            $(`#tab-${tabId}`).attr('aria-hidden', 'false');

            // Load tab-specific content
            this.loadTabContent(tabId);

            // Trigger tab change event
            $(document).trigger('bookinn:tab:changed', [tabId]);
        },

        /**
         * Switch between sub-tabs
         */
        switchSubTab: function(subTabId) {
            console.log('BookInn Management Unified: Switching to sub-tab:', subTabId);
            
            // Update sub-tab navigation
            $('.bookinn-sub-tab-link').removeClass('active');
            $(`.bookinn-sub-tab-link[data-tab="${subTabId}"]`).addClass('active');
            
            // Update sub-tab content
            $('.bookinn-sub-tab-panel').removeClass('active');
            $(`#${subTabId}`).addClass('active');
            
            // Load sub-tab specific content
            this.loadSubTabContent(subTabId);
        },

        /**
         * Load content for specific tab
         */
        loadTabContent: function(tabId) {
            switch(tabId) {
                case 'dashboard':
                    this.ensureDashboardContent();
                    break;
                case 'bookings':
                    this.ensureBookingsContent();
                    break;
                case 'rooms':
                    this.ensureRoomsContent();
                    break;
                case 'calendar':
                    this.ensureCalendarContent();
                    break;
                case 'reports':
                    // Reports content is rendered by PHP, but charts need JavaScript initialization
                    console.log('Reports tab activated - initializing charts');
                    this.initializeReportsCharts();
                    break;
            }
        },

        /**
         * Load content for specific sub-tab
         */
        loadSubTabContent: function(subTabId) {
            switch(subTabId) {
                case 'rooms-list':
                    this.loadRoomsData();
                    break;
                case 'room-types':
                    this.loadRoomTypesData();
                    break;
                case 'calendar-main':
                    // Standard FullCalendar initialization
                    this.initializeFullCalendar();
                    break;
            }
        },

        // ===== CONTENT LOADING METHODS =====

        /**
         * Ensure dashboard content is loaded
         */
        ensureDashboardContent: function() {
            const $container = $('#tab-dashboard, #dashboard');

            // Check for any existing dashboard content, not just .bookinn-dashboard-content
            const existingContent = $container.find('.bookinn-dashboard-content, .bookinn-metrics-grid, .bookinn-charts-section, .bookinn-dashboard-bottom-row');

            if (existingContent.length === 0) {
                this.loadStaticDashboard();
            } else {
                console.log('BookInn Management Unified: Dashboard content already present, preserving existing content');
            }
        },

        /**
         * Ensure bookings content is loaded
         */
        ensureBookingsContent: function() {
            const $container = $('#tab-bookings, #bookings');
            const existingContent = $container.find('.bookinn-bookings-container, .bookinn-bookings-content, .bookinn-section-header');

            if (existingContent.length === 0) {
                this.loadStaticBookings();
            } else {
                console.log('BookInn Management Unified: Bookings content already present, preserving existing content');
            }
        },

        /**
         * Ensure rooms content is loaded
         */
        ensureRoomsContent: function() {
            const $container = $('#tab-rooms, #rooms');
            const existingContent = $container.find('.bookinn-rooms-container, .bookinn-rooms-content, .bookinn-sub-tabs');

            if (existingContent.length === 0) {
                this.loadStaticRooms();
            } else {
                console.log('BookInn Management Unified: Rooms content already present, preserving existing content');
            }

            // Always load room types for the filter dropdown when rooms tab is accessed
            this.loadRoomTypesData()
                .then(() => {
                    console.log('BookInn: Room types loaded for filter dropdown');
                })
                .catch((error) => {
                    console.error('BookInn: Failed to load room types for filter:', error);
                });
        },

        /**
         * Ensure calendar content is loaded
         */
        ensureCalendarContent: function() {
            const $container = $('#tab-calendar, #calendar');
            // Check for standardized PHP-generated calendar container structure
            const existingContent = $container.find('.bookinn-calendar-container');

            if (existingContent.length === 0) {
                this.loadStaticCalendar();
            } else {
                console.log('BookInn Management Unified: Calendar content already present (PHP-generated), preserving existing content');
                // Initialize calendar if it exists but hasn't been initialized
                this.initializeFullCalendar();
                this.bindCalendarEvents();
            }
        },



        // ===== STATIC CONTENT LOADERS =====

        /**
         * Load static dashboard content
         */
        loadStaticDashboard: function() {
            console.log('BookInn Management Unified: Loading static dashboard...');
            const $container = $('#tab-dashboard, #dashboard');

            if (!$container.length) {
                console.warn('Dashboard container not found');
                return;
            }

            // Check if dashboard already has content - if so, don't override it
            const existingContent = $container.find('.bookinn-dashboard-content, .bookinn-metrics-grid, .bookinn-charts-section, .bookinn-dashboard-bottom-row');
            if (existingContent.length > 0) {
                console.log('BookInn Management Unified: Dashboard content already exists, preserving it');
                return;
            }

            // Only add loading text if container is completely empty
            if ($container.children().length === 0) {
                $container.html('<div class="bookinn-dashboard-content"><p>Dashboard loading...</p></div>');
            }
        },

        /**
         * Load static bookings content
         */
        loadStaticBookings: function() {
            console.log('BookInn Management Unified: Loading static bookings...');
            const $container = $('#tab-bookings, #bookings');

            if (!$container.length) {
                console.warn('Bookings container not found');
                return;
            }

            // Check if bookings content already exists - look for actual container classes
            const existingContent = $container.find('.bookinn-bookings-container, .bookinn-bookings-content, .bookinn-section-header, .bookinn-table-container');
            if (existingContent.length > 0) {
                console.log('BookInn Management Unified: Bookings content already exists, preserving it');
                return;
            }

            // Only add loading text if container is completely empty
            if ($container.children().length === 0) {
                $container.html('<div class="bookinn-bookings-content"><p>Bookings loading...</p></div>');
            }
        },

        /**
         * Load static rooms content
         */
        loadStaticRooms: function() {
            console.log('BookInn Management Unified: Loading static rooms...');
            const $container = $('#tab-rooms, #rooms');

            if (!$container.length) {
                console.warn('Rooms container not found');
                return;
            }

            // Check if rooms content already exists - look for actual container classes
            const existingContent = $container.find('.bookinn-rooms-container, .bookinn-rooms-content, .bookinn-sub-tabs, .bookinn-section-header');
            if (existingContent.length > 0) {
                console.log('BookInn Management Unified: Rooms content already exists, preserving it');
                return;
            }

            // Only add loading text if container is completely empty
            if ($container.children().length === 0) {
                $container.html('<div class="bookinn-rooms-content"><p>Rooms loading...</p></div>');
            }
        },

        /**
         * Load static calendar content - aligned with PHP rendering structure
         */
        loadStaticCalendar: function() {
            console.log('BookInn Management Unified: Loading static calendar...');
            const $container = $('#tab-calendar, #calendar');

            if (!$container.length) {
                console.warn('Calendar container not found');
                return;
            }

            // Check if calendar content already exists - look for PHP-generated content first
            const existingContent = $container.find('.bookinn-calendar-container');
            const phpGeneratedFilters = $container.find('#room-filter, #room-type-filter, #status-filter, #availability-filter');

            if (existingContent.length > 0 || phpGeneratedFilters.length > 0) {
                console.log('BookInn Management Unified: Calendar content already exists (PHP-generated), preserving it');
                // If PHP-generated content exists, just initialize the calendar without replacing HTML
                this.initializeFullCalendar();
                this.bindCalendarEvents();
                return;
            }

            // Create standardized calendar content that matches PHP structure
            const calendarHTML = `
                <div class="bookinn-calendar-container">
                    <!-- Calendar Header -->
                    <div class="bookinn-section-header">
                        <h3><i class="dashicons dashicons-calendar-alt"></i> Booking Calendar</h3>
                    </div>

                    <!-- Calendar Filters -->
                    <div class="bookinn-calendar-filters">
                        <div class="filter-group">
                            <label>Room Filter:</label>
                            <select id="room-filter" class="bookinn-select">
                                <option value="">All Rooms</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Room Type Filter:</label>
                            <select id="room-type-filter" class="bookinn-select">
                                <option value="">All Types</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Status Filter:</label>
                            <select id="status-filter" class="bookinn-select">
                                <option value="">All Statuses</option>
                                <option value="confirmed">Confirmed</option>
                                <option value="pending">Pending</option>
                                <option value="checked_in">Checked In</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label>Availability Filter:</label>
                            <select id="availability-filter" class="bookinn-select">
                                <option value="bookings">Show Bookings</option>
                                <option value="availability">Show Availability</option>
                                <option value="both">Show Both</option>
                            </select>
                        </div>

                        <div class="bookinn-calendar-legend">
                            <span class="bookinn-legend-item">
                                <span class="bookinn-legend-color bookinn-status-confirmed"></span>
                                Confirmed
                            </span>
                            <span class="bookinn-legend-item">
                                <span class="bookinn-legend-color bookinn-status-pending"></span>
                                Pending
                            </span>
                            <span class="bookinn-legend-item">
                                <span class="bookinn-legend-color bookinn-status-checked_in"></span>
                                Checked In
                            </span>
                        </div>
                    </div>

                    <!-- FullCalendar Container -->
                    <div class="bookinn-calendar-wrapper">
                        <div id="bookinn-calendar" class="bookinn-calendar-widget">
                            <div class="calendar-loading">
                                <i class="dashicons dashicons-update-alt"></i>
                                <p>Loading calendar...</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $container.html(calendarHTML);
            
            // Initialize calendar after content is added
            setTimeout(() => {
                this.initializeFullCalendar();
                this.bindCalendarEvents();
            }, 100);
        },



        /**
         * Load dynamic content via AJAX
         */
        loadDynamicContent: function() {
            console.log('BookInn Management Unified: Loading dynamic content...');

            // Load real data from API
            this.loadRoomsData();
            this.loadRoomTypesData();
            this.loadBookingsData();
            // Reports data is now loaded via PHP server-side rendering
        },

        /**
         * Setup auto-refresh functionality
         */
        setupAutoRefresh: function() {
            // Auto-refresh every 5 minutes
            setInterval(() => {
                if (this._initialized) {
                    this.refreshCurrentTabData();
                }
            }, 300000); // 5 minutes
        },

        /**
         * Refresh current tab data
         */
        refreshCurrentTabData: function() {
            const activeTab = $('.bookinn-tab-link.active').data('tab');
            if (activeTab) {
                this.loadTabContent(activeTab);
            }
        },

        // ===== UNIFIED MODAL MANAGEMENT SYSTEM =====

        /**
         * Unified Modal Manager 
         */
        ModalManager: {
            show: function(modalElement) {
                const $modal = $(modalElement);
                console.log('ModalManager: Showing modal', $modal.attr('id'));
                
                // Ensure modal has proper structure and classes
                if (!$modal.hasClass('bookinn-modal')) {
                    $modal.addClass('bookinn-modal');
                }
                
                // Force display and opacity
                $modal.css({
                    'display': 'flex',
                    'opacity': '1',
                    'z-index': '10000'
                }).addClass('is-active');
                
                $('body').addClass('bookinn-modal-open');
                
                // Debug info
                setTimeout(() => {
                    console.log('ModalManager: Modal status check', {
                        id: $modal.attr('id'),
                        display: $modal.css('display'),
                        opacity: $modal.css('opacity'),
                        zIndex: $modal.css('z-index'),
                        hasIsActive: $modal.hasClass('is-active'),
                        visible: $modal.is(':visible')
                    });
                }, 100);
            },

            hide: function(modalSelector) {
                const $modal = $(modalSelector);
                console.log('ModalManager: Hiding modal', modalSelector);
                
                $modal.removeClass('is-active').css({
                    'display': 'none',
                    'opacity': '0'
                });
                
                $('body').removeClass('bookinn-modal-open');
            },

            hideAll: function() {
                $('.bookinn-modal.is-active').removeClass('is-active');
                $('body').removeClass('bookinn-modal-open');
            }
        },

        // ===== MODAL MANAGEMENT METHODS =====

        /**
         * Show room modal for adding/editing rooms
         */
        showRoomModal: function(roomData = null, showLoading = false) {
            console.log('BookInn: showRoomModal called', {roomData, showLoading});
            
            try {
                // Remove any existing room modal to prevent duplicates and event issues
                $('#bookinn-room-modal').remove();
                let modal;
                console.log('BookInn: Ensuring only one room modal exists.');
                // Always create a new modal
                const modalHtml = this.createRoomModalHTML();
                $('body').append(modalHtml);
                modal = $('#bookinn-room-modal');
                console.log('BookInn: Modal created, length:', modal.length);

                // Populate room type select when modal is first created
                console.log('BookInn: Populating room type select...');
                this.populateRoomTypeSelect();

                // Show loading state or populate form
                if (showLoading) {
                    this.showModalLoading(modal);
                    modal.find('.bookinn-modal-header h4').text('Loading Room Data...');
                } else if (roomData) {
                    this.populateRoomForm(roomData);
                    modal.find('.bookinn-modal-header h4').text('Edit Room');
                } else {
                    this.clearRoomForm();
                    modal.find('.bookinn-modal-header h4').text('Add New Room');
                }

                console.log('BookInn: About to show modal with unified manager...');
                
                // Initialize image upload functionality
                this.initImageUploads();
                
                // Use unified modal manager
                if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                    window.BookInn.Core.ModalSystem.show(modal[0]);
                } else {
                    // Fallback
                    modal.addClass('is-active');
                    $('body').addClass('bookinn-modal-open');
                }

            } catch (error) {
                console.error('Error showing room modal:', error);
                alert('Error opening room form. Please try again.');
            }
        },

        hideRoomModal: function() {
            console.log('BookInn: hideRoomModal called');
            const modal = document.getElementById('bookinn-room-modal');
            if (modal) {
                if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                    window.BookInn.Core.ModalSystem.hide(modal);
                } else {
                    // Fallback
                    $(modal).removeClass('is-active').hide();
                    $('body').removeClass('bookinn-modal-open');
                }
            }
        },

        /**
         * Show booking modal for adding new bookings only
         */
        showBookingModal: function() {
            try {
                let modal = $('#bookinn-booking-modal');

                // Create modal if it doesn't exist
                if (modal.length === 0) {
                    const modalHtml = this.createBookingModalHTML();
                    $('body').append(modalHtml);
                    modal = $('#bookinn-booking-modal');
                }

                // Always clear form for new booking
                this.clearBookingForm();
                modal.find('.bookinn-modal-header h4').text('Add New Booking');

                // Imposta le date di default solo una volta al caricamento iniziale
                const $checkin = $('#new-booking-checkin');
                const $checkout = $('#new-booking-checkout');
                const $adults = $('#new-booking-adults');
                
                // Set default dates only if fields are empty
                if (!$checkin.val() || $checkin.val() === '') {
                    const today = new Date();
                    const checkinDate = today.toISOString().split('T')[0];
                    $checkin.val(checkinDate);
                    console.log('BookInn: Set default check-in date:', checkinDate);
                }
                
                if (!$checkout.val() || $checkout.val() === '') {
                    const tomorrow = new Date();
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    const checkoutDate = tomorrow.toISOString().split('T')[0];
                    $checkout.val(checkoutDate);
                    console.log('BookInn: Set default check-out date:', checkoutDate);
                }
                
                // Set default adults only if field is empty
                if (!$adults.val() || $adults.val() === '') {
                    $adults.val('1');
                    console.log('BookInn: Set default adults: 1');
                }

                // Debug: Log current field values after setting defaults
                console.log('BookInn: Modal field values after defaults set:', {
                    checkin: $checkin.val(),
                    checkout: $checkout.val(),
                    adults: $adults.val(),
                    children: $('#new-booking-children').val()
                });

                // Carica le camere disponibili per le date correnti (scelte utente o default)
                console.log('BookInn: Scheduling room loading for current dates');
                setTimeout(() => {
                    console.log('BookInn: Executing scheduled room loading');
                    if (this.loadAvailableRoomsForNewBooking && typeof this.loadAvailableRoomsForNewBooking === 'function') {
                        this.loadAvailableRoomsForNewBooking();
                    } else {
                        console.error('BookInn: loadAvailableRoomsForNewBooking function not available in modal context');
                    }
                }, 100);

                // Initialize availability check functionality
                this.initializeAvailabilityCheck();

                // Show modal using unified system
                if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                    window.BookInn.Core.ModalSystem.show(modal[0]);
                } else {
                    // Fallback
                    modal.addClass('is-active');
                    $('body').addClass('bookinn-modal-open');
                }

            } catch (error) {
                console.error('Error showing booking modal:', error);
                alert('Error opening booking form. Please try again.');
            }
        },

        /**
         * Show room type modal for adding/editing room types
         */
        showRoomTypeModal: function(roomTypeData = null, showLoading = false) {
            try {
                // Remove any existing room type modal to prevent duplicates and event issues
                $('#bookinn-room-type-modal').remove();
                let modal;
                // Always create a new modal
                const modalHtml = this.createRoomTypeModalHTML();
                $('body').append(modalHtml);
                modal = $('#bookinn-room-type-modal');

                // Show loading state or populate form
                if (showLoading) {
                    this.showModalLoading(modal);
                    modal.find('.bookinn-modal-header h4').text('Loading Room Type Data...');
                } else if (roomTypeData) {
                    this.populateRoomTypeForm(roomTypeData);
                    modal.find('.bookinn-modal-header h4').text('Edit Room Type');
                } else {
                    this.clearRoomTypeForm();
                    modal.find('.bookinn-modal-header h4').text('Add New Room Type');
                }

                // Use global modal system for consistent behavior
                if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                    window.BookInn.Core.ModalSystem.show(modal[0]);
                } else {
                    // Fallback
                    modal.addClass('is-active');
                    $('body').addClass('bookinn-modal-open');
                }

            } catch (error) {
                console.error('Error showing room type modal:', error);
                alert('Error opening room type form. Please try again.');
            }
        },

        hideRoomTypeModal: function() {
            const modal = document.getElementById('bookinn-room-type-modal');
            if (modal) {
                if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                    window.BookInn.Core.ModalSystem.hide(modal);
                } else {
                    // Fallback
                    $(modal).removeClass('is-active').hide();
                    $('body').removeClass('bookinn-modal-open');
                }
            }
        },

        /**
         * Show room types management modal (list view)
         */
        showRoomTypesManagementModal: function() {
            try {
                let modal = $('#bookinn-room-types-management-modal');

                // Create modal if it doesn't exist
                if (modal.length === 0) {
                    const modalHtml = this.createRoomTypesManagementModalHTML();
                    $('body').append(modalHtml);
                    modal = $('#bookinn-room-types-management-modal');
                }

                // Load room types data
                this.loadRoomTypesInModal();

                // Use consistent modal system
                modal.addClass('is-active');
                $('body').addClass('bookinn-modal-open');

            } catch (error) {
                console.error('Error showing room types management modal:', error);
                alert('Error opening room types management. Please try again.');
            }
        },

        /**
         * Edit room
         */
        editRoom: function(roomId) {
            console.log('BookInn Management Unified: Editing room:', roomId);

            // Show modal immediately with loading state
            this.showRoomModal(null, true); // true = loading state

            // Load room data and populate modal
            this.loadRoomData(roomId).then(roomData => {
                this.populateRoomForm(roomData);
                this.hideModalLoading('#bookinn-room-modal');
                $('#bookinn-room-modal .bookinn-modal-header h4').text('Edit Room');
            }).catch(error => {
                console.error('Error loading room data:', error);
                this.hideModalLoading('#bookinn-room-modal');
                this.showModalError('#bookinn-room-modal', 'Error loading room data: ' + error);
            });
        },

        /**
         * Edit room type
         */
        editRoomType: function(roomTypeId) {
            console.log('BookInn Management Unified: Editing room type:', roomTypeId);

            // Show modal immediately with loading state
            this.showRoomTypeModal(null, true); // true = loading state

            // Load room type data and populate modal
            this.loadRoomTypeData(roomTypeId).then(roomTypeData => {
                this.populateRoomTypeForm(roomTypeData);
                this.hideModalLoading('#bookinn-room-type-modal');
                $('#bookinn-room-type-modal .bookinn-modal-header h4').text('Edit Room Type');
            }).catch(error => {
                console.error('Error loading room type data:', error);
                this.hideModalLoading('#bookinn-room-type-modal');
                this.showModalError('#bookinn-room-type-modal', 'Error loading room type data: ' + error);
            });
        },

        /**
         * Save room
         */
        saveRoom: function() {
            console.log('BookInn Management Unified: Saving room...');

            const formData = this.collectRoomFormData();
            if (!this.validateRoomData(formData)) {
                return;
            }

            this.showSaveLoading('#save-room');

            // Save via AJAX
            this.saveRoomData(formData).then(response => {
                this.hideSaveLoading('#save-room');
                this.showSuccessMessage('Room saved successfully!');
                this.closeActiveModal();
                this.refreshRoomsData();
            }).catch(error => {
                this.hideSaveLoading('#save-room');
                console.error('Error saving room:', error);
                alert('Error saving room. Please try again.');
            });
        },

        /**
         * Save room type
         */
        saveRoomType: function() {
            console.log('BookInn Management Unified: Saving room type...');

            const formData = this.collectRoomTypeFormData();
            if (!this.validateRoomTypeData(formData)) {
                return;
            }

            this.showSaveLoading('#save-room-type');

            // Save via AJAX
            this.saveRoomTypeData(formData).then(response => {
                this.hideSaveLoading('#save-room-type');
                this.showSuccessMessage('Room type saved successfully!');
                this.closeActiveModal();
                this.refreshRoomTypesData();
            }).catch(error => {
                this.hideSaveLoading('#save-room-type');
                console.error('Error saving room type:', error);
                alert('Error saving room type. Please try again.');
            });
        },

        /**
         * Close active modal
         */
        closeActiveModal: function() {
            if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                window.BookInn.Core.ModalSystem.hide();
            } else {
                $('.bookinn-modal.is-active').removeClass('is-active');
                $('body').removeClass('bookinn-modal-open');
            }
        },

        // ===== MODAL HTML CREATION METHODS =====

        /**
         * Create room modal HTML
         */
        createRoomModalHTML: function() {
            return `
                <div id="bookinn-room-modal" class="bookinn-modal" role="dialog" aria-labelledby="room-modal-title" tabindex="-1">
                    <div class="bookinn-modal-overlay"></div>
                    <div class="bookinn-modal-container bookinn-modal-wide">
                        <div class="bookinn-modal-header">
                            <h4 id="room-modal-title">Add New Room</h4>
                            <button class="bookinn-modal-close" aria-label="Close dialog">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <div class="bookinn-room-form-content">
                                <form id="room-form">
                                    <input type="hidden" id="room-id" name="room_id" value="">

                                    <!-- Room Basic Information Section -->
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-door-open"></i> Room Information</h5>
                                        <div class="bookinn-form-grid">
                                            <div class="bookinn-form-group">
                                                <label for="room-number">Room Number *</label>
                                                <input type="text" id="room-number" name="room_number" class="bookinn-input" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="room-name">Room Name</label>
                                                <input type="text" id="room-name" name="room_name" class="bookinn-input">
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="room-type-select">Room Type *</label>
                                                <select id="room-type-select" name="room_type_id" class="bookinn-select" required>
                                                    <option value="">Select Room Type</option>
                                                </select>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="room-floor">Floor</label>
                                                <select id="room-floor" name="floor" class="bookinn-select">
                                                    <option value="1">1st Floor</option>
                                                    <option value="2">2nd Floor</option>
                                                    <option value="3">3rd Floor</option>
                                                    <option value="4">4th Floor</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Room Status Section -->
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-cog"></i> Room Status</h5>
                                        <div class="bookinn-form-grid">
                                            <div class="bookinn-form-group">
                                                <label for="room-status">Status</label>
                                                <select id="room-status" name="status" class="bookinn-select">
                                                    <option value="available">Available</option>
                                                    <option value="occupied">Occupied</option>
                                                    <option value="maintenance">Maintenance</option>
                                                    <option value="cleaning">Cleaning</option>
                                                    <option value="out_of_order">Out of Order</option>
                                                </select>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label>
                                                    <input type="checkbox" id="room-active" name="is_active" checked>
                                                    Active
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Room Images Section -->
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-images"></i> Room Images</h5>
                                        
                                        <!-- Primary Image Upload -->
                                        <div class="bookinn-form-group">
                                            <label for="room-primary-image">Primary Image</label>
                                            <div class="bookinn-image-upload-container">
                                                <input type="file" id="room-primary-image" name="primary_pic" 
                                                       class="bookinn-file-input" accept="image/*" style="display: none;">
                                                <div class="bookinn-image-upload-area" data-target="room-primary-image">
                                                    <div class="bookinn-upload-placeholder">
                                                        <i class="fas fa-cloud-upload-alt"></i>
                                                        <p>Click to upload primary image or drag & drop</p>
                                                        <small>Supported formats: JPG, PNG, WebP (Max: 5MB)</small>
                                                    </div>
                                                </div>
                                                <div class="bookinn-image-preview" id="primary-image-preview" style="display: none;">
                                                    <img src="" alt="Primary image preview" class="bookinn-room-thumb">
                                                    <div class="bookinn-image-actions">
                                                        <button type="button" class="bookinn-btn bookinn-btn-sm bookinn-btn-danger remove-image" 
                                                                data-target="primary">
                                                            <i class="fas fa-trash"></i> Remove
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Secondary Images Upload -->
                                        <div class="bookinn-form-group">
                                            <label for="room-secondary-images">Additional Images</label>
                                            <div class="bookinn-image-upload-container">
                                                <input type="file" id="room-secondary-images" name="secondary_pics[]" 
                                                       class="bookinn-file-input" accept="image/*" multiple style="display: none;">
                                                <div class="bookinn-image-upload-area" data-target="room-secondary-images">
                                                    <div class="bookinn-upload-placeholder">
                                                        <i class="fas fa-images"></i>
                                                        <p>Click to upload additional images or drag & drop</p>
                                                        <small>You can select multiple images (Max: 10 images, 5MB each)</small>
                                                    </div>
                                                </div>
                                                <div class="bookinn-images-grid" id="secondary-images-preview"></div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-close-modal">Cancel</button>
                            <button type="button" id="save-room" class="bookinn-btn bookinn-btn-primary">Save Room</button>
                        </div>
                    </div>
                </div>
            `;
        },

        /**
         * Create room type modal HTML
         */
        createRoomTypeModalHTML: function() {
            return `
                <div id="bookinn-room-type-modal" class="bookinn-modal" role="dialog" aria-labelledby="room-type-modal-title" tabindex="-1">
                    <div class="bookinn-modal-overlay"></div>
                    <div class="bookinn-modal-container bookinn-modal-wide">
                        <div class="bookinn-modal-header">
                            <h4 id="room-type-modal-title">Add New Room Type</h4>
                            <button class="bookinn-modal-close" aria-label="Close dialog">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <div class="bookinn-room-type-form-content">
                                <form id="room-type-form">
                                    <input type="hidden" id="room-type-id" name="room_type_id" value="">

                                    <!-- Basic Information Section -->
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-home"></i> Basic Information</h5>
                                        <div class="bookinn-form-grid">
                                            <div class="bookinn-form-group">
                                                <label for="room-type-name">Type Name *</label>
                                                <input type="text" id="room-type-name" name="name" class="bookinn-input" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="room-type-base-price">Base Price (€) *</label>
                                                <input type="number" id="room-type-base-price" name="base_price" class="bookinn-input" step="0.01" min="0" required>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="room-type-max-guests">Max Guests *</label>
                                                <input type="number" id="room-type-max-guests" name="max_guests" class="bookinn-input" min="1" max="10" required>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Description & Amenities Section -->
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-list"></i> Details & Amenities</h5>
                                        <div class="bookinn-form-group full-width">
                                            <label for="room-type-description">Description</label>
                                            <textarea id="room-type-description" name="description" class="bookinn-textarea" rows="3"></textarea>
                                        </div>
                                        <div class="bookinn-form-group full-width">
                                            <label for="room-type-amenities">Amenities</label>
                                            <input type="text" id="room-type-amenities" name="amenities" class="bookinn-input" placeholder="WiFi, TV, Air Conditioning, etc.">
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-close-modal">Cancel</button>
                            <button type="button" id="save-room-type" class="bookinn-btn bookinn-btn-primary">Save Room Type</button>
                        </div>
                    </div>
                </div>
            `;
        },

        /**
         * Create room types management modal HTML (list view)
         */
        createRoomTypesManagementModalHTML: function() {
            return `
                <div id="bookinn-room-types-management-modal" class="bookinn-modal bookinn-modal-xl" role="dialog" aria-labelledby="room-types-management-title" tabindex="-1">
                    <div class="bookinn-modal-content">
                        <div class="bookinn-modal-header">
                            <h4 id="room-types-management-title">Manage Room Types</h4>
                            <button class="bookinn-close-modal" aria-label="Close dialog">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <div class="bookinn-section-header">
                                <button id="bookinn-add-room-type-in-modal" class="bookinn-btn bookinn-btn-primary">
                                    <span class="bookinn-icon">+</span>
                                    Add Room Type
                                </button>
                            </div>
                            <div class="bookinn-table-container">
                                <table class="bookinn-table" id="bookinn-room-types-modal-table">
                                    <thead>
                                        <tr>
                                            <th>Type Name</th>
                                            <th>Description</th>
                                            <th>Base Price</th>
                                            <th>Max Guests</th>
                                            <th>Rooms Count</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr><td colspan="6" class="bookinn-loading">Loading room types...</td></tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-close-modal">Close</button>
                        </div>
                    </div>
                </div>
            `;
        },

        /**
         * Create booking modal HTML New Booking form
         */
        createBookingModalHTML: function() {
            return `
                <div id="bookinn-booking-modal" class="bookinn-modal" role="dialog" aria-labelledby="booking-modal-title" tabindex="-1">
                    <div class="bookinn-modal-overlay"></div>
                    <div class="bookinn-modal-container bookinn-modal-wide">
                        <div class="bookinn-modal-header">
                            <h4 id="booking-modal-title">Add New Booking</h4>
                            <button class="bookinn-modal-close" aria-label="Close dialog">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <form id="bookinn-booking-form" class="bookinn-form">
                                <input type="hidden" id="booking-id" name="booking_id" value="">
                                
                                <div class="bookinn-form-grid">
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-checkin">Check-in Date *</label>
                                        <input type="date" id="new-booking-checkin" name="check_in_date" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-checkout">Check-out Date *</label>
                                        <input type="date" id="new-booking-checkout" name="check_out_date" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-adults">Adults *</label>
                                        <select id="new-booking-adults" name="adults" class="bookinn-select" required>
                                            <option value="1">1 Adult</option>
                                            <option value="2">2 Adults</option>
                                            <option value="3">3 Adults</option>
                                            <option value="4">4 Adults</option>
                                        </select>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-children">Children</label>
                                        <select id="new-booking-children" name="children" class="bookinn-select">
                                            <option value="0">No Children</option>
                                            <option value="1">1 Child</option>
                                            <option value="2">2 Children</option>
                                            <option value="3">3 Children</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Room Availability Check Section -->


                                <div class="bookinn-form-grid">
                                    <div class="bookinn-form-group">
                                        <label for="new-guest-first-name">Guest First Name *</label>
                                        <input type="text" id="new-guest-first-name" name="guest_first_name" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-guest-last-name">Guest Last Name *</label>
                                        <input type="text" id="new-guest-last-name" name="guest_last_name" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-guest-email">Email *</label>
                                        <input type="email" id="new-guest-email" name="guest_email" class="bookinn-input" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-guest-phone">Phone</label>
                                        <input type="tel" id="new-guest-phone" name="guest_phone" class="bookinn-input">
                                    </div>
                                </div>

                                <div class="bookinn-form-grid">
                                <!-- Spostato il dropdown Room nella sezione check section -->
                                <div class="bookinn-form-group">
                                    <label for="new-booking-room-select-v2">Room *</label>
                                    <select id="new-booking-room-select-v2" name="room_id" class="bookinn-select" required>
                                        <option value="">Select Room</option>
                                        <!-- Rooms will be loaded via AJAX -->
                                    </select>
                                    <div id="room-status-display" class="bookinn-room-status-display" style="display: none;">
                                        <span class="room-status-label">Status:</span>
                                        <span class="room-status-value"></span>
                                    </div>
                                </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-total">Total Amount *</label>
                                        <input type="number" id="new-booking-total" name="total_amount" class="bookinn-input" step="0.01" required>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-booking-source">Booking Source</label>
                                        <select id="new-booking-booking-source" name="booking_source" class="bookinn-select">
                                            <option value="direct">Direct</option>
                                            <option value="website">Website</option>
                                            <option value="phone">Phone</option>
                                            <option value="email">Email</option>
                                            <option value="walk_in">Walk In</option>
                                            <option value="booking_com">Booking.com</option>
                                            <option value="airbnb">Airbnb</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-payment-method">Payment Method</label>
                                        <select id="new-booking-payment-method" name="payment_method" class="bookinn-select">
                                            <option value="cash">Cash</option>
                                            <option value="credit_card">Credit Card</option>
                                            <option value="debit_card">Debit Card</option>
                                            <option value="bank_transfer">Bank Transfer</option>
                                            <option value="paypal">PayPal</option>
                                            <option value="stripe">Stripe</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div class="bookinn-form-group">
                                        <label for="new-booking-status">Status</label>
                                        <select id="new-booking-status" name="status" class="bookinn-select">
                                            <option value="pending">Pending</option>
                                            <option value="confirmed">Confirmed</option>
                                            <option value="checked_in">Checked In</option>
                                            <option value="checked_out">Checked Out</option>
                                            <option value="cancelled">Cancelled</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="bookinn-form-group">
                                    <label for="new-booking-notes">Notes</label>
                                    <textarea id="new-booking-notes" name="notes" class="bookinn-textarea" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-close-modal">Cancel</button>
                            <button type="button" class="bookinn-btn bookinn-btn-primary" id="save-new-booking">Save Booking</button>
                        </div>
                    </div>
                </div>
            `;
        },

        // ===== DATA HANDLING METHODS =====

        /**
         * Load room data by ID
         */
        loadRoomData: function(roomId) {
            const self = this;
            return new Promise((resolve, reject) => {
                // Debug: Log nonce information
                console.log('BookInn Management Unified: Loading room data with nonces:', {
                    ajaxNonce: self.config.ajaxNonce,
                    nonce: self.config.nonce,
                    using: self.config.ajaxNonce || self.config.nonce
                });

                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'bookinn_get_room',
                        nonce: self.config.ajaxNonce || self.config.nonce, // Use correct nonce for management AJAX
                        room_id: roomId
                    },
                    timeout: 10000, // Add timeout to prevent hanging
                    success: function(response) {
                        console.log('Room data AJAX success:', response);
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to load room data');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Room data AJAX error:', {xhr, status, error, responseText: xhr.responseText});
                        reject(`AJAX Error: ${error} (Status: ${xhr.status})`);
                    }
                });
            });
        },

        /**
         * Load room type data by ID
         */
        loadRoomTypeData: function(roomTypeId) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'bookinn_get_room_type',
                        nonce: this.config.ajaxNonce || this.config.nonce, // Use correct nonce for management AJAX
                        room_type_id: roomTypeId
                    },
                    timeout: 10000, // Add timeout to prevent hanging
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to load room type data');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Room type data AJAX error:', {xhr, status, error});
                        reject(`AJAX Error: ${error} (Status: ${xhr.status})`);
                    }
                });
            });
        },

        /**
         * Load rooms data with filters
         */
        loadRoomsData: function(filters = {}) {
            const self = this;

            // Show loading spinner
            const $tableBody = $('#bookinn-rooms-table tbody');
            const $tableContainer = $('.bookinn-table-container');
            
            if ($tableBody.length) {
                $tableBody.html('<tr><td colspan="6" class="bookinn-loading-cell"><i class="bookinn-icon-loading bookinn-spin"></i> Loading rooms...</td></tr>');
            }

            // Prepare data with filters
            const data = {
                action: 'bookinn_get_rooms',
                nonce: this.config?.nonce || this.config?.ajaxNonce || this.config?.managementNonce || window.bookinnAjax?.nonce
            };

            // Add filters if provided
            if (filters.status) data.status = filters.status;
            if (filters.room_number) data.room_number = filters.room_number;
            if (filters.room_type_id) data.room_type_id = filters.room_type_id;

            console.log('Loading rooms with filters:', filters);
            console.log('AJAX data being sent:', data);
            console.log('AJAX URL:', this.config.ajaxUrl);

            // Validate AJAX configuration
            if (!this.config.ajaxUrl) {
                console.error('BookInn: AJAX URL not configured');
                const error = 'AJAX URL not configured';
                if ($tableBody.length) {
                    $tableBody.html('<tr><td colspan="6" class="bookinn-no-data">Configuration error: ' + error + '</td></tr>');
                }
                return Promise.reject(error);
            }

            if (!data.nonce) {
                console.error('BookInn: No valid nonce found');
                const error = 'Security nonce not available';
                if ($tableBody.length) {
                    $tableBody.html('<tr><td colspan="6" class="bookinn-no-data">Security error: ' + error + '</td></tr>');
                }
                return Promise.reject(error);
            }

            return new Promise((resolve, reject) => {
                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: data,
                    success: function(response) {
                        console.log('Room data response:', response);
                        if (response.success) {
                            self.cache.rooms = response.data;
                            self.updateRoomsDisplay(response.data);
                            resolve(response.data);
                        } else {
                            console.error('Room loading failed:', response.data);
                            if ($tableBody.length) {
                                $tableBody.html('<tr><td colspan="6" class="bookinn-no-data">Failed to load rooms: ' + (response.data || 'Unknown error') + '</td></tr>');
                            }
                            reject(response.data || 'Failed to load rooms');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Room loading AJAX error:', {xhr, status, error});
                        if ($tableBody.length) {
                            $tableBody.html('<tr><td colspan="6" class="bookinn-no-data">Network error loading rooms</td></tr>');
                        }
                        reject(error);
                    }
                });
            });
        },

        /**
         * Load room types data
         */
        loadRoomTypesData: function() {
            const self = this;

            return new Promise((resolve, reject) => {
                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'bookinn_get_room_types',
                        nonce: this.config.ajaxNonce || this.config.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            self.cache.roomTypes = response.data;
                            self.updateRoomTypesDisplay(response.data);
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to load room types');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },

        /**
         * Perform room search with current filter values
         */
        performRoomSearch: function() {
            console.log('BookInn: Starting room search...');

            // Get filter values from the form
            const statusFilter = $('#rooms-status-filter').val();
            const numberFilter = $('#rooms-number-filter').val().trim();
            const typeFilter = $('#rooms-type-filter').val();

            console.log('BookInn: Raw filter values:', {
                status: statusFilter,
                number: numberFilter,
                type: typeFilter
            });

            // Build filters object
            const filters = {};
            if (statusFilter && statusFilter !== '') filters.status = statusFilter;
            if (numberFilter && numberFilter !== '') filters.room_number = numberFilter;
            if (typeFilter && typeFilter !== '') filters.room_type_id = typeFilter;

            console.log('BookInn: Processed room search filters:', filters);

            // Show loading state on button
            const $button = $('#apply-room-filters');
            if (!$button.length) {
                console.error('BookInn: Apply room filters button not found');
                return;
            }

            const originalText = $button.html();
            $button.html('<i class="fa-solid fa-spinner fa-spin bookinn-fa"></i> Applying...').prop('disabled', true);

            // Load rooms with filters
            this.loadRoomsData(filters)
                .then((rooms) => {
                    console.log('BookInn: Room search completed successfully, found', rooms.length, 'rooms');
                    
                    let message = 'Search completed.';
                    if (Object.keys(filters).length > 0) {
                        message += ' Found ' + rooms.length + ' room(s) matching your criteria.';
                    } else {
                        message += ' Showing all ' + rooms.length + ' rooms.';
                    }
                    
                    this.showNotification(message, 'success');
                })
                .catch((error) => {
                    console.error('BookInn: Room search failed:', error);
                    this.showNotification('Room search failed: ' + error, 'error');
                })
                .finally(() => {
                    // Restore button state
                    $button.html(originalText).prop('disabled', false);
                });
        },

        /**
         * Load bookings data
         */
        loadBookingsData: function(filters = {}) {
            const self = this;

            return new Promise((resolve, reject) => {
                const ajaxData = {
                    action: 'bookinn_get_bookings',
                    nonce: this.config.nonce
                };

                // Add filters if provided
                if (Object.keys(filters).length > 0) {
                    ajaxData.filters = filters;
                    console.log('[BookInn DEBUG] Sending filters to backend:', filters);
                } else {
                    console.log('[BookInn DEBUG] No filters provided, loading all bookings');
                }

                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: ajaxData,
                    success: function(response) {
                        console.log('[BookInn DEBUG] AJAX Response:', response);
                        console.log('[BookInn DEBUG] Response type:', typeof response);
                        console.log('[BookInn DEBUG] Response.success:', response.success);
                        console.log('[BookInn DEBUG] Response.data:', response.data);
                        
                        if (response.success && response.data) {
                            // Estrarre l'array delle prenotazioni dalla risposta
                            const bookingsArray = response.data.bookings || response.data;
                            console.log('[BookInn DEBUG] Extracted bookings array:', bookingsArray);
                            console.log('[BookInn DEBUG] Applied filters:', response.data.applied_filters);
                            console.log('[BookInn DEBUG] Total found:', response.data.total);
                            console.log('[BookInn DEBUG] Calling updateBookingsDisplay with:', bookingsArray.length, 'bookings');
                            
                            // Update filter summary if filters were applied
                            if (Object.keys(filters).length > 0) {
                                self.updateFilterSummary(filters, bookingsArray.length, response.data.total);
                            } else {
                                self.clearFilterSummary();
                            }
                            
                            self.cache.bookings = bookingsArray;
                            self.updateBookingsDisplay(bookingsArray);
                            resolve(bookingsArray);
                        } else {
                            console.error('[BookInn DEBUG] AJAX failed or no data:', response);
                            reject(response.data || 'Failed to load bookings');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('[BookInn DEBUG] AJAX Error:', {xhr, status, error});
                        reject(error);
                    }
                });
            });
        },

        /**
         * Update filter summary display
         */
        updateFilterSummary: function(filters, foundCount, totalCount) {
            let summaryText = `Showing ${foundCount} of ${totalCount} bookings`;
            
            const activeFilters = [];
            if (filters.status) activeFilters.push(`Status: ${filters.status}`);
            if (filters.date_from) activeFilters.push(`From: ${filters.date_from}`);
            if (filters.date_to) activeFilters.push(`To: ${filters.date_to}`);
            if (filters.room_id) {
                const roomName = $('#filter-room option:selected').text();
                activeFilters.push(`Room: ${roomName}`);
            }
            
            if (activeFilters.length > 0) {
                summaryText += ` (Filtered by: ${activeFilters.join(', ')})`;
            }
            
            // Add or update filter summary
            let $summary = $('.bookinn-filter-summary');
            if (!$summary.length) {
                $summary = $('<div class="bookinn-filter-summary"></div>');
                $('#bookinn-bookings-table').before($summary);
            }
            
            $summary.html(`<i class="fa fa-filter"></i> ${summaryText}`);
            $summary.show();
        },

        /**
         * Clear filter summary display
         */
        clearFilterSummary: function() {
            $('.bookinn-filter-summary').hide();
        },

        // ===== DEBUG AND TESTING FUNCTIONS =====

        /**
         * Test filter system (chiamabile dalla console)
         */
        testFilters: function() {
            console.log('=== BookInn Filter System Test ===');
            
            // Test 1: Verifica elementi esistenti
            console.log('1. Testing filter elements existence:');
            const elements = {
                filterStatus: $('#filter-status'),
                filterDateFrom: $('#filter-date-from'),
                filterDateTo: $('#filter-date-to'),
                filterRoom: $('#filter-room'),
                applyButton: $('#apply-filters'),
                clearButton: $('#clear-filters'),
                filterPanel: $('#bookinn-list-filters')
            };
            
            Object.keys(elements).forEach(key => {
                const el = elements[key];
                console.log(`  - ${key}: ${el.length > 0 ? 'FOUND' : 'MISSING'} (length: ${el.length})`);
                if (el.length > 0 && (key.includes('filter-') && !key.includes('Button'))) {
                    console.log(`    Current value: "${el.val()}"`);
                }
            });
            
            // Test 2: Simulazione filtro
            console.log('2. Testing filter simulation:');
            console.log('Setting test values...');
            $('#filter-status').val('confirmed');
            console.log('Status set to:', $('#filter-status').val());
            
            // Test 3: AJAX Configuration
            console.log('3. Testing AJAX configuration:');
            console.log('AJAX URL:', this.config.ajaxUrl);
            console.log('Nonce:', this.config.nonce);
            
            // Test 4: Manual AJAX call
            console.log('4. Testing manual AJAX call:');
            this.testAjaxCall();
            
            console.log('=== Test Complete ===');
        },

        /**
         * Test AJAX call manually
         */
        testAjaxCall: function() {
            const testFilters = {
                status: 'confirmed'
            };
            
            console.log('Making test AJAX call with filters:', testFilters);
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_get_bookings',
                    nonce: this.config.nonce,
                    filters: testFilters
                },
                success: function(response) {
                    console.log('Test AJAX SUCCESS:', response);
                },
                error: function(xhr, status, error) {
                    console.error('Test AJAX ERROR:', {xhr, status, error});
                }
            });
        },

        /**
         * Initialize reports section using dedicated reports widget
         */
        loadReportsData: function() {
            console.log('BookInn Management Unified: Initializing reports section...');

            // Debug: Check what containers exist
            const reportsTab = $('#tab-reports');
            const reportsInterface = $('#tab-reports .bookinn-reports-interface');
            const reportsFallback = $('#tab-reports .bookinn-reports-fallback');

            console.log('BookInn Reports Debug:', {
                reportsTabExists: reportsTab.length > 0,
                reportsInterfaceExists: reportsInterface.length > 0,
                reportsFallbackExists: reportsFallback.length > 0,
                dedicatedWidgetAvailable: typeof window.BookInnReportsWidget !== 'undefined',
                tabContent: reportsTab.html()?.substring(0, 200) + '...'
            });

            // Check if the dedicated reports widget is available
            if (typeof window.BookInnReportsWidget !== 'undefined') {
                // Use the dedicated reports widget
                const reportsContainer = $('#tab-reports .bookinn-reports-interface');
                if (reportsContainer.length && !reportsContainer.data('reports-initialized')) {
                    // Show the dedicated widget container and hide fallback
                    reportsContainer.show();
                    $('.bookinn-reports-fallback').hide();

                    try {
                        new window.BookInnReportsWidget(reportsContainer[0]);
                        reportsContainer.data('reports-initialized', true);
                        console.log('Reports widget initialized successfully');
                    } catch (error) {
                        console.error('Error initializing reports widget:', error);
                        console.log('Using PHP server-side rendered reports instead');
                    }
                } else if (reportsContainer.length === 0) {
                    console.log('Reports interface container not found - using PHP server-side rendering');
                    // Removed fallback function call - reports are now rendered server-side
                }
            } else {
                // Fallback: Show basic interface and load KPI data
                console.log('Reports widget not available - now using PHP server-side rendering');
                // Removed fallback function call - reports are now rendered server-side
            }
        },









        /**
         * Update reports charts with data
         */
        updateReportsCharts: function(data) {
            console.log('Updating reports charts with data:', data);

            // Initialize charts if they haven't been initialized yet
            if (!this.charts.reportsRevenue || !this.charts.reportsOccupancy || !this.charts.reportsBookingSource) {
                setTimeout(() => {
                    this.initializeReportsCharts();
                    // Try updating charts again after initialization
                    setTimeout(() => this.updateReportsCharts(data), 1000);
                }, 500);
                return;
            }

            // Update revenue chart if data is available
            if (data.revenue_chart && this.charts.reportsRevenue) {
                this.charts.reportsRevenue.data.labels = data.revenue_chart.labels || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
                this.charts.reportsRevenue.data.datasets[0].data = data.revenue_chart.data || [5200, 6800, 7500, 8200, 9100, 8800];
                this.charts.reportsRevenue.update();
            }

            // Update occupancy chart if data is available
            if (data.occupancy_chart && this.charts.reportsOccupancy) {
                this.charts.reportsOccupancy.data.labels = data.occupancy_chart.labels || ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
                this.charts.reportsOccupancy.data.datasets[0].data = data.occupancy_chart.data || [78, 85, 92, 88, 95, 90];
                this.charts.reportsOccupancy.update();
            }

            // Update booking source chart if data is available
            if (data.booking_source_chart && this.charts.reportsBookingSource) {
                this.charts.reportsBookingSource.data.labels = data.booking_source_chart.labels || ['Direct', 'Booking.com', 'Airbnb', 'Expedia', 'Other'];
                this.charts.reportsBookingSource.data.datasets[0].data = data.booking_source_chart.data || [35, 25, 20, 12, 8];
                this.charts.reportsBookingSource.update();
            }
        },



        /**
         * Format number with proper locale formatting
         */
        formatNumber: function(number, decimals = 0) {
            if (number === null || number === undefined || isNaN(number)) {
                return '0';
            }

            // Convert to number if it's a string
            const num = parseFloat(number);

            // Use browser's built-in number formatting if available
            if (typeof Intl !== 'undefined' && Intl.NumberFormat) {
                try {
                    return new Intl.NumberFormat('en-US', {
                        minimumFractionDigits: decimals,
                        maximumFractionDigits: decimals
                    }).format(num);
                } catch (e) {
                    // Fallback to basic formatting
                }
            }

            // Fallback: basic number formatting
            return num.toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        },

        /**
         * Format currency with proper symbol and formatting
         */
        formatCurrency: function(amount, currency = 'EUR') {
            if (amount === null || amount === undefined || isNaN(amount)) {
                return '€0.00';
            }

            const num = parseFloat(amount);
            const symbols = {
                'USD': '$',
                'EUR': '€',
                'GBP': '£',
                'JPY': '¥',
                'CHF': 'CHF',
                'CAD': 'CAD$',
                'AUD': 'AUD$'
            };

            const symbol = symbols[currency] || '€';
            const formatted = this.formatNumber(num, 2);

            return symbol + formatted;
        },

        // ===== FORM HANDLING METHODS =====

        /**
         * Collect room form data
         */
        collectRoomFormData: function() {
            var checked = $('.bookinn-modal.is-active #room-active').prop('checked');
            console.log('DEBUG is_active checkbox:', checked);
            
            // Collect basic form data
            const formData = {
                room_id: $('#room-id').val(),
                hotel_id: $('#room-hotel-id').val() || 1,
                room_type_id: parseInt($('#room-type-select').val(), 10) || 0,
                room_number: $('#room-number').val(),
                name: $('#room-name').val(),
                floor: $('#room-floor').val() || 1,
                status: $('#room-status').val() || 'available',
                is_active: checked ? 1 : 0
            };
            
            // Add image data
            const imageData = this.collectImageData();
            formData.primary_pic = imageData.primary_pic;
            formData.secondary_pics = imageData.secondary_pics;
            
            return formData;
        },

            /**
             * Debug: Log checkbox value on change
             */
            initActiveCheckboxDebug: function() {
                $(document).on('change', '#room-active', function() {
                    console.log('DEBUG: #room-active changed, checked =', $(this).prop('checked'));
                });
            },

        /**
         * Collect room type form data
         */
        collectRoomTypeFormData: function() {
            return {
                room_type_id: $('#room-type-id').val(),
                hotel_id: $('#room-type-hotel-id').val() || 1,
                name: $('#room-type-name').val(),
                description: $('#room-type-description').val(),
                max_adults: $('#room-type-max-adults').val() || 2,
                max_children: $('#room-type-max-children').val() || 0,
                max_guests: $('#room-type-max-guests').val() || 2,
                base_price: $('#room-type-base-price').val() || 0,
                amenities: $('#room-type-amenities').val() || '',
                images: $('#room-type-images').val() || '',
                is_active: $('#room-type-is-active').prop('checked') ? 1 : 0
            };
        },

        /**
         * Validate room data
         */
        validateRoomData: function(data) {
            if (!data.room_number) {
                alert('Room number is required');
                $('#room-number').focus();
                return false;
            }
            if (!data.name) {
                alert('Room name is required');
                $('#room-name').focus();
                return false;
            }
            if (!data.room_type_id) {
                alert('Room type is required');
                $('#room-type-select').focus();
                return false;
            }
            return true;
        },

        /**
         * Validate room type data
         */
        validateRoomTypeData: function(data) {
            if (!data.name) {
                alert('Room type name is required');
                $('#room-type-name').focus();
                return false;
            }

            if (!data.base_price || data.base_price < 0) {
                alert('Valid base price is required');
                $('#room-type-base-price').focus();
                return false;
            }

            if (!data.max_guests || data.max_guests <= 0) {
                alert('Valid maximum guests number is required');
                $('#room-type-max-guests').focus();
                return false;
            }

            return true;
        },

        /**
         * Populate room form with data
         */
        populateRoomForm: function(roomData) {
            console.log('BookInn Management Unified: Populating room form with data:', roomData);

            if (!roomData) {
                console.error('No room data provided to populateRoomForm');
                return;
            }

            try {
                $('#room-id').val(roomData.id || '');
                $('#room-hotel-id').val(roomData.hotel_id || '1');
                $('#room-number').val(roomData.room_number || '');
                $('#room-name').val(roomData.name || '');
                $('#room-floor').val(roomData.floor || '1');
                $('#room-status').val(roomData.status || 'available');
                $('#room-active').prop('checked', roomData.is_active == 1);

                // Popola la select dei tipi stanza e seleziona il valore corretto dopo il caricamento
                    // Debug: log #room-active changes
                    this.initActiveCheckboxDebug();
                const self = this;
                this.populateRoomTypeSelect();
                setTimeout(function() {
                    $('#room-type-select').val(roomData.room_type_id || '');
                    // Se non esiste l'opzione, la aggiunge
                    if (roomData.room_type_id && $('#room-type-select option[value="' + roomData.room_type_id + '"]').length === 0 && roomData.room_type_name) {
                        $('#room-type-select').append(`<option value="${roomData.room_type_id}" selected>${roomData.room_type_name}</option>`);
                    }
                }, 300);

                // Populate room images
                this.populateRoomImages(roomData);

                console.log('Room form populated successfully');
            } catch (error) {
                console.error('Error populating room form:', error);
            }
        },

        /**
         * Populate room type form with data
         */
        populateRoomTypeForm: function(roomTypeData) {
            console.log('BookInn Management Unified: Populating room type form with data:', roomTypeData);
            
            if (!roomTypeData) {
                console.error('No room type data provided to populateRoomTypeForm');
                return;
            }

            try {
                $('#room-type-id').val(roomTypeData.id || '');
                console.log('Set room type ID:', roomTypeData.id);
                
                $('#room-type-hotel-id').val(roomTypeData.hotel_id || '1');
                console.log('Set hotel ID:', roomTypeData.hotel_id);
                
                $('#room-type-name').val(roomTypeData.name || '');
                console.log('Set room type name:', roomTypeData.name);
                
                $('#room-type-description').val(roomTypeData.description || '');
                console.log('Set room type description:', roomTypeData.description);
                
                $('#room-type-base-price').val(roomTypeData.base_price || '');
                console.log('Set room type base price:', roomTypeData.base_price);
                
                $('#room-type-max-adults').val(roomTypeData.max_adults || '2');
                console.log('Set room type max adults:', roomTypeData.max_adults);
                
                $('#room-type-max-children').val(roomTypeData.max_children || '0');
                console.log('Set room type max children:', roomTypeData.max_children);
                
                $('#room-type-max-guests').val(roomTypeData.max_guests || '2');
                console.log('Set room type max guests:', roomTypeData.max_guests);
                
                $('#room-type-amenities').val(roomTypeData.amenities || '');
                console.log('Set room type amenities:', roomTypeData.amenities);
                
                $('#room-type-images').val(roomTypeData.images || '');
                console.log('Set room type images:', roomTypeData.images);
                
                $('#room-type-is-active').prop('checked', roomTypeData.is_active == 1);
                console.log('Set room type active:', roomTypeData.is_active);
                
                console.log('Room type form populated successfully');
            } catch (error) {
                console.error('Error populating room type form:', error);
            }
        },

        /**
         * Clear room form
         */
        clearRoomForm: function() {
            $('#room-form')[0].reset();
            $('#room-id').val('');
            $('#room-hotel-id').val('1');
            $('#room-floor').val('1');
            $('#room-status').val('available');
            $('#room-active').prop('checked', true);
            
            // Clear image data
            this.clearImageData();
        },

        /**
         * Clear room type form
         */
        clearRoomTypeForm: function() {
            $('#room-type-form')[0].reset();
            $('#room-type-id').val('');
            $('#room-type-hotel-id').val('1');
            $('#room-type-max-adults').val('2');
            $('#room-type-max-children').val('0');
            $('#room-type-max-guests').val('2');
            $('#room-type-is-active').prop('checked', true);
        },

        /**
         * Clear booking form for new bookings
         */
        clearBookingForm: function() {
            $('#bookinn-booking-form')[0].reset();
            $('#booking-id').val('');
            $('#new-booking-status').val('pending');
            $('#new-booking-payment-method').val('cash');
            
            // Don't force default values - let showBookingModal handle them only if fields are empty
            // This prevents overwriting user-modified values
            console.log('BookInn: Form cleared, keeping user-selected dates and guest counts');
        },

        /**
         * Populate room type select dropdown
         */
        populateRoomTypeSelect: function() {
            const $select = $('#room-type-select');
            if ($select.length === 0) return;

            // Clear existing options except the first one
            $select.find('option:not(:first)').remove();

            // Load room types and populate select
            this.loadRoomTypesData().then(roomTypes => {
                roomTypes.forEach(type => {
                    $select.append(`<option value="${type.id}">${type.name}</option>`);
                });
                console.log('BookInn Management Unified: Room type select populated with', roomTypes.length, 'options');
            }).catch(error => {
                console.error('Error loading room types for select:', error);
            });
        },

        /**
         * Clear room type form
         */
        clearRoomTypeForm: function() {
            $('#room-type-form')[0].reset();
            $('#room-type-id').val('');
        },

        /**
         * Show booking wizard
         */
        // Wizard functionality removed - all wizard functions removed



        // ===== UTILITY METHODS =====

        /**
         * Show loading state on button
         */
        showSaveLoading: function(buttonSelector) {
            const $button = $(buttonSelector);
            $button.prop('disabled', true).addClass('bookinn-loading');
            $button.data('original-text', $button.text());
            $button.text('Saving...');
        },

        /**
         * Hide loading state on button
         */
        hideSaveLoading: function(buttonSelector) {
            const $button = $(buttonSelector);
            $button.prop('disabled', false).removeClass('bookinn-loading');
            const originalText = $button.data('original-text');
            if (originalText) {
                $button.text(originalText);
            }
        },

        /**
         * Show success message
         */
        showSuccessMessage: function(message) {
            // Use BookInn.Core notification system if available
            if (window.BookInn.Core && window.BookInn.Core.showNotification) {
                window.BookInn.Core.showNotification(message, 'success');
            } else {
                alert(message);
            }
        },

        // ===== SAVE METHODS =====

        /**
         * Save room data
         */
        saveRoomData: function(formData) {
            return new Promise((resolve, reject) => {
                // Create FormData object for file uploads
                const formDataObj = new FormData();
                
                // Add standard form fields
                formDataObj.append('action', 'bookinn_save_room'); // Use unified save handler
                formDataObj.append('nonce', this.config.nonce);
                formDataObj.append('room_id', formData.room_id || '');
                formDataObj.append('hotel_id', formData.hotel_id || 1);
                formDataObj.append('room_type_id', formData.room_type_id);
                formDataObj.append('room_number', formData.room_number);
                formDataObj.append('name', formData.name);
                formDataObj.append('floor', formData.floor || 1);
                formDataObj.append('status', formData.status || 'available');
                formDataObj.append('is_active', typeof formData.is_active !== 'undefined' ? formData.is_active : 1);
                
                // Add image files
                if (formData.primary_pic && formData.primary_pic instanceof File) {
                    formDataObj.append('primary_pic', formData.primary_pic);
                }
                
                if (formData.secondary_pics && Array.isArray(formData.secondary_pics)) {
                    formData.secondary_pics.forEach((file, index) => {
                        if (file instanceof File) {
                            formDataObj.append('secondary_pics[]', file);
                        }
                    });
                }
                
                console.log('Sending room data to server with files');
                
                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: formDataObj,
                    processData: false, // Important for file uploads
                    contentType: false, // Important for file uploads
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to save room');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },

        /**
         * Save room type data
         */
        saveRoomTypeData: function(formData) {
            return new Promise((resolve, reject) => {
                // Map form data to server expectations with correct field names
                const serverData = {
                    action: 'bookinn_save_room_type', // Use unified save handler
                    nonce: this.config.nonce,
                    room_type_id: formData.room_type_id,
                    hotel_id: formData.hotel_id || 1,
                    name: formData.name,
                    description: formData.description || '',
                    max_adults: formData.max_adults || 2,
                    max_children: formData.max_children || 0,
                    max_guests: formData.max_guests || 2,
                    base_price: formData.base_price || 0,
                    amenities: formData.amenities || '',
                    images: formData.images || '',
                    is_active: formData.is_active || 1
                };
                
                console.log('Sending room type data to server:', serverData);
                
                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: serverData,
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to save room type');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },

        // ===== DELETE METHODS =====

        /**
         * Delete room with validation
         */
        deleteRoom: function(roomId) {
            if (!roomId) {
                alert('Room ID is required');
                return;
            }

            // Get room info from the row for better confirmation message
            const roomRow = $(`tr[data-room-id="${roomId}"]`);
            const roomNumber = roomRow.find('td:first strong').text() || 'Unknown';
            
            if (!confirm(`Are you sure you want to delete Room ${roomNumber}?\n\nThis action cannot be undone. The room will be permanently removed from the system.`)) {
                return;
            }

            const self = this;

            // Show loading state
            const deleteBtn = $(`.bookinn-delete-room[data-room-id="${roomId}"]`);
            const originalText = deleteBtn.text();
            deleteBtn.prop('disabled', true).text('Deleting...');

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_delete_room',
                    nonce: this.config.nonce,
                    room_id: roomId
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccessMessage(`Room ${roomNumber} deleted successfully!`);
                        // Remove the row from the table with animation
                        roomRow.fadeOut(300, function() {
                            $(this).remove();
                            // Check if table is empty
                            if ($('#bookinn-rooms-table tbody tr').length === 0) {
                                $('#bookinn-rooms-table tbody').html('<tr><td colspan="6" class="bookinn-no-data">No rooms found. Add your first room to get started.</td></tr>');
                            }
                        });
                        // Refresh rooms data for other components
                        self.refreshRoomsData();
                    } else {
                        const errorMsg = response.data || 'Failed to delete room';
                        alert('Error: ' + errorMsg);
                        // Restore button state
                        deleteBtn.prop('disabled', false).text(originalText);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Delete room error:', error);
                    alert('Error deleting room. Please check your connection and try again.');
                    // Restore button state
                    deleteBtn.prop('disabled', false).text(originalText);
                }
            });
        },

        /**
         * Delete room type
         */
        deleteRoomType: function(roomTypeId) {
            if (!confirm('Are you sure you want to delete this room type?')) {
                return;
            }

            const self = this;

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_delete_room_type',
                    nonce: this.config.ajaxNonce || this.config.nonce,
                    room_type_id: roomTypeId
                },
                success: function(response) {
                    if (response.success) {
                        self.showSuccessMessage('Room type deleted successfully!');
                        self.refreshRoomTypesData();
                    } else {
                        alert('Error: ' + (response.data || 'Failed to delete room type'));
                    }
                },
                error: function() {
                    alert('Error deleting room type. Please try again.');
                }
            });
        },

        // ===== REFRESH METHODS =====

        /**
         * Refresh rooms data
         */
        refreshRoomsData: function() {
            this.loadRoomsData();
        },

        /**
         * Refresh room types data
         */
        refreshRoomTypesData: function() {
            this.loadRoomTypesData();
            // Also refresh room types in modal if open
            if ($('#bookinn-room-types-management-modal').hasClass('is-active')) {
                this.loadRoomTypesInModal();
            }
        },

        // ===== DISPLAY UPDATE METHODS =====

        /**
         * Update rooms display
         */
        updateRoomsDisplay: function(rooms) {
            console.log('BookInn: Updating rooms display with', rooms.length, 'rooms');
            
            const $tableBody = $('#bookinn-rooms-table tbody');
            if (!$tableBody.length) {
                console.error('BookInn: Rooms table body not found');
                return;
            }

            if (!rooms || rooms.length === 0) {
                $tableBody.html('<tr><td colspan="7" class="bookinn-no-data">No rooms found matching the current filters.</td></tr>');
                return;
            }

            let html = '';
            rooms.forEach(function(room) {
                const roomId = room.id || '';
                const roomNumber = room.room_number || '';
                const roomType = room.room_type_name || 'N/A';
                const floor = room.floor || 'N/A';
                const status = room.status || 'available';
                const price = room.base_price ? parseFloat(room.base_price).toFixed(2) : '0.00';
                const isActive = typeof room.is_active !== 'undefined' ? room.is_active : 0;

                // Status badge
                let statusBadge = '';
                switch(status) {
                    case 'available':
                        statusBadge = '<span class="bookinn-status-badge bookinn-status-available">Available</span>';
                        break;
                    case 'occupied':
                        statusBadge = '<span class="bookinn-status-badge bookinn-status-occupied">Occupied</span>';
                        break;
                    case 'maintenance':
                        statusBadge = '<span class="bookinn-status-badge bookinn-status-maintenance">Maintenance</span>';
                        break;
                    case 'cleaning':
                        statusBadge = '<span class="bookinn-status-badge bookinn-status-cleaning">Cleaning</span>';
                        break;
                    case 'out_of_order':
                        statusBadge = '<span class="bookinn-status-badge bookinn-status-out-of-order">Out of Order</span>';
                        break;
                    default:
                        statusBadge = '<span class="bookinn-status-badge bookinn-status-unknown">' + status + '</span>';
                }

                // Active label
                let activeLabel = '';
                if (isActive == 1 || isActive === true || isActive === '1') {
                    activeLabel = '<span class="bookinn-active-label bookinn-active">Active</span>';
                } else {
                    activeLabel = '<span class="bookinn-active-label bookinn-inactive">Inactive</span>';
                }

                html += `
                    <tr data-room-id="${roomId}">
                        <td><strong>${roomNumber}</strong></td>
                        <td>${roomType}</td>
                        <td>${floor}</td>
                        <td>${statusBadge}</td>
                        <td>${activeLabel}</td>
                        <td>$${price}</td>
                        <td class="bookinn-actions">
                            <button class="bookinn-btn bookinn-btn-sm bookinn-btn-primary bookinn-show-room" data-room-id="${roomId}">
                                <i class="fas fa-eye"></i> Show
                            </button>
                            <button class="bookinn-btn bookinn-btn-sm bookinn-btn-primary bookinn-edit-room" data-room-id="${roomId}">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bookinn-delete-room" data-room-id="${roomId}">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                `;
            });
            $tableBody.html(html);
            console.log('BookInn: Rooms table updated successfully');
        },

        // Show Room Modal (read-only)
        showRoomReadOnlyModal: function(roomData) {
            $('#bookinn-room-show-modal').remove();
            const isActive = roomData.is_active == 1 || roomData.is_active === true || roomData.is_active === '1';
            const modalHtml = `
                <div id="bookinn-room-show-modal" class="bookinn-modal" data-room-id="${roomData.id}" role="dialog" aria-labelledby="room-show-modal-title" tabindex="-1">
                    <div class="bookinn-modal-overlay"></div>
                    <div class="bookinn-modal-container bookinn-modal-wide">
                        <div class="bookinn-modal-header">
                            <h4 id="room-show-modal-title">Room Details</h4>
                            <button class="bookinn-modal-close" type="button" aria-label="Close dialog">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <div class="bookinn-room-form-content">
                                <form id="bookinn-room-show-form" autocomplete="off">
                                    <!-- Room Basic Information Section -->
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-door-open"></i> Room Information</h5>
                                        <div class="bookinn-form-grid">
                                            <div class="bookinn-form-group">
                                                <label for="show-room-number">Room Number</label>
                                                <input type="text" id="show-room-number" name="room_number" class="bookinn-input" value="${roomData.room_number}" readonly>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="show-room-name">Room Name</label>
                                                <input type="text" id="show-room-name" name="room_name" class="bookinn-input" value="${roomData.name || ''}" readonly>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="show-room-type">Room Type</label>
                                                <input type="text" id="show-room-type" name="room_type" class="bookinn-input" value="${roomData.room_type_name || ''}" readonly>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="show-room-floor">Floor</label>
                                                <input type="text" id="show-room-floor" name="floor" class="bookinn-input" value="${roomData.floor || ''}" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Room Status Section -->
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-cog"></i> Room Status</h5>
                                        <div class="bookinn-form-grid">
                                            <div class="bookinn-form-group">
                                                <label for="show-room-status">Status</label>
                                                <input type="text" id="show-room-status" name="status" class="bookinn-input" value="${roomData.status || ''}" readonly>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label for="show-room-active">Active</label>
                                                <input type="text" id="show-room-active" name="is_active" class="bookinn-input ${isActive ? 'bookinn-active' : 'bookinn-inactive'}" value="${isActive ? 'Active' : 'Inactive'}" readonly>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Room Images Section (Read-only) -->
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-images"></i> Room Images</h5>
                                        <div class="bookinn-images-readonly">
                                            <div class="bookinn-form-group">
                                                <label>Primary Image</label>
                                                <div class="bookinn-image-display primary-image-display">
                                                    ${roomData.primary_pic_thumb ? 
                                                        `<img src="${roomData.primary_pic_thumb}" alt="Primary room thumbnail" class="bookinn-room-thumb">` :
                                                        (roomData.primary_pic ?
                                                            `<img src="${roomData.primary_pic}" alt="Primary room image" class="bookinn-room-thumb">` :
                                                            '<div class="bookinn-no-image"><i class="fas fa-image"></i><p>No primary image</p></div>'
                                                        )
                                                    }
                                                </div>
                                            </div>
                                            <div class="bookinn-form-group">
                                                <label>Additional Images</label>
                                                <div class="bookinn-images-display secondary-images-display">
                                                    ${this.renderSecondaryImagesReadOnly(roomData.secondary_pics)}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-secondary" id="cancel-show-room">Cancel</button>
                            <button type="button" class="bookinn-btn bookinn-btn-primary" id="edit-from-show-room">Edit</button>
                        </div>
                    </div>
                </div>
            `;
            $('body').append(modalHtml);
            setTimeout(function() {
                $('#bookinn-room-show-modal').addClass('is-active').show();
                $('body').addClass('bookinn-modal-open');
                // Debug: log modal presence and ids
                const $modal = $('#bookinn-room-show-modal');
                console.log('BookInn DEBUG: showRoomReadOnlyModal in DOM:', $modal.length > 0, 'ID:', $modal.attr('id'));
                console.log('BookInn DEBUG: show form ID:', $('#bookinn-room-show-form').attr('id'));
                console.log('BookInn DEBUG: Modal data-room-id:', $modal.attr('data-room-id'));
            }, 10);
        },

        /**
         * Update room types display
         */
        updateRoomTypesDisplay: function(roomTypes) {
            // Update main room types table
            const $container = $('#bookinn-room-types-table tbody');
            if ($container.length) {
                // Implementation will be added based on existing structure
                console.log('BookInn Management Unified: Updating room types display with', roomTypes.length, 'types');
            }

            // Populate room type filter dropdown
            const $typeFilter = $('#rooms-type-filter');
            if ($typeFilter.length) {
                console.log('BookInn: Populating room type filter with', roomTypes.length, 'types');
                
                // Keep the default "All Room Types" option and clear others
                $typeFilter.find('option:not(:first)').remove();
                
                // Add room types as options
                roomTypes.forEach(function(type) {
                    $typeFilter.append(
                        $('<option></option>')
                            .attr('value', type.id)
                            .text(type.name)
                    );
                });
                
                console.log('BookInn: Room type filter populated successfully');
            } else {
                console.log('BookInn: Room type filter dropdown not found');
            }
        },

        /**
         * Image Management Functions
         */
        
        /**
         * Render secondary images for read-only display
         */
        renderSecondaryImagesReadOnly: function(secondaryPics) {
            if (!secondaryPics) {
                return '<div class="bookinn-no-image"><i class="fas fa-images"></i><p>No additional images</p></div>';
            }
            
            try {
                const images = typeof secondaryPics === 'string' ? JSON.parse(secondaryPics) : secondaryPics;
                if (!Array.isArray(images) || images.length === 0) {
                    return '<div class="bookinn-no-image"><i class="fas fa-images"></i><p>No additional images</p></div>';
                }
                
                return `<div class="bookinn-images-grid-readonly">
                    ${images.map(img => `
                        <div class="bookinn-image-item-readonly">
                            <img src="${img}" alt="Room image" class="bookinn-room-image-secondary">
                        </div>
                    `).join('')}
                </div>`;
            } catch (error) {
                console.error('Error parsing secondary images:', error);
                return '<div class="bookinn-no-image"><i class="fas fa-exclamation-triangle"></i><p>Error loading images</p></div>';
            }
        },

        /**
         * Initialize image upload functionality
         */
        initImageUploads: function() {
            const self = this;
            
            // File input change handlers
            $(document).off('change', '.bookinn-file-input').on('change', '.bookinn-file-input', function(e) {
                const files = e.target.files;
                const inputId = $(this).attr('id');
                
                if (inputId === 'room-primary-image') {
                    self.handlePrimaryImageUpload(files[0]);
                } else if (inputId === 'room-secondary-images') {
                    self.handleSecondaryImagesUpload(files);
                }
            });

            // Upload area click handlers
            $(document).off('click', '.bookinn-image-upload-area').on('click', '.bookinn-image-upload-area', function(e) {
                e.preventDefault();
                const targetId = $(this).data('target');
                $('#' + targetId).click();
            });

            // Drag and drop handlers
            $(document).off('dragover dragenter drop', '.bookinn-image-upload-area');
            $(document).on('dragover dragenter', '.bookinn-image-upload-area', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).addClass('bookinn-drag-over');
            });

            $(document).on('dragleave', '.bookinn-image-upload-area', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('bookinn-drag-over');
            });

            $(document).on('drop', '.bookinn-image-upload-area', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $(this).removeClass('bookinn-drag-over');
                
                const files = e.originalEvent.dataTransfer.files;
                const targetId = $(this).data('target');
                
                if (targetId === 'room-primary-image') {
                    self.handlePrimaryImageUpload(files[0]);
                } else if (targetId === 'room-secondary-images') {
                    self.handleSecondaryImagesUpload(files);
                }
            });

            // Remove image handlers
            $(document).off('click', '.remove-image').on('click', '.remove-image', function(e) {
                e.preventDefault();
                const target = $(this).data('target');
                
                if (target === 'primary') {
                    self.removePrimaryImage();
                } else {
                    self.removeSecondaryImage($(this).data('index'));
                }
            });
        },

        /**
         * Handle primary image upload
         */
        handlePrimaryImageUpload: function(file) {
            if (!file) return;
            
            if (!this.validateImageFile(file)) return;
            
            const self = this;
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const $preview = $('#primary-image-preview');
                const $uploadArea = $('[data-target="room-primary-image"]');
                
                $preview.find('img').attr('src', e.target.result);
                $uploadArea.hide();
                $preview.show();
                
                // Store the file data for upload
                self.primaryImageFile = file;
                console.log('BookInn: Primary image loaded successfully');
            };
            
            reader.readAsDataURL(file);
        },

        /**
         * Handle secondary images upload
         */
        handleSecondaryImagesUpload: function(files) {
            if (!files || files.length === 0) return;
            
            const self = this;
            const maxImages = 10;
            const currentImages = $('.bookinn-images-grid .bookinn-image-item').length;
            
            if (currentImages + files.length > maxImages) {
                alert(`You can upload maximum ${maxImages} additional images. Currently you have ${currentImages} images.`);
                return;
            }
            
            Array.from(files).forEach((file, index) => {
                if (!self.validateImageFile(file)) return;
                
                const reader = new FileReader();
                reader.onload = function(e) {
                    self.addSecondaryImagePreview(e.target.result, file, currentImages + index);
                };
                reader.readAsDataURL(file);
            });
        },

        /**
         * Add secondary image preview
         */
        addSecondaryImagePreview: function(src, file, index) {
            const $grid = $('#secondary-images-preview');
            
            const imageHtml = `
                <div class="bookinn-image-item" data-index="${index}">
                    <img src="${src}" alt="Secondary image ${index + 1}">
                    <div class="bookinn-image-actions">
                        <button type="button" class="bookinn-btn bookinn-btn-sm bookinn-btn-danger remove-image" 
                                data-target="secondary" data-index="${index}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            
            $grid.append(imageHtml);
            
            // Store the file data
            if (!this.secondaryImageFiles) {
                this.secondaryImageFiles = [];
            }
            this.secondaryImageFiles[index] = file;
            
            console.log('BookInn: Secondary image added successfully');
        },

        /**
         * Remove primary image
         */
        removePrimaryImage: function() {
            const $preview = $('#primary-image-preview');
            const $uploadArea = $('[data-target="room-primary-image"]');
            
            $preview.hide();
            $preview.find('img').attr('src', '');
            $uploadArea.show();
            $('#room-primary-image').val('');
            
            this.primaryImageFile = null;
            console.log('BookInn: Primary image removed');
        },

        /**
         * Remove secondary image
         */
        removeSecondaryImage: function(index) {
            $(`.bookinn-image-item[data-index="${index}"]`).remove();
            
            if (this.secondaryImageFiles && this.secondaryImageFiles[index]) {
                delete this.secondaryImageFiles[index];
            }
            
            console.log('BookInn: Secondary image removed');
        },

        /**
         * Validate image file
         */
        validateImageFile: function(file) {
            const maxSize = 5 * 1024 * 1024; // 5MB
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            
            if (!allowedTypes.includes(file.type)) {
                alert('Invalid file type. Please upload JPG, PNG, or WebP images only.');
                return false;
            }
            
            if (file.size > maxSize) {
                alert('File size too large. Please upload images smaller than 5MB.');
                return false;
            }
            
            return true;
        },

        /**
         * Collect image data for form submission
         */
        collectImageData: function() {
            const imageData = {
                primary_pic: this.primaryImageFile || null,
                secondary_pics: []
            };
            
            if (this.secondaryImageFiles) {
                Object.values(this.secondaryImageFiles).forEach(file => {
                    if (file) {
                        imageData.secondary_pics.push(file);
                    }
                });
            }
            
            return imageData;
        },

        /**
         * Clear image data
         */
        clearImageData: function() {
            this.primaryImageFile = null;
            this.secondaryImageFiles = null;
            $('#primary-image-preview').hide();
            $('#secondary-images-preview').empty();
            $('.bookinn-image-upload-area').show();
            $('.bookinn-file-input').val('');
        },

        /**
         * Populate images for edit mode
         */
        populateRoomImages: function(roomData) {
            // Clear existing images
            this.clearImageData();
            
            // Populate primary image (prefer thumbnail)
            const $preview = $('#primary-image-preview');
            const $uploadArea = $('[data-target="room-primary-image"]');
            if (roomData.primary_pic_thumb) {
                $preview.find('img').attr('src', roomData.primary_pic_thumb).addClass('bookinn-room-thumb');
                $uploadArea.hide();
                $preview.show();
            } else if (roomData.primary_pic) {
                $preview.find('img').attr('src', roomData.primary_pic).addClass('bookinn-room-thumb');
                $uploadArea.hide();
                $preview.show();
            }
            
            // Populate secondary images
            if (roomData.secondary_pics) {
                try {
                    const images = typeof roomData.secondary_pics === 'string' ? 
                        JSON.parse(roomData.secondary_pics) : roomData.secondary_pics;
                    
                    if (Array.isArray(images)) {
                        images.forEach((imgSrc, index) => {
                            this.addExistingSecondaryImagePreview(imgSrc, index);
                        });
                    }
                } catch (error) {
                    console.error('Error parsing secondary images:', error);
                }
            }
        },

        /**
         * Add existing secondary image preview (for edit mode)
         */
        addExistingSecondaryImagePreview: function(src, index) {
            const $grid = $('#secondary-images-preview');
            
            const imageHtml = `
                <div class="bookinn-image-item" data-index="${index}" data-existing="true">
                    <img src="${src}" alt="Secondary image ${index + 1}">
                    <div class="bookinn-image-actions">
                        <button type="button" class="bookinn-btn bookinn-btn-sm bookinn-btn-danger remove-image" 
                                data-target="secondary" data-index="${index}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            
            $grid.append(imageHtml);
        },

        /**
         * Update bookings display
         */
        updateBookingsDisplay: function(bookings) {
            console.log('[BookInn DEBUG] Rendering booking table. Numero prenotazioni:', bookings ? bookings.length : 'undefined/null');
            console.log('[BookInn DEBUG] Bookings data:', bookings);

            // Controllo validità dati
            if (!bookings || !Array.isArray(bookings)) {
                console.error('[BookInn DEBUG] Invalid bookings data received:', bookings);
                bookings = []; // Imposta array vuoto come fallback
            }

            // Try multiple possible table containers
            const possibleContainers = [
                '#bookings-table-body',
                '#bookinn-bookings-table tbody',
                '#bookings .bookinn-bookings-content',
                '.bookinn-bookings-table tbody'
            ];

            let $tableBody = null;
            for (const selector of possibleContainers) {
                $tableBody = $(selector);
                if ($tableBody.length > 0) {
                    console.log('[BookInn DEBUG] Found table container:', selector);
                    break;
                }
            }

            if (!$tableBody || $tableBody.length === 0) {
                console.error('[BookInn DEBUG] No table body found. Available elements:', {
                    bookingsTableBody: $('#bookings-table-body').length,
                    bookinnBookingsTable: $('#bookinn-bookings-table tbody').length,
                    bookingsContent: $('#bookings .bookinn-bookings-content').length,
                    allTables: $('table').length
                });

                // Fallback: try to update the entire bookings content area
                const $container = $('#bookings .bookinn-bookings-content');
                if ($container.length) {
                    this.updateBookingsDisplayFallback($container, bookings);
                }
                return;
            }

            console.log('BookInn Management Unified: Updating bookings display with', bookings ? bookings.length : 0, 'bookings');

            if (!bookings || bookings.length === 0) {
                console.log('[BookInn DEBUG] No bookings to display, showing empty message');
                $tableBody.html('<tr><td colspan="8" class="bookinn-no-data">No bookings found</td></tr>');
                return;
            }

            let html = '';
            bookings.forEach(booking => {
                console.log('[BookInn DEBUG] Creating table row for booking:', booking.id);
                const statusClass = `bookinn-status-${booking.status}`;

                html += `
                    <tr data-booking-id="${booking.id}">
                        <td><strong>#${booking.id}</strong></td>
                        <td>
                            <div class="bookinn-guest-info">
                                <strong class="booking-guest-name">${booking.guest_first_name || ''} ${booking.guest_last_name || ''}</strong>
                                <small class="booking-guest-email">${booking.guest_email || ''}</small>
                            </div>
                        </td>
                        <td class="booking-room">${booking.room_number || 'N/A'}</td>
                        <td class="booking-checkin">${this.formatDate(booking.check_in_date)}</td>
                        <td class="booking-checkout">${this.formatDate(booking.check_out_date)}</td>
                        <td><span class="bookinn-status-badge booking-status ${statusClass}">${this.formatStatusLabel(booking.status)}</span></td>
                        <td class="booking-total"><strong>${this.formatCurrency(booking.total_amount)}</strong></td>
                        <td>
                            <div class="bookinn-table-actions">
                                <button class="bookinn-btn bookinn-btn-xs bookinn-btn-secondary bookinn-view-booking" data-booking-id="${booking.id}">View</button>
                                <button class="bookinn-btn bookinn-btn-xs bookinn-btn-primary bookinn-edit-booking" data-booking-id="${booking.id}">Edit</button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            $tableBody.html(html);

            // Debug: Check if edit buttons were created
            console.log('[BookInn DEBUG] Table updated. Edit buttons found:', $tableBody.find('.bookinn-edit-booking').length);
        },

        /**
         * Fallback method to update bookings display when table body is not found
         */
        updateBookingsDisplayFallback: function($container, bookings) {
            console.log('[BookInn DEBUG] Using fallback method to update bookings display');

            let html = '<div class="bookinn-table-container"><table class="bookinn-table" id="bookinn-bookings-table"><thead><tr>';
            html += '<th>ID</th><th>Guest</th><th>Room</th><th>Check-in</th><th>Check-out</th><th>Status</th><th>Total</th><th>Actions</th>';
            html += '</tr></thead><tbody id="bookings-table-body">';

            if (!bookings || bookings.length === 0) {
                html += '<tr><td colspan="8" class="bookinn-no-data">No bookings found</td></tr>';
            } else {
                bookings.forEach(booking => {
                    const statusClass = `bookinn-status-${booking.status}`;
                    html += `
                        <tr data-booking-id="${booking.id}">
                            <td><strong>#${booking.id}</strong></td>
                            <td>
                                <div class="bookinn-guest-info">
                                    <strong class="booking-guest-name">${booking.guest_first_name || ''} ${booking.guest_last_name || ''}</strong>
                                    <small class="booking-guest-email">${booking.guest_email || ''}</small>
                                </div>
                            </td>
                            <td class="booking-room">${booking.room_number || 'N/A'}</td>
                            <td class="booking-checkin">${this.formatDate(booking.check_in_date)}</td>
                            <td class="booking-checkout">${this.formatDate(booking.check_out_date)}</td>
                            <td><span class="bookinn-status-badge booking-status ${statusClass}">${this.formatStatusLabel(booking.status)}</span></td>
                            <td class="booking-total"><strong>${this.formatCurrency(booking.total_amount)}</strong></td>
                            <td>
                                <div class="bookinn-table-actions">
                                    <button class="bookinn-btn bookinn-btn-xs bookinn-btn-secondary bookinn-view-booking" data-booking-id="${booking.id}">View</button>
                                    <button class="bookinn-btn bookinn-btn-xs bookinn-btn-primary bookinn-edit-booking" data-booking-id="${booking.id}">Edit</button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
            }

            html += '</tbody></table></div>';
            $container.html(html);

            console.log('[BookInn DEBUG] Fallback table created with', bookings.length, 'bookings');
        },

        /**
         * Load room types in modal
         */
        loadRoomTypesInModal: function() {
            const $tbody = $('#bookinn-room-types-modal-table tbody');
            $tbody.html('<tr><td colspan="6" class="bookinn-loading">Loading room types...</td></tr>');

            this.loadRoomTypesData().then(roomTypes => {
                let html = '';
                roomTypes.forEach(type => {
                    html += `
                        <tr data-room-type-id="${type.id}">
                            <td><strong>${type.name}</strong></td>
                            <td>${type.description || '-'}</td>
                            <td>€${parseFloat(type.base_price).toFixed(2)}</td>
                            <td>${type.max_guests}</td>
                            <td>${type.room_count || 0}</td>
                            <td>
                                <div class="bookinn-actions">
                                    <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary bookinn-edit-room-type" data-room-type-id="${type.id}">Edit</button>
                                    ${type.room_count == 0 ? `<button class="bookinn-btn bookinn-btn-sm bookinn-btn-danger bookinn-delete-room-type" data-room-type-id="${type.id}">Delete</button>` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                });

                if (html === '') {
                    html = '<tr><td colspan="6" class="bookinn-no-data">No room types found. Add your first room type to get started.</td></tr>';
                }

                $tbody.html(html);
            }).catch(error => {
                $tbody.html('<tr><td colspan="6" class="bookinn-error">Error loading room types</td></tr>');
            });
        },

        // ===== BOOKING AND CALENDAR METHODS =====

        /**
         * Show booking details modal
         */
        showBookingDetails: function(bookingId) {
            console.log('BookInn Management Unified: Showing booking details for:', bookingId);

            // Use existing booking details functionality if available
            if (window.BookInnDashboard && window.BookInnDashboard.showBookingDetails) {
                window.BookInnDashboard.showBookingDetails(bookingId);
            } else {
                // Fallback implementation
                alert(`Booking details for ID: ${bookingId}\n(Full implementation pending)`);
            }
        },

        /**
         * Load dashboard data
         */
        loadDashboardData: function() {
            const self = this;

            return new Promise((resolve, reject) => {
                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'bookinn_get_dashboard_data',
                        nonce: this.config.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            self.cache.dashboardData = response.data;
                            self.updateDashboardDisplay(response.data);
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to load dashboard data');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Dashboard data AJAX error:', {xhr, status, error});
                        reject(error);
                    }
                });
            });
        },

        /**
         * Update dashboard display
         */
        updateDashboardDisplay: function(data) {
            // Update dashboard widgets with new data
            const $container = $('#dashboard .bookinn-dashboard-content');
            if ($container.length && data) {
                console.log('BookInn Management Unified: Updating dashboard display with data:', data);
                // Dashboard update implementation will be added based on existing structure
            }
        },

        // ===== MODAL LOADING STATE METHODS =====

        /**
         * Show loading state in modal
         */
        showModalLoading: function(modal) {
            const $modal = $(modal);
            const $content = $modal.find('.bookinn-modal-body');

            // Add loading overlay
            if ($content.find('.bookinn-loading-overlay').length === 0) {
                $content.append(`
                    <div class="bookinn-loading-overlay">
                        <div class="bookinn-loading-spinner">
                            <div class="bookinn-spinner"></div>
                            <p>Loading data...</p>
                        </div>
                    </div>
                `);
            }

            // Disable form elements
            $content.find('input, select, textarea, button').prop('disabled', true);
        },

        /**
         * Hide loading state in modal
         */
        hideModalLoading: function(modalSelector) {
            const $modal = $(modalSelector);
            $modal.find('.bookinn-loading-overlay').remove();
            $modal.find('input, select, textarea, button').prop('disabled', false);
        },

        /**
         * Show error state in modal
         */
        showModalError: function(modalSelector, errorMessage) {
            const $modal = $(modalSelector);
            const $content = $modal.find('.bookinn-modal-body');

            // Remove loading overlay
            $content.find('.bookinn-loading-overlay').remove();

            // Add error message
            $content.prepend(`
                <div class="bookinn-error-message">
                    <div class="bookinn-error-content">
                        <i class="bookinn-icon-warning"></i>
                        <p>${errorMessage}</p>
                        <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary" onclick="$(this).parent().parent().remove()">
                            Dismiss
                        </button>
                    </div>
                </div>
            `);

            // Re-enable form elements
            $content.find('input, select, textarea, button').prop('disabled', false);
        },

        // ===== BOOKING MANAGEMENT METHODS =====
        /**
         * View booking details
         */
        viewBooking: function(bookingId) {
            console.log('BookInn Management Unified: Viewing booking:', bookingId);

            // Show modal immediately with loading state
            this.showBookingViewModal(null, true); // true = loading state

            // Add delay to ensure autoloader and classes are ready
            setTimeout(() => {
                // Load booking data and populate view modal
                this.loadBookingData(bookingId).then(bookingData => {
                    this.populateBookingViewModal(bookingData);
                    this.hideModalLoading('#bookinn-booking-view-modal');
                    $('#bookinn-booking-view-modal .bookinn-modal-header h4').text('View Booking Details');
                }).catch(error => {
                    console.error('Error loading booking data:', error);
                    this.hideModalLoading('#bookinn-booking-view-modal');
                    this.showModalError('#bookinn-booking-view-modal', 'Error loading booking data: ' + error);
                });
            }, 500); // 500ms delay to ensure system is ready
        },

        /**
         * Load booking data by ID
         */
        loadBookingData: function(bookingId) {
            const self = this;
            return new Promise((resolve, reject) => {
                console.log('BookInn Management Unified: Loading booking data with nonces:', {
                    ajaxNonce: self.config.ajaxNonce,
                    nonce: self.config.nonce,
                    using: self.config.ajaxNonce || self.config.nonce
                });

                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'bookinn_get_booking',
                        nonce: self.config.ajaxNonce || self.config.nonce,
                        booking_id: bookingId
                    },
                    timeout: 30000, // Increased timeout to 30 seconds for autoloader compatibility
                    success: function(response) {
                        console.log('Booking data AJAX success:', response);
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to load booking data');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Booking data AJAX error:', {xhr, status, error, responseText: xhr.responseText});
                        reject(`AJAX Error: ${error} (Status: ${xhr.status})`);
                    }
                });
            });
        },

        /**
         * Populate booking form with data
         */
        populateBookingForm: function(bookingData) {
            if (!bookingData) {
                console.error('BookInn Management Unified: No booking data provided to populateBookingForm');
                return;
            }

            console.log('BookInn Management Unified: Populating STANDARD booking form with data:', bookingData);

            // Hidden fields
            $('#booking-id').val(bookingData.id || '');
            $('#booking-guest-id').val(bookingData.guest_id || '');
            $('#booking-reference').val(bookingData.booking_reference || '');

            // Guest information - support both formats
            let guestName = '';
            if (bookingData.guest_first_name && bookingData.guest_last_name) {
                guestName = `${bookingData.guest_first_name} ${bookingData.guest_last_name}`;
            } else {
                guestName = bookingData.guest_name || '';
            }
            
            $('#booking-guest-name').val(guestName);
            $('#booking-guest-email').val(bookingData.guest_email || '');
            $('#booking-guest-phone').val(bookingData.guest_phone || '');
            $('#booking-guest-address').val(bookingData.guest_address || '');

            // Reservation details
            $('#booking-check-in').val(bookingData.check_in_date || '');
            $('#booking-check-out').val(bookingData.check_out_date || '');
            $('#booking-adults').val(bookingData.adults || 1);
            $('#booking-children').val(bookingData.children || 0);

            // Room information
            $('#booking-room-id').val(bookingData.room_id || '');
            $('#booking-status').val(bookingData.status || 'pending');
            
            // Force status selection with verification
            if (bookingData.status) {
                const $statusField = $('#booking-status');
                $statusField.val(bookingData.status);
                
                // Verify the status was set correctly
                if ($statusField.val() !== bookingData.status) {
                    console.warn(`[BookInn] Status verification failed. Expected: "${bookingData.status}", Got: "${$statusField.val()}"`);
                    // Add the option if it doesn't exist
                    if ($statusField.find(`option[value="${bookingData.status}"]`).length === 0) {
                        $statusField.append(`<option value="${bookingData.status}">${bookingData.status}</option>`);
                        $statusField.val(bookingData.status);
                    }
                }
                console.log(`[BookInn] Standard Form - Booking Status Set: "${bookingData.status}" (Verified: "${$statusField.val()}")`);
            }

            // Payment information
            $('#booking-total-amount').val(bookingData.total_amount || '');
            $('#booking-payment-status').val(bookingData.payment_status || 'pending');

            // Special requests
            $('#booking-special-requests').val(bookingData.special_requests || bookingData.internal_notes || '');

            // Update room select with current room info if available
            if (bookingData.room_id && bookingData.room_number) {
                const $roomSelect = $('#booking-room-id');
                if ($roomSelect.find(`option[value="${bookingData.room_id}"]`).length === 0) {
                    $roomSelect.append(`<option value="${bookingData.room_id}" selected>Room ${bookingData.room_number} - ${bookingData.room_type_name || 'Unknown Type'}</option>`);
                } else {
                    $roomSelect.val(bookingData.room_id);
                }
            }
            
            console.log('BookInn Management Unified: STANDARD form population completed');
        },

        /**
         * Show booking view modal (read-only initially)
         */
        showBookingViewModal: function(bookingData = null, showLoading = false) {
            try {
                let modal = $('#bookinn-booking-view-modal');

                // Create modal if it doesn't exist
                if (modal.length === 0) {
                    const modalHtml = this.createBookingViewModalHTML();
                    $('body').append(modalHtml);
                    modal = $('#bookinn-booking-view-modal');
                    this.bindBookingViewEvents();
                }

                // Show loading state or populate view
                if (showLoading) {
                    this.showModalLoading(modal);
                    modal.find('.bookinn-modal-header h4').text('Loading Booking Data...');
                } else if (bookingData) {
                    this.populateBookingViewModal(bookingData);
                    modal.find('.bookinn-modal-header h4').text('View Booking Details');
                } else {
                    modal.find('.bookinn-modal-header h4').text('Booking Details');
                }

                // Show modal using unified system
                if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                    window.BookInn.Core.ModalSystem.show(modal[0]);
                } else {
                    // Fallback
                    modal.addClass('is-active');
                    $('body').addClass('bookinn-modal-open');
                }

            } catch (error) {
                console.error('Error showing booking view modal:', error);
                alert('Error opening booking details. Please try again.');
            }
        },

        /**
         * Create booking view modal HTML
         */
        createBookingViewModalHTML: function() {
            return `
                <div id="bookinn-booking-view-modal" class="bookinn-modal">
                    <div class="bookinn-modal-overlay"></div>
                    <div class="bookinn-modal-container bookinn-modal-wide">
                        <div class="bookinn-modal-header">
                            <h4>View Booking Details</h4>
                            <button class="bookinn-modal-close" type="button">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <div class="bookinn-booking-view-content">
                                <div class="bookinn-view-section">
                                    <h5><i class="fas fa-calendar"></i> Reservation Details</h5>
                                    <div class="bookinn-view-grid">
                                        <div class="bookinn-view-item">
                                            <label>Booking Reference</label>
                                            <span id="view-booking-reference">-</span>
                                        </div>
                                           <div class="bookinn-view-item">
                                            <label>Check-in Date</label>
                                            <span id="view-check-in-date">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Check-out Date</label>
                                            <span id="view-check-out-date">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Status</label>
                                            <span id="view-booking-status" class="bookinn-status-badge">-</span>
                                        </div>
                                     
                                        <div class="bookinn-view-item">
                                            <label>Adults</label>
                                            <span id="view-adults">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Children</label>
                                            <span id="view-children">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bookinn-view-section">
                                    <h5><i class="fas fa-user"></i> Guest Information</h5>
                                    <div class="bookinn-view-grid">
                                        <div class="bookinn-view-item">
                                            <label>Guest Name</label>
                                            <span id="view-guest-name">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Email</label>
                                            <span id="view-guest-email">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Phone</label>
                                            <span id="view-guest-phone">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Address</label>
                                            <span id="view-guest-address">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bookinn-view-section">
                                    <h5><i class="fas fa-bed"></i> Room Information</h5>
                                    <div class="bookinn-view-grid">
                                        <div class="bookinn-view-item">
                                            <label>Room Number</label>
                                            <span id="view-room-number">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Room Type</label>
                                            <span id="view-room-type">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bookinn-view-section">
                                    <h5><i class="fas fa-credit-card"></i> Payment & Booking Information</h5>
                                    <div class="bookinn-view-grid">
                                        <div class="bookinn-view-item">
                                            <label>Total Amount</label>
                                            <span id="view-total-amount" class="bookinn-amount">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Payment Status</label>
                                            <span id="view-payment-status" class="bookinn-status-badge">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Booking Source</label>
                                            <span id="view-booking-source">-</span>
                                        </div>
                                        <div class="bookinn-view-item">
                                            <label>Payment Method</label>
                                            <span id="view-payment-method">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bookinn-view-section">
                                    <h5><i class="fas fa-sticky-note"></i> Special Requests</h5>
                                    <div class="bookinn-view-item full-width">
                                        <span id="view-special-requests">-</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-primary bookinn-edit-booking" id="edit-booking-btn">Edit</button>
                            <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-modal-close">Close</button>
                        </div>
                    </div>
                </div>
            `;
        },

        /**
         * Create booking edit modal HTML (cloned from view modal with form inputs)
         */
        createBookingEditModalHTML: function() {
            return `
                <div id="bookinn-booking-edit-modal" class="bookinn-modal">
                    <div class="bookinn-modal-overlay"></div>
                    <div class="bookinn-modal-container bookinn-modal-wide">
                        <div class="bookinn-modal-header">
                            <h4>Edit Booking Details</h4>
                            <button class="bookinn-modal-close" type="button">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <form id="bookinn-booking-edit-form">
                                <input type="hidden" id="unified-edit-booking-id" name="booking_id">
                                <div class="bookinn-booking-view-content">
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-calendar"></i> Reservation Details</h5>
                                        <div class="bookinn-view-grid">
                                            <div class="bookinn-view-item">
                                                <label>Booking Reference</label>
                                                <span id="unified-edit-booking-reference">-</span>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Check-in Date *</label>
                                                <input type="date" id="unified-edit-check-in-date" name="check_in_date" class="bookinn-form-input" required>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Check-out Date *</label>
                                                <input type="date" id="unified-edit-check-out-date" name="check_out_date" class="bookinn-form-input" required>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Status *</label>
                                                <select id="unified-edit-booking-status" name="status" class="bookinn-form-input" required>
                                                    <option value="" disabled hidden>Select status</option>
                                                    <option value="pending">Pending</option>
                                                    <option value="confirmed">Confirmed</option>
                                                    <option value="checked_in">Checked In</option>
                                                    <option value="checked_out">Checked Out</option>
                                                    <option value="cancelled">Cancelled</option>
                                                    <option value="no_show">No Show</option>
                                                </select>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Adults *</label>
                                                <select id="unified-edit-adults" name="adults" class="bookinn-form-input" required>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                    <option value="6">6</option>
                                                </select>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Children</label>
                                                <select id="unified-edit-children" name="children" class="bookinn-form-input">
                                                    <option value="0">0</option>
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-user"></i> Guest Information</h5>
                                        <div class="bookinn-view-grid">
                                            <div class="bookinn-view-item">
                                                <label>First Name *</label>
                                                <input type="text" id="unified-edit-guest-first-name" name="guest_first_name" class="bookinn-form-input" required>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Last Name *</label>
                                                <input type="text" id="unified-edit-guest-last-name" name="guest_last_name" class="bookinn-form-input" required>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Email *</label>
                                                <input type="email" id="unified-edit-guest-email" name="guest_email" class="bookinn-form-input" required>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Phone</label>
                                                <input type="tel" id="unified-edit-guest-phone" name="guest_phone" class="bookinn-form-input">
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Address</label>
                                                <input type="text" id="unified-edit-guest-address" name="guest_address" class="bookinn-form-input">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-bed"></i> Room Information</h5>
                                        <div class="bookinn-view-grid">
                                            <div class="bookinn-view-item">
                                                <label>Room *</label>
                                                <select id="unified-edit-room-id" name="room_id" class="bookinn-form-input" required>
                                                    <option value="">Select Room...</option>
                                                </select>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Room Type</label>
                                                <span id="unified-edit-room-type">-</span>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Room Status</label>
                                                <select id="unified-edit-room-status" name="room_status" class="bookinn-form-input">
                                                    <option value="available">Available</option>
                                                    <option value="occupied">Occupied</option>
                                                    <option value="maintenance">Maintenance</option>
                                                    <option value="cleaning">Cleaning</option>
                                                    <option value="out_of_order">Out of Order</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-credit-card"></i> Payment & Booking Information</h5>
                                        <div class="bookinn-view-grid">
                                            <div class="bookinn-view-item">
                                                <label>Total Amount *</label>
                                                <input type="number" id="unified-edit-total-amount" name="total_amount" class="bookinn-form-input" step="0.01" min="0" required>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Payment Status</label>
                                                <select id="unified-edit-payment-status" name="payment_status" class="bookinn-form-input">
                                                    <option value="pending">Pending</option>
                                                    <option value="paid">Paid</option>
                                                    <option value="partial">Partial</option>
                                                    <option value="refunded">Refunded</option>
                                                </select>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Booking Source *</label>
                                                <select id="unified-edit-booking-source" name="booking_source" class="bookinn-form-input" required>
                                                    <option value="direct">Direct</option>
                                                    <option value="website">Website</option>
                                                    <option value="phone">Phone</option>
                                                    <option value="email">Email</option>
                                                    <option value="walk_in">Walk In</option>
                                                    <option value="booking_com">Booking.com</option>
                                                    <option value="airbnb">Airbnb</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>
                                            <div class="bookinn-view-item">
                                                <label>Payment Method</label>
                                                <select id="unified-edit-payment-method" name="payment_method" class="bookinn-form-input">
                                                    <option value="cash">Cash</option>
                                                    <option value="credit_card">Credit Card</option>
                                                    <option value="debit_card">Debit Card</option>
                                                    <option value="bank_transfer">Bank Transfer</option>
                                                    <option value="paypal">PayPal</option>
                                                    <option value="stripe">Stripe</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="bookinn-view-section">
                                        <h5><i class="fas fa-sticky-note"></i> Special Requests</h5>
                                        <div class="bookinn-view-item full-width">
                                            <textarea id="unified-edit-special-requests" name="special_requests" class="bookinn-form-input" rows="3" placeholder="Enter any special requests..."></textarea>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-primary" id="save-booking-btn">Save Changes</button>
                            <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-modal-close">Cancel</button>
                        </div>
                    </div>
                </div>
            `;
        },

        /**
         * Populate booking view modal with data
         */
        populateBookingViewModal: function(bookingData) {
            if (!bookingData) return;

            console.log('BookInn Management Unified: Populating booking view modal with data:', bookingData);

            // Store booking data for edit functionality as early as possible
            var $viewModal = $('#bookinn-booking-view-modal');
            if ($viewModal.length) {
                $viewModal.data('booking-data', bookingData);
            } else {
                // Se la modale viene ricreata dinamicamente, attendi che sia nel DOM
                var interval = setInterval(function() {
                    var $modal = $('#bookinn-booking-view-modal');
                    if ($modal.length) {
                        $modal.data('booking-data', bookingData);
                        clearInterval(interval);
                    }
                }, 50);
            }

            $('#view-booking-reference').text(bookingData.booking_reference || bookingData.id || '-');
            $('#view-booking-status').text(this.formatStatus(bookingData.status || 'pending')).attr('class', 'bookinn-status-badge bookinn-status-' + (bookingData.status || 'pending'));
            $('#view-check-in-date').text(this.formatDate(bookingData.check_in_date) || '-');
            $('#view-check-out-date').text(this.formatDate(bookingData.check_out_date) || '-');
            $('#view-adults').text(bookingData.adults || '1');
            $('#view-children').text(bookingData.children || '0');
            $('#view-guest-name').text(bookingData.guest_name || '-');
            $('#view-guest-email').text(bookingData.guest_email || '-');
            $('#view-guest-phone').text(bookingData.guest_phone || '-');
            $('#view-guest-address').text(bookingData.guest_address || '-');
            $('#view-room-number').text(bookingData.room_number || '-');
            $('#view-room-type').text(bookingData.room_type_name || '-');
            $('#view-total-amount').text(bookingData.total_amount ? '€' + parseFloat(bookingData.total_amount).toFixed(2) : '-');
            $('#view-payment-status').text(this.formatStatus(bookingData.payment_status || 'pending')).attr('class', 'bookinn-status-badge bookinn-status-' + (bookingData.payment_status || 'pending'));
            $('#view-booking-source').text(bookingData.booking_source ? this.formatBookingSource(bookingData.booking_source) : '-');
            $('#view-payment-method').text(bookingData.payment_method ? this.formatPaymentMethod(bookingData.payment_method) : '-');
            $('#view-special-requests').text(bookingData.special_requests || 'No special requests');
        },

        // Helper per formattare booking source in modo leggibile
        formatBookingSource: function(source) {
            const map = {
                direct: 'Direct',
                website: 'Website',
                phone: 'Phone',
                email: 'Email',
                walk_in: 'Walk In',
                booking_com: 'Booking.com',
                airbnb: 'Airbnb',
                other: 'Other'
            };
            return map[source] || source || '-';
        },

        // Helper per formattare payment method in modo leggibile
        formatPaymentMethod: function(method) {
            const map = {
                cash: 'Cash',
                credit_card: 'Credit Card',
                debit_card: 'Debit Card',
                bank_transfer: 'Bank Transfer',
                paypal: 'PayPal',
                stripe: 'Stripe',
                other: 'Other'
            };
            return map[method] || method || '-';
        },

        /**
         * Show booking edit modal (cloned from view modal with form inputs)
         */
        showBookingEditModal: function(bookingData = null, showLoading = false) {
            try {
                // Remove any existing edit modal first
                $('#bookinn-booking-edit-modal').remove();
                // Remove any conflicting modal elements

                // Create and append the modal
                const modalHtml = this.createBookingEditModalHTML();
                $('body').append(modalHtml);
                
                // Reset user modification flags for new modal session
                $('#unified-edit-booking-status').removeData('user-modified');

                // Show the modal using the global modal system
                if (window.BookInn && window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                    window.BookInn.Core.ModalSystem.show('#bookinn-booking-edit-modal');
                } else {
                    // Fallback
                    $('#bookinn-booking-edit-modal').addClass('is-active');
                    $('body').addClass('bookinn-modal-open');
                }

                // Populate form if data is provided - with timeout to ensure DOM is ready
                if (bookingData && !showLoading) {
                    const self = this;
                    
                    // Wait a bit longer to ensure modal HTML is fully rendered
                    setTimeout(function() {
                        // Verify modal fields exist before populating
                        if ($('#unified-edit-guest-email').length > 0) {
                            console.log('[BookInn] Modal fields detected, populating...');
                            self.populateBookingEditModal(bookingData);
                        } else {
                            // Wait a bit more and try again
                            console.log('[BookInn] Modal fields not ready, waiting longer...');
                            setTimeout(function() {
                                if ($('#unified-edit-guest-email').length > 0) {
                                    self.populateBookingEditModal(bookingData);
                                } else {
                                    console.error('[BookInn] Modal fields still not found after extended wait');
                                }
                            }, 200);
                        }
                    }, 150);
                }

                // Bind edit modal specific events
                this.bindBookingEditEvents();

                console.log('BookInn Management Unified: Booking edit modal opened');
            } catch (error) {
                console.error('Error showing booking edit modal:', error);
            }
        },

        /**
         * REFACTORED: Populate booking edit modal - Simple and Direct
         */
        populateBookingEditModal: function(bookingData) {
            if (!bookingData) {
                console.error('[BookInn] No booking data provided');
                return;
            }

            // Verify modal is ready before proceeding
            if (!$('#bookinn-booking-edit-modal').hasClass('is-active')) {
                console.warn('[BookInn] Modal not active, skipping population');
                return;
            }

            // Check if essential unified fields exist
            const essentialFields = ['#unified-edit-guest-email', '#unified-edit-guest-first-name', '#unified-edit-guest-last-name'];
            const missingFields = essentialFields.filter(field => $(field).length === 0);

            if (missingFields.length > 0) {
                console.error('[BookInn] Essential unified fields missing:', missingFields);
                return;
            }

            console.log('[BookInn] Populating edit modal with:', bookingData);
            console.log('[BookInn] Using UNIFIED IDs - should only populate fields with "unified-" prefix');

            // Simple, direct field population with proper fallbacks
            const fields = {
                '#unified-edit-booking-id': bookingData.id || '',
                '#unified-edit-guest-first-name': bookingData.guest_first_name || bookingData.first_name || '',
                '#unified-edit-guest-last-name': bookingData.guest_last_name || bookingData.last_name || '',
                '#unified-edit-guest-email': bookingData.guest_email || '',
                '#unified-edit-guest-phone': bookingData.guest_phone || '',
                '#unified-edit-guest-address': bookingData.guest_address || '',
                '#unified-edit-check-in-date': bookingData.check_in_date || '',
                '#unified-edit-check-out-date': bookingData.check_out_date || '',
                '#unified-edit-adults': bookingData.adults || '1',
                '#unified-edit-children': bookingData.children || '0',
                '#unified-edit-booking-status': bookingData.status || 'pending',
                '#unified-edit-room-status': bookingData.room_status || 'available',
                '#unified-edit-total-amount': bookingData.total_amount || '0',
                '#unified-edit-payment-status': bookingData.payment_status || 'pending',
                '#unified-edit-booking-source': bookingData.booking_source || 'direct',
                '#unified-edit-payment-method': bookingData.payment_method || 'cash',
                '#unified-edit-special-requests': bookingData.special_requests || ''
            };

            // Populate fields with force and verification
            Object.keys(fields).forEach(function(fieldId) {
                const $field = $(fieldId);
                const value = fields[fieldId];
                
                if ($field.length > 0) {
                    // Multiple methods to ensure population
                    $field.val(value);
                    $field.attr('value', value);
                    $field.prop('value', value);
                    
                    // Special handling for select fields (especially status)
                    if ($field.is('select')) {
                        // Ensure option exists
                        if ($field.find(`option[value="${value}"]`).length === 0) {
                            console.warn(`[BookInn] Option "${value}" not found in ${fieldId}, adding it`);
                            $field.append(`<option value="${value}">${value}</option>`);
                        }
                        // Force selection
                        $field.val(value);
                        
                        // Only trigger change for non-status fields to avoid conflicts
                        if (!fieldId.includes('status')) {
                            $field.trigger('change');
                        }
                        
                        // Extra verification for booking status
                        if (fieldId === '#unified-edit-booking-status') {
                            console.log(`[BookInn] 🎯 Booking Status Set: "${value}" (Verified: "${$field.val()}")`);
                            // Force selection again if failed
                            if ($field.val() !== value) {
                                setTimeout(function() {
                                    $field.val(value);
                                    console.log(`[BookInn] 🔄 Booking Status Retry Set: "${value}" (Verified: "${$field.val()}")`);
                                }, 100);
                            }
                        }
                        
                        // Extra verification for room status
                        if (fieldId === '#unified-edit-room-status') {
                            console.log(`[BookInn] 🏠 Room Status Set: "${value}" (Verified: "${$field.val()}")`);
                            // Force selection again if failed  
                            if ($field.val() !== value) {
                                setTimeout(function() {
                                    $field.val(value);
                                    console.log(`[BookInn] 🔄 Room Status Retry Set: "${value}" (Verified: "${$field.val()}")`);
                                }, 100);
                            }
                        }
                    }
                    
                    // Force CSS to make sure it's visible
                    $field.css({
                        'color': '#000000',
                        'background-color': '#ffffff',
                        'opacity': '1',
                        'visibility': 'visible'
                    });
                    
                    console.log(`[BookInn] ✓ ${fieldId}: "${value}"`);
                } else {
                    // Only warn for unified fields, ignore legacy ones silently
                    if (fieldId.includes('unified-')) {
                        console.warn(`[BookInn] ⚠ Unified field not found: ${fieldId}`);
                    }
                }
            });

            // Special handling for booking status - ensure it's properly selected ONLY on initial load
            if (bookingData.status) {
                console.log('[BookInn] Initial booking status verification...');
                setTimeout(function() {
                    const $statusField = $('#unified-edit-booking-status');
                    const expectedStatus = bookingData.status;
                    const currentValue = $statusField.val();
                    const userModified = $statusField.data('user-modified');
                    
                    console.log(`[BookInn] Status Check - Expected: "${expectedStatus}", Current: "${currentValue}", User Modified: ${userModified}`);
                    
                    // Only correct if the field is empty and user hasn't modified it
                    if ((!currentValue || currentValue === '' || currentValue === null) && !userModified) {
                        console.log('[BookInn] Setting initial status value...');
                        $statusField.val(expectedStatus);
                        
                        // Final verification
                        setTimeout(function() {
                            const finalValue = $statusField.val();
                            console.log(`[BookInn] Initial Status Set: "${finalValue}"`);
                        }, 50);
                    } else {
                        console.log('[BookInn] Status field already has value or user modified, preserving current selection');
                    }
                }, 200);
            }

            // Handle room selection specially
            this.populateRoomSelection(bookingData);

            // Set display fields
            $('#unified-edit-booking-reference').text(bookingData.booking_reference || bookingData.id || '-');
            $('#unified-edit-room-type').text(bookingData.room_type_name || '-');

            // Store data for later use
            $('#bookinn-booking-edit-modal').data('booking-data', bookingData);
            
            console.log('[BookInn] Modal population completed');
            console.log('[BookInn] VERIFICATION - Final field values:');
            console.log('[BookInn] Guest First Name:', $('#unified-edit-guest-first-name').val());
            console.log('[BookInn] Guest Last Name:', $('#unified-edit-guest-last-name').val());
            console.log('[BookInn] Guest Email:', $('#unified-edit-guest-email').val());
            console.log('[BookInn] Guest Phone:', $('#unified-edit-guest-phone').val());
            console.log('[BookInn] Room ID:', $('#unified-edit-room-id').val());
            console.log('[BookInn] Total Amount:', $('#unified-edit-total-amount').val());
        },

        /**
         * Handle room selection dropdown population
         */
        populateRoomSelection: function(bookingData) {
            const $roomSelect = $('#unified-edit-room-id');
            const self = this;
            
            if ($roomSelect.length === 0) {
                // Only warn if this is expected to exist (not in all contexts)
                if ($('#bookinn-booking-edit-modal').hasClass('is-active')) {
                    console.warn('[BookInn] ⚠ Room select field not found in active modal');
                }
                return;
            }

            console.log('[BookInn] Setting room selection - Room ID:', bookingData.room_id);

            // Set current room immediately if available
            if (bookingData.room_id) {
                // Force room selection with multiple methods
                const setRoomSelection = function() {
                    // Add current room option if not exists
                    if ($roomSelect.find(`option[value="${bookingData.room_id}"]`).length === 0) {
                        const roomText = bookingData.room_number ? 
                            `Room ${bookingData.room_number} - ${bookingData.room_type_name || 'Room'}` : 
                            `Room ID ${bookingData.room_id}`;
                        $roomSelect.append(`<option value="${bookingData.room_id}" selected>${roomText}</option>`);
                        console.log('[BookInn] Added room option:', roomText);
                    }
                    
                    // Force selection with multiple methods
                    $roomSelect.val(bookingData.room_id);
                    $roomSelect.prop('value', bookingData.room_id);
                    $roomSelect.find(`option[value="${bookingData.room_id}"]`).prop('selected', true);
                    
                    console.log('[BookInn] Room set to:', bookingData.room_id, '- Current value:', $roomSelect.val());
                };

                // Set immediately
                setRoomSelection();

                // Load available rooms for the dates, but preserve selection
                if (bookingData.check_in_date && bookingData.check_out_date) {
                    console.log('[BookInn] Loading available rooms while preserving selection...');
                    
                    // Store the current selection
                    const currentRoomId = bookingData.room_id;
                    
                    // Load rooms and restore selection
                    this.loadAvailableRoomsForEdit(bookingData.check_in_date, bookingData.check_out_date, currentRoomId)
                        .then(function() {
                            // Ensure selection is maintained after AJAX load
                            setTimeout(function() {
                                $roomSelect.val(currentRoomId);
                                console.log('[BookInn] Room selection restored after AJAX:', currentRoomId);
                            }, 100);
                        });
                } else {
                    // No dates available, just ensure selection persists
                    setTimeout(setRoomSelection, 100);
                    setTimeout(setRoomSelection, 500);
                }
            } else {
                console.log('[BookInn] No room ID provided in booking data');
            }
        },

        /**
         * Load available rooms for edit modal
         */
        loadAvailableRoomsForEdit: function(checkInDate, checkOutDate, currentRoomId = null) {
            const self = this;
            const $roomSelect = $('#unified-edit-room-id');
            
            // Return a Promise for better control
            return new Promise(function(resolve, reject) {
                // Show loading state
                $roomSelect.html('<option value="">Loading rooms...</option>').prop('disabled', true);

                // Load rooms via AJAX
                $.ajax({
                    url: self.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'bookinn_get_available_rooms',
                        nonce: self.config.nonce,
                        check_in_date: checkInDate,
                        check_out_date: checkOutDate,
                        exclude_booking_id: $('#unified-edit-booking-id').val() // Exclude current booking from availability check
                    },
                    success: function(response) {
                        $roomSelect.prop('disabled', false);
                        
                        if (response.success && response.data) {
                            let options = '<option value="">Select Room...</option>';
                            let currentRoomFound = false;
                            
                            response.data.forEach(function(room) {
                                const selected = (currentRoomId && room.id == currentRoomId) ? 'selected' : '';
                                if (currentRoomId && room.id == currentRoomId) {
                                    currentRoomFound = true;
                                }
                                options += `<option value="${room.id}" ${selected}>${room.room_number} - ${room.room_type_name} (€${room.price}/night)</option>`;
                            });
                            
                            // If current room is not in available list, add it anyway (as it's the current booking)
                            if (currentRoomId && !currentRoomFound) {
                                const currentBookingData = $('#bookinn-booking-edit-modal').data('booking-data');
                                const roomText = currentBookingData && currentBookingData.room_number ? 
                                    `${currentBookingData.room_number} - ${currentBookingData.room_type_name || 'Room'} (Current)` :
                                    `Room ID ${currentRoomId} (Current)`;
                                options += `<option value="${currentRoomId}" selected>${roomText}</option>`;
                                console.log('[BookInn] Added current room to list:', roomText);
                            }
                            
                            $roomSelect.html(options);
                            
                            // Force selection with multiple attempts
                            if (currentRoomId) {
                                $roomSelect.val(currentRoomId);
                                $roomSelect.find(`option[value="${currentRoomId}"]`).prop('selected', true);
                                
                                // Verify selection
                                console.log('[BookInn] Room selection after AJAX - Expected:', currentRoomId, 'Actual:', $roomSelect.val());
                            }
                            
                            resolve();
                        } else {
                            $roomSelect.html('<option value="">No rooms available</option>').prop('disabled', true);
                            reject('No rooms available');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('[BookInn] Error loading rooms for edit:', error);
                        $roomSelect.html('<option value="">Error loading rooms</option>').prop('disabled', true);
                        reject(error);
                    }
                });
            });
        },

        /**
         * Bind booking edit modal events
         */
        bindBookingEditEvents: function() {
            const self = this;

            // Edit button in view modal - open edit modal (unified)
            $(document).off('click', '#bookinn-booking-view-modal .bookinn-edit-booking').on('click', '#bookinn-booking-view-modal .bookinn-edit-booking', function(e) {
                e.preventDefault();
                console.log('Edit booking button clicked from view modal (unified handler)');
                // Recupera sempre i dati dal view modal
                var bookingData = $('#bookinn-booking-view-modal').data('booking-data');
                if (bookingData) {
                    // Chiudi la modale view
                    if (window.BookInn && window.BookInn.Core && window.BookInn.Core.ModalSystem && typeof window.BookInn.Core.ModalSystem.hide === 'function') {
                        window.BookInn.Core.ModalSystem.hide('#bookinn-booking-view-modal');
                    } else {
                        $('#bookinn-booking-view-modal').removeClass('is-active');
                        $('body').removeClass('bookinn-modal-open');
                    }
                    // Mostra la modale edit con i dati già presenti
                    self.showBookingEditModal(bookingData);
                } else {
                    console.error('No booking data found for editing in #bookinn-booking-view-modal');
                }
            });

            // Save changes button
            $(document).off('click', '#save-booking-btn').on('click', '#save-booking-btn', function(e) {
                e.preventDefault();
                self.saveBookingChanges();
            });

            // Date change handlers to reload available rooms
            $(document).off('change', '#unified-edit-check-in-date, #unified-edit-check-out-date').on('change', '#unified-edit-check-in-date, #unified-edit-check-out-date', function() {
                const checkIn = $('#unified-edit-check-in-date').val();
                const checkOut = $('#unified-edit-check-out-date').val();
                const currentRoomId = $('#unified-edit-room-id').val();
                
                if (checkIn && checkOut && checkIn < checkOut) {
                    self.loadAvailableRoomsForEdit(checkIn, checkOut, currentRoomId);
                }
            });

            // Room selection change handler - update room type display
            $(document).off('change', '#unified-edit-room-id').on('change', '#unified-edit-room-id', function() {
                const selectedOption = $(this).find('option:selected');
                const roomText = selectedOption.text();
                
                if (roomText && roomText !== 'Select Room...' && roomText !== 'Loading rooms...' && roomText !== 'No rooms available') {
                    // Extract room type from option text (format: "Room Number - Room Type (Price)")
                    const roomTypeMatch = roomText.match(/-\s*([^(]+)\s*\(/);
                    if (roomTypeMatch) {
                        $('#unified-edit-room-type').text(roomTypeMatch[1].trim());
                    }
                } else {
                    $('#unified-edit-room-type').text('-');
                }
            });
            
            // Status change handler - track user changes
            $(document).off('change', '#unified-edit-booking-status').on('change', '#unified-edit-booking-status', function() {
                const newValue = $(this).val();
                const selectedText = $(this).find('option:selected').text();
                console.log(`[DEBUG][STATUS-CHANGE] User changed booking status to: "${newValue}" (${selectedText})`);
                
                // Mark that user has manually changed the status
                $(this).data('user-modified', true);
            });
        },

        /**
         * Save booking changes
         */
        saveBookingChanges: function() {
            const self = this;
            const $form = $('#bookinn-booking-edit-form');
            const $saveBtn = $('#save-booking-btn');

            // Validate form
            if (!this.validateBookingEditForm()) {
                return;
            }

            // Disable save button and show loading state
            $saveBtn.prop('disabled', true).text('Saving...');

            // Use correct AJAX configuration
            const ajaxUrl = this.config?.ajaxUrl || window.bookinnAjax?.url || window.bookinn_dashboard?.ajax_url || '/wp-admin/admin-ajax.php';
            const nonce = this.config?.nonce || window.bookinnAjax?.nonce || window.bookinn_dashboard?.nonce;

            // Collect form data manually to ensure all fields are included
            const formData = {
                action: 'bookinn_update_booking',
                nonce: nonce,
                booking_id: $('#unified-edit-booking-id').val(),
                first_name: $('#unified-edit-guest-first-name').val(),
                last_name: $('#unified-edit-guest-last-name').val(),
                guest_email: $('#unified-edit-guest-email').val(),
                guest_phone: $('#unified-edit-guest-phone').val(),
                guest_address: $('#unified-edit-guest-address').val(),
                check_in_date: $('#unified-edit-check-in-date').val(),
                check_out_date: $('#unified-edit-check-out-date').val(),
                adults: $('#unified-edit-adults').val(),
                children: $('#unified-edit-children').val(),
                room_id: $('#unified-edit-room-id').val(),
                status: '', // Will be populated below with verification
                room_status: $('#unified-edit-room-status').val(),
                total_amount: $('#unified-edit-total-amount').val(),
                payment_status: $('#unified-edit-payment-status').val(),
                special_requests: $('#unified-edit-special-requests').val(),
                booking_source: $('#unified-edit-booking-source').val() || 'direct',
                payment_method: $('#unified-edit-payment-method').val() || 'cash'
            };
            
            // Special handling for status field to ensure correct value
            const $statusField = $('#unified-edit-booking-status');
            const statusValue = $statusField.val();
            const selectedOption = $statusField.find('option:selected');
            const selectedValue = selectedOption.val();
            const selectedText = selectedOption.text();
            
            console.log('[DEBUG][STATUS] Status Field Debug:');
            console.log('- .val():', statusValue);
            console.log('- option:selected.val():', selectedValue); 
            console.log('- option:selected.text():', selectedText);
            console.log('- Field element:', $statusField[0]);
            
            // Use the most reliable value
            formData.status = selectedValue || statusValue || 'pending';
            
            console.log('[DEBUG][STATUS] Final status used:', formData.status);
            
            // DEBUG: Mostra il valore di status che verrà inviato
            console.log('[DEBUG][SAVE] Valore status selezionato per salvataggio:', formData.status);
            console.log('[DEBUG][SAVE] Valore room status selezionato per salvataggio:', formData.room_status);
            console.log('[DEBUG][SAVE] Elemento status del form:', $('#unified-edit-booking-status')[0]);
            console.log('[DEBUG][SAVE] Elemento room status del form:', $('#unified-edit-room-status')[0]);
            console.log('[DEBUG][SAVE] Valore dell elemento status:', $('#unified-edit-booking-status').val());
            console.log('[DEBUG][SAVE] Valore dell elemento room status:', $('#unified-edit-room-status').val());
            console.log('[DEBUG][SAVE] Opzioni disponibili nel select status:', $('#unified-edit-booking-status option').map(function() { return $(this).val(); }).get());
            console.log('[DEBUG][SAVE] Opzioni disponibili nel select room status:', $('#unified-edit-room-status option').map(function() { return $(this).val(); }).get());
            console.log('[DEBUG][SAVE] Form Data completo che sarà inviato:', formData);
            console.log('[DEBUG][SAVE] JSON Form Data:', JSON.stringify(formData));
            console.log('Saving booking changes with data:', formData);

            // Send AJAX request to save changes
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: formData,
                success: function(response) {
                    console.log('[DEBUG][SAVE] Risposta backend dopo salvataggio:', response);
                    if (response.success) {
                        // Show success message
                        self.showNotification('Booking updated successfully!', 'success');

                        // Get booking ID for selective update
                        const bookingId = formData.booking_id;

                        // Prepare updated data for row update
                        const updatedData = {
                            guest_first_name: formData.first_name,
                            guest_last_name: formData.last_name,
                            guest_email: formData.guest_email,
                            check_in_date: formData.check_in_date,
                            check_out_date: formData.check_out_date,
                            adults: formData.adults,
                            children: formData.children,
                            total_amount: formData.total_amount,
                            status: formData.status,
                            booking_source: formData.booking_source,
                            payment_method: formData.payment_method
                        };

                        // Hide edit modal
                        if (window.BookInn && window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                            window.BookInn.Core.ModalSystem.hide('#bookinn-booking-edit-modal');
                        } else {
                            $('#bookinn-booking-edit-modal').removeClass('is-active');
                            $('body').removeClass('bookinn-modal-open');
                        }

                        // Try selective row update first, fallback to full refresh
                        if (bookingId && self.updateBookingRow && typeof self.updateBookingRow === 'function') {
                            self.updateBookingRow(bookingId, updatedData);
                        } else if (self.loadBookingsData && typeof self.loadBookingsData === 'function') {
                            // Fallback: refresh entire table
                            self.loadBookingsData();
                        }

                        // Refresh dashboard if available
                        if (self.loadDashboardData && typeof self.loadDashboardData === 'function') {
                            self.loadDashboardData();
                        }
                    } else {
                        self.showNotification('Error updating booking: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error saving booking changes:', error);
                    self.showNotification('Network error while saving booking changes', 'error');
                },
                complete: function() {
                    // Re-enable save button
                    $saveBtn.prop('disabled', false).text('Save Changes');
                }
            });
        },

        /**
         * Validate booking edit form
         */
        validateBookingEditForm: function() {
            let isValid = true;
            const $form = $('#bookinn-booking-edit-form');

            // Clear previous error states
            $form.find('.bookinn-form-input').removeClass('bookinn-error');

            // Check required fields
            $form.find('[required]').each(function() {
                if (!$(this).val().trim()) {
                    $(this).addClass('bookinn-error');
                    isValid = false;
                }
            });

            // Validate date range
            const checkIn = new Date($('#unified-edit-check-in-date').val());
            const checkOut = new Date($('#unified-edit-check-out-date').val());

            if (checkIn >= checkOut) {
                $('#unified-edit-check-in-date, #unified-edit-check-out-date').addClass('bookinn-error');
                this.showNotification('Check-out date must be after check-in date', 'error');
                isValid = false;
            }

            // Validate email format
            const email = $('#unified-edit-guest-email').val();
            if (email && !this.isValidEmail(email)) {
                $('#unified-edit-guest-email').addClass('bookinn-error');
                this.showNotification('Please enter a valid email address', 'error');
                isValid = false;
            }

            // Validate total amount
            const totalAmount = parseFloat($('#unified-edit-total-amount').val());
            if (isNaN(totalAmount) || totalAmount < 0) {
                $('#unified-edit-total-amount').addClass('bookinn-error');
                this.showNotification('Please enter a valid total amount', 'error');
                isValid = false;
            }

            if (!isValid) {
                this.showNotification('Please correct the highlighted fields', 'error');
            }

            return isValid;
        },

        /**
         * Validate email format
         */
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        /**
         * Show notification message
         */
        showNotification: function(message, type = 'info') {
            // Check if there's a global notification system
            if (window.BookInn && window.BookInn.Core && window.BookInn.Core.showNotification) {
                window.BookInn.Core.showNotification(message, type);
                return;
            }

            // Fallback: create simple notification
            const notification = $(`
                <div class="bookinn-notification bookinn-notification-${type}" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                    color: white;
                    padding: 15px 20px;
                    border-radius: 4px;
                    z-index: 10001;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                ">
                    ${message}
                </div>
            `);

            $('body').append(notification);

            // Auto-remove after 3 seconds
            setTimeout(() => {
                notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        },

        /**
         * Bind booking view modal events
         */
        bindBookingViewEvents: function() {
            const self = this;

            // Initialize edit modal events
            this.bindBookingEditEvents();
        },

        /**
         * Format status for display
         */
        formatStatus: function(status) {
            const statusMap = {
                'pending': 'Pending',
                'confirmed': 'Confirmed',
                'checked_in': 'Checked In',
                'checked_out': 'Checked Out',
                'cancelled': 'Cancelled',
                'no_show': 'No Show'
            };
            return statusMap[status] || status;
        },

        /**
         * Format date for display
         */
        formatDate: function(dateString) {
            if (!dateString) return null;
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });
            } catch (e) {
                return dateString;
            }
        },

        /**
         * Update single booking row in table after edit
         */
        updateBookingRow: function(bookingId, updatedData) {
            console.log('BookInn: Attempting to update booking row:', bookingId, updatedData);

            // Try multiple selectors to find the booking row
            let $row = $(`#bookings-table tbody tr[data-booking-id="${bookingId}"]`);

            if ($row.length === 0) {
                // Try alternative selectors
                $row = $(`#bookings-table tbody tr[data-id="${bookingId}"]`);
            }

            if ($row.length === 0) {
                // Try finding by booking ID in any data attribute
                $row = $(`#bookings-table tbody tr`).filter(function() {
                    return $(this).data('booking-id') == bookingId || $(this).data('id') == bookingId;
                });
            }

            console.log('BookInn: Row search results:', {
                bookingId: bookingId,
                rowsFound: $row.length,
                tableExists: $('#bookings-table').length > 0,
                tbodyExists: $('#bookings-table tbody').length > 0,
                totalRows: $('#bookings-table tbody tr').length
            });

            if ($row.length === 0) {
                console.log('BookInn: Booking row not found, refreshing entire table');
                this.refreshBookingsTable();
                return;
            }

            console.log('BookInn: Updating booking row:', bookingId, updatedData);

            // Update all editable fields if present in the table
            if (updatedData.guest_first_name && updatedData.guest_last_name) {
                $row.find('.booking-guest-name').text(`${updatedData.guest_first_name} ${updatedData.guest_last_name}`);
            }
            if (updatedData.guest_email) {
                $row.find('.booking-guest-email').text(updatedData.guest_email);
            }
            if (updatedData.guest_phone) {
                $row.find('.booking-guest-phone').text(updatedData.guest_phone);
            }
            if (updatedData.guest_address) {
                $row.find('.booking-guest-address').text(updatedData.guest_address);
            }
            if (updatedData.check_in_date) {
                $row.find('.booking-checkin').text(this.formatDateForDisplay(updatedData.check_in_date));
            }
            if (updatedData.check_out_date) {
                $row.find('.booking-checkout').text(this.formatDateForDisplay(updatedData.check_out_date));
            }
            if (updatedData.adults || updatedData.children) {
                const adults = updatedData.adults || $row.find('.booking-guests').data('adults') || 1;
                const children = updatedData.children || $row.find('.booking-guests').data('children') || 0;
                $row.find('.booking-guests').text(`${adults} adults${children > 0 ? `, ${children} children` : ''}`);
            }
            if (updatedData.room_number) {
                $row.find('.booking-room-number').text(updatedData.room_number);
            }
            if (updatedData.room_type_name) {
                $row.find('.booking-room-type').text(updatedData.room_type_name);
            }
            if (updatedData.total_amount) {
                $row.find('.booking-total').text(this.formatCurrency(updatedData.total_amount));
            }
            if (updatedData.status) {
                const $statusCell = $row.find('.booking-status');
                $statusCell.removeClass('status-pending status-confirmed status-checked_in status-checked_out status-cancelled')
                          .addClass(`status-${updatedData.status}`)
                          .text(this.formatStatusLabel(updatedData.status));
            }
            if (updatedData.payment_status) {
                $row.find('.booking-payment-status').text(this.formatStatusLabel(updatedData.payment_status));
            }
            if (updatedData.special_requests) {
                $row.find('.booking-special-requests').text(updatedData.special_requests);
            }

            // Add visual feedback for the updated row
            $row.addClass('bookinn-row-updated');
            setTimeout(() => {
                $row.removeClass('bookinn-row-updated');
            }, 2000);
        },

        /**
         * Refresh bookings table with better error handling
         */
        refreshBookingsTable: function() {
            console.log('BookInn: Refreshing bookings table...');

            // Show loading state on refresh button
            const $refreshBtn = $('#bookinn-refresh-bookings');
            const originalText = $refreshBtn.html();
            $refreshBtn.html('<i class="bookinn-icon-loading"></i> Refreshing...').prop('disabled', true);

            // Show loading state on table
            const $tableBody = $('#bookings-table-body');
            const $table = $('#bookinn-bookings-table');

            if ($tableBody.length) {
                $tableBody.html('<tr><td colspan="8" class="bookinn-loading-cell"><i class="bookinn-icon-loading"></i> Refreshing bookings...</td></tr>');
            }

            // Try multiple methods to refresh the table
            if (this.loadBookingsData && typeof this.loadBookingsData === 'function') {
                console.log('BookInn: Using loadBookingsData method');
                this.loadBookingsData().then(() => {
                    $refreshBtn.html(originalText).prop('disabled', false);
                    this.showNotification('Bookings refreshed successfully!', 'success');
                }).catch(error => {
                    console.error('BookInn: Error refreshing bookings table:', error);
                    $refreshBtn.html(originalText).prop('disabled', false);
                    this.showNotification('Error refreshing bookings table', 'error');
                });
            } else if (window.BookInn && window.BookInn.ManagementUnified && window.BookInn.ManagementUnified.loadBookingsData) {
                console.log('BookInn: Using global loadBookingsData method');
                window.BookInn.ManagementUnified.loadBookingsData();
                setTimeout(() => {
                    $refreshBtn.html(originalText).prop('disabled', false);
                    this.showNotification('Bookings refreshed successfully!', 'success');
                }, 1000);
            } else {
                console.log('BookInn: No refresh method available, using AJAX fallback');
                this.refreshBookingsViaAjax().then(() => {
                    $refreshBtn.html(originalText).prop('disabled', false);
                    this.showNotification('Bookings refreshed successfully!', 'success');
                }).catch(error => {
                    console.error('BookInn: AJAX refresh failed:', error);
                    $refreshBtn.html(originalText).prop('disabled', false);
                    this.showNotification('Error refreshing bookings. Please try again.', 'error');
                });
            }
        },

        /**
         * Refresh bookings via AJAX fallback
         */
        refreshBookingsViaAjax: function() {
            const self = this;
            const ajaxUrl = this.config?.ajaxUrl || window.bookinnAjax?.url || '/wp-admin/admin-ajax.php';
            const nonce = this.config?.nonce || window.bookinnAjax?.nonce;

            console.log('BookInn DEBUG: Refreshing bookings via AJAX fallback');

            return new Promise((resolve, reject) => {
                $.ajax({
                    url: ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'bookinn_get_bookings',
                        nonce: nonce
                    },
                    success: function(response) {
                        console.log('BookInn DEBUG: Refresh AJAX response:', response);
                        if (response.success && response.data) {
                            // Use the same update method as loadBookingsData
                            self.cache.bookings = response.data;
                            self.updateBookingsDisplay(response.data);
                            resolve(response.data);
                        } else {
                            console.error('BookInn DEBUG: Refresh AJAX failed:', response);
                            reject(response.data || 'Unknown error');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('BookInn DEBUG: Refresh AJAX error:', { xhr, status, error });
                        reject(error);
                    }
                });
            });
        },



        /**
         * Format status label for display
         */
        formatStatusLabel: function(status) {
            const labels = {
                'pending': 'Pending',
                'confirmed': 'Confirmed',
                'checked_in': 'Checked In',
                'checked_out': 'Checked Out',
                'cancelled': 'Cancelled',
                'no_show': 'No Show'
            };
            return labels[status] || status;
        },

        /**
         * Find form elements for availability check with fallback selectors
         */
        findAvailabilityFormElements: function() {
            const elements = {};

            // Primary selectors
            elements.checkin = $('#new-booking-checkin');
            elements.checkout = $('#new-booking-checkout');
            elements.adults = $('#new-booking-adults');
            elements.children = $('#new-booking-children');

            // If primary elements not found, try alternatives
            if (elements.checkin.length === 0) {
                const alternatives = [
                    'input[name="check_in_date"]',
                    'input[id*="checkin"]',
                    'input[id*="check-in"]',
                    '.bookinn-modal input[type="date"]:first'
                ];

                for (const selector of alternatives) {
                    elements.checkin = $(selector);
                    if (elements.checkin.length > 0) {
                        console.log('BookInn DEBUG: Found checkin element with alternative selector:', selector);
                        break;
                    }
                }
            }

            if (elements.checkout.length === 0) {
                const alternatives = [
                    'input[name="check_out_date"]',
                    'input[id*="checkout"]',
                    'input[id*="check-out"]',
                    '.bookinn-modal input[type="date"]:last'
                ];

                for (const selector of alternatives) {
                    elements.checkout = $(selector);
                    if (elements.checkout.length > 0) {
                        console.log('BookInn DEBUG: Found checkout element with alternative selector:', selector);
                        break;
                    }
                }
            }

            if (elements.adults.length === 0) {
                const alternatives = [
                    'select[name="adults"]',
                    'input[name="adults"]',
                    'select[id*="adults"]'
                ];

                for (const selector of alternatives) {
                    elements.adults = $(selector);
                    if (elements.adults.length > 0) {
                        console.log('BookInn DEBUG: Found adults element with alternative selector:', selector);
                        break;
                    }
                }
            }

            if (elements.children.length === 0) {
                const alternatives = [
                    'select[name="children"]',
                    'input[name="children"]',
                    'select[id*="children"]'
                ];

                for (const selector of alternatives) {
                    elements.children = $(selector);
                    if (elements.children.length > 0) {
                        console.log('BookInn DEBUG: Found children element with alternative selector:', selector);
                        break;
                    }
                }
            }

            return elements;
        },

        /**
         * Initialize room availability check functionality
         */
        initializeAvailabilityCheck: function() {
            const self = this;

            // Prevent multiple initializations
            if (this.availabilityCheckInitialized) {
                console.log('BookInn: Availability check already initialized, skipping');
                return;
            }
            this.availabilityCheckInitialized = true;

            console.log('BookInn: Initializing availability check functionality');

            // Check if button exists
            const $button = $('#check-room-availability');
            console.log('BookInn: Availability button found:', $button.length > 0);

            // Monitor required fields for availability check
            const requiredFields = ['#new-booking-checkin', '#new-booking-checkout', '#new-booking-adults'];

            function checkAvailabilityButtonState() {
                const checkin = $('#new-booking-checkin').val();
                const checkout = $('#new-booking-checkout').val();
                const adults = $('#new-booking-adults').val();

                const allFieldsFilled = checkin && checkout && adults;
                const validDateRange = checkin && checkout && new Date(checkin) < new Date(checkout);

                const shouldEnable = allFieldsFilled && validDateRange;
                const $btn = $('#check-room-availability');
                $btn.prop('disabled', !shouldEnable);

                console.log('BookInn: Availability button state check:', {
                    checkin, checkout, adults, allFieldsFilled, validDateRange, shouldEnable,
                    buttonExists: $btn.length > 0,
                    buttonDisabled: $btn.prop('disabled')
                });
            }

            // Remove any existing handlers to prevent duplicates
            $(document).off('change.availabilityCheck', requiredFields.join(', '));
            $(document).off('change.availabilityCheck', '#new-booking-children');
            $(document).off('click.availabilityCheck', '#check-room-availability');

            // Bind change events to required fields with namespace
            $(document).on('change.availabilityCheck', requiredFields.join(', '), function() {
                console.log('BookInn: Required field changed, checking button state');
                checkAvailabilityButtonState();
            });
            $(document).on('change.availabilityCheck', '#new-booking-children', function() {
                console.log('BookInn: Children field changed, checking button state');
                checkAvailabilityButtonState();
            });

            // Bind availability check button click with namespace
            $(document).on('click.availabilityCheck', '#check-room-availability', function(e) {
                e.preventDefault();
                console.log('BookInn: Check availability button clicked - event handler working!');

                if (typeof self.performAvailabilityCheck === 'function') {
                    self.performAvailabilityCheck();
                } else {
                    console.error('BookInn: performAvailabilityCheck function not found');
                    alert('Error: Availability check function not available. Please refresh the page.');
                }
            });

            // Initial state check
            setTimeout(function() {
                console.log('BookInn: Running initial availability button state check');
                checkAvailabilityButtonState();
            }, 200);

            console.log('BookInn: Availability check initialization complete');
        },

        /**
         * Perform room availability check
         */
        performAvailabilityCheck: function() {
            console.log('BookInn DEBUG: Starting availability check...');

            // Use the robust element finder
            const elements = this.findAvailabilityFormElements();

            console.log('BookInn DEBUG: Element existence check:', {
                checkinExists: elements.checkin.length > 0,
                checkoutExists: elements.checkout.length > 0,
                adultsExists: elements.adults.length > 0,
                childrenExists: elements.children.length > 0,
                checkinVisible: elements.checkin.is(':visible'),
                checkoutVisible: elements.checkout.is(':visible'),
                adultsVisible: elements.adults.is(':visible'),
                childrenVisible: elements.children.is(':visible')
            });

            const checkin = elements.checkin.val();
            const checkout = elements.checkout.val();
            const adults = parseInt(elements.adults.val()) || 1;
            const children = parseInt(elements.children.val()) || 0;
            const totalGuests = adults + children;

            // Debug: Log form values being read
            console.log('BookInn DEBUG: Form values read for availability check:', {
                checkin: checkin,
                checkout: checkout,
                adults: adults,
                children: children,
                totalGuests: totalGuests,
                checkinElement: elements.checkin.length,
                checkoutElement: elements.checkout.length,
                adultsElement: elements.adults.length,
                childrenElement: elements.children.length,
                checkinValue: elements.checkin.val(),
                checkoutValue: elements.checkout.val(),
                adultsValue: elements.adults.val(),
                childrenValue: elements.children.val(),
                checkinId: elements.checkin.attr('id'),
                checkoutId: elements.checkout.attr('id'),
                adultsId: elements.adults.attr('id'),
                childrenId: elements.children.attr('id')
            });

            // Check if we have valid values
            if (!checkin || !checkout) {
                console.error('BookInn DEBUG: Missing date values - checkin:', checkin, 'checkout:', checkout);

                // Try to check if modal is open
                const $modal = $('.bookinn-modal:visible, #bookinn-new-booking-modal:visible');
                console.log('BookInn DEBUG: Modal visibility check:', {
                    modalFound: $modal.length > 0,
                    modalVisible: $modal.is(':visible'),
                    allModals: $('.bookinn-modal').length
                });

                this.showNotification('Please select check-in and check-out dates', 'error');
                return;
            }

            if (new Date(checkin) >= new Date(checkout)) {
                console.error('BookInn DEBUG: Invalid date range - checkin:', checkin, 'checkout:', checkout);
                this.showNotification('Check-out date must be after check-in date', 'error');
                return;
            }

            console.log('BookInn DEBUG: Date validation passed - proceeding with availability check');

            // Show loading state
            const $button = $('#check-room-availability');
            const originalText = $button.html();
            $button.html('<i class="bookinn-icon-loading"></i> Checking...').prop('disabled', true);

            // AJAX call to check availability
            const ajaxUrl = this.config?.ajaxUrl || window.bookinnAjax?.url || '/wp-admin/admin-ajax.php';
            const nonce = this.config?.nonce || window.bookinnAjax?.nonce;

            const ajaxData = {
                action: 'bookinn_check_room_availability',
                nonce: nonce,
                checkin_date: checkin,  // Use consistent parameter names
                checkout_date: checkout,
                adults: adults,
                children: children,
                total_guests: totalGuests
            };

            console.log('BookInn DEBUG: AJAX data being sent for availability check:', ajaxData);

            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: ajaxData,
                success: (response) => {
                    $button.html(originalText).prop('disabled', false);
                    console.log('BookInn DEBUG: Availability check response:', response);

                    if (response.success && response.data) {
                        this.displayAvailabilityResults(response.data);
                    } else {
                        console.error('BookInn DEBUG: Availability check failed:', response);
                        this.showNotification('Error checking availability: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    $button.html(originalText).prop('disabled', false);
                    console.error('BookInn DEBUG: Availability check AJAX error:', { xhr, status, error });
                    this.showNotification('Network error while checking availability', 'error');
                }
            });
        },

        /**
         * Display availability check results
         */
        displayAvailabilityResults: function(data) {
            const $resultsContainer = $('#availability-results');
            const $roomSelect = $('#new-booking-room-select-v2');

            if (!data.available_rooms || data.available_rooms.length === 0) {
                $resultsContainer.html(`
                    <div class="bookinn-availability-no-rooms">
                        <i class="bookinn-icon-warning"></i>
                        <p>No rooms available for the selected dates and guest count.</p>
                        <p>Please try different dates or contact us for assistance.</p>
                    </div>
                `).show();

                // Clear room dropdown and show no rooms available
                $roomSelect.find('option:not(:first)').remove();
                $roomSelect.append('<option value="">No rooms available for selected dates</option>');
                return;
            }

            // Update the room dropdown with filtered results
            $roomSelect.find('option:not(:first)').remove();

            let resultsHtml = `
                <div class="bookinn-availability-header">
                    <h4>Available Rooms (${data.available_rooms.length} found)</h4>
                    <p class="bookinn-availability-note">Room dropdown has been updated with available rooms.</p>
                </div>
                <div class="bookinn-availability-list">
            `;

            data.available_rooms.forEach(room => {
                const pricePerNight = parseFloat(room.price_per_night) || 0;
                const totalNights = this.calculateNights($('#new-booking-checkin').val(), $('#new-booking-checkout').val());
                const totalPrice = pricePerNight * totalNights;

                // Add room to dropdown
                const roomTypeName = room.room_type_name || 'Standard Room';
                const optionText = `${room.room_number} - ${roomTypeName} (${this.formatCurrency(pricePerNight)}/night)`;
                $roomSelect.append(`<option value="${room.id}" data-price="${pricePerNight}">${optionText}</option>`);

                resultsHtml += `
                    <div class="bookinn-availability-room" data-room-id="${room.id}" data-price="${totalPrice}">
                        <div class="bookinn-room-info">
                            <h5>${room.room_number} - ${roomTypeName}</h5>
                            <p class="bookinn-room-details">
                                Capacity: ${room.max_occupancy} guests |
                                ${room.bed_type || 'Standard bed'} |
                                ${room.room_size || 'Standard'} m²
                            </p>
                            ${room.amenities ? `<p class="bookinn-room-amenities">${room.amenities}</p>` : ''}
                        </div>
                        <div class="bookinn-room-pricing">
                            <div class="bookinn-price-breakdown">
                                <span class="bookinn-price-per-night">${this.formatCurrency(pricePerNight)}/night</span>
                                <span class="bookinn-total-price">${this.formatCurrency(totalPrice)} total</span>
                                <small class="bookinn-nights-count">${totalNights} night${totalNights !== 1 ? 's' : ''}</small>
                            </div>
                            <button type="button" class="bookinn-btn bookinn-btn-primary bookinn-select-room"
                                    data-room-id="${room.id}" data-price="${totalPrice}">
                                Select Room
                            </button>
                        </div>
                    </div>
                `;
            });

            resultsHtml += '</div>';

            $resultsContainer.html(resultsHtml).show();

            // Show success notification
            this.showNotification(`Found ${data.available_rooms.length} available rooms. Room dropdown updated.`, 'success');

            // Bind room selection events
            this.bindRoomSelectionEvents();
        },

        /**
         * Calculate number of nights between dates
         */
        calculateNights: function(checkin, checkout) {
            if (!checkin || !checkout) return 0;
            const start = new Date(checkin);
            const end = new Date(checkout);
            const diffTime = Math.abs(end - start);
            return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        },

        /**
         * Bind room selection events
         */
        bindRoomSelectionEvents: function() {
            const self = this;

            $(document).off('click', '.bookinn-select-room').on('click', '.bookinn-select-room', function() {
                const roomId = $(this).data('room-id');
                const price = $(this).data('price');

                // Update room dropdown
                $('#new-booking-room-select-v2').val(roomId);

                // Update total amount
                $('#new-booking-total').val(price);

                // Visual feedback
                $('.bookinn-availability-room').removeClass('selected');
                $(this).closest('.bookinn-availability-room').addClass('selected');

                self.showNotification('Room selected successfully!', 'success');

                // Scroll to guest information section
                $('html, body').animate({
                    scrollTop: $('#new-guest-first-name').offset().top - 100
                }, 500);
            });
        },

        // ===== QUICK ACTION METHODS =====

        /**
         * Open room calendar with room pre-filtered
         */
        openRoomCalendar: function(roomId) {
            console.log('BookInn Management Unified: Opening calendar for room:', roomId);

            try {
                // Switch to calendar tab
                if (window.BookInnDashboard && window.BookInnDashboard.switchTab) {
                    window.BookInnDashboard.switchTab('calendar');

                    // Wait for calendar to load, then apply room filter
                    setTimeout(() => {
                        this.applyRoomFilter(roomId);
                    }, 500);
                } else {
                    // Fallback: trigger calendar tab click
                    $('.bookinn-tab-button[data-tab="calendar"]').trigger('click');

                    setTimeout(() => {
                        this.applyRoomFilter(roomId);
                    }, 500);
                }

                this.showSuccess(`Calendar opened with Room ${roomId} filter applied`);

            } catch (error) {
                console.error('Error opening room calendar:', error);
                this.showError('Error opening calendar. Please try again.');
            }
        },

        /**
         * Apply room filter to calendar
         */
        applyRoomFilter: function(roomId) {
            try {
                // Look for room filter dropdown in calendar section
                const $roomFilter = $('#calendar-room-filter, #filter-room, .calendar-room-filter');

                if ($roomFilter.length > 0) {
                    $roomFilter.val(roomId).trigger('change');
                    console.log('Room filter applied:', roomId);
                } else {
                    console.log('Room filter dropdown not found, creating filter indicator');

                    // Create a visual indicator that room is filtered
                    const $calendarSection = $('.bookinn-calendar-section, #calendar-content');
                    if ($calendarSection.length > 0) {
                        // Remove existing filter indicators
                        $calendarSection.find('.bookinn-active-filter').remove();

                        // Add filter indicator
                        $calendarSection.prepend(`
                            <div class="bookinn-active-filter">
                                <i class="fas fa-filter"></i>
                                <span>Showing Room ${roomId} only</span>
                                <button class="bookinn-btn bookinn-btn-sm bookinn-btn-secondary" onclick="$(this).parent().remove()">
                                    Clear Filter
                                </button>
                            </div>
                        `);
                    }
                }
            } catch (error) {
                console.error('Error applying room filter:', error);
            }
        },

        /**
         * Open quick booking with room pre-selected
         */
        openQuickBooking: function(roomId) {
            console.log('BookInn Management Unified: Opening quick booking for room:', roomId);
            
            // Use standard booking modal instead of wizard
            this.showBookingModal();
            
            // Pre-select room if provided
            if (roomId) {
                setTimeout(() => {
                    $('#new-booking-room-select-v2').val(roomId).trigger('change');
                }, 100);
            }
        },

        /**
         * Utility Functions
         */
        showLoading: function(selector) {
            const $element = $(selector);
            if ($element.length) {
                // Se è la tabella delle prenotazioni, mostra overlay centrato
                if (selector === '#bookinn-bookings-table' || selector === '#bookings-table-body') {
                    // Evita overlay multipli
                    if ($element.parent().find('.bookinn-loading-overlay').length === 0) {
                        $element.parent().css('position', 'relative');
                        $element.parent().append('<div class="bookinn-loading-overlay"><div class="bookinn-loading-spinner"><div class="bookinn-spinner"></div></div></div>');
                    }
                } else {
                    $element.addClass('bookinn-loading').append('<div class="bookinn-spinner"></div>');
                }
            }
        },

        // Hide loading indicator
        hideLoading: function(selector) {
            const $element = $(selector);
            if (selector === '#bookinn-bookings-table' || selector === '#bookings-table-body') {
                $element.parent().find('.bookinn-loading-overlay').remove();
            } else {
                $element.removeClass('bookinn-loading').find('.bookinn-spinner').remove();
            }
        },

        // Show error message
        showError: function(message, selector = 'body') {
            const $element = $(selector);
            if ($element.length) {
                const errorHtml = `
                    <div class="bookinn-error-message">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>${message || this.config.strings.error}</span>
                    </div>
                `;
                $element.prepend(errorHtml);
                setTimeout(() => $('.bookinn-error-message').fadeOut(), 5000);
            }
        },

        // Show success message
        showSuccess: function(message) {
            const successHtml = '<div class="bookinn-success-message">' + message + '</div>';
            $('body').prepend(successHtml);
            setTimeout(() => $('.bookinn-success-message').fadeOut(), 3000);
        },

        // Format date for display
        formatDateForDisplay: function(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
            } catch (error) {
                return dateString;
            }
        },

        // Validate email format
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        // Performance monitoring
        performanceMonitor: {
            timers: {},

            start: function(operation) {
                this.timers[operation] = performance.now();
            },

            end: function(operation) {
                if (this.timers[operation]) {
                    const duration = performance.now() - this.timers[operation];
                    console.log(`BookInn Performance: ${operation} took ${duration.toFixed(2)}ms`);
                    delete this.timers[operation];

                    // Log slow operations
                    if (duration > 1000) {
                        console.warn(`BookInn Performance Warning: ${operation} took ${duration.toFixed(2)}ms (>1s)`);
                    }

                    return duration;
                }
                return null;
            }
        },

        // Browser compatibility checks
        checkBrowserSupport: function() {
            const features = {
                fetch: typeof fetch !== 'undefined',
                promises: typeof Promise !== 'undefined',
                arrow: (() => { try { eval('() => {}'); return true; } catch(e) { return false; } })(),
                const: (() => { try { eval('const x = 1'); return true; } catch(e) { return false; } })()
            };

            const unsupported = Object.keys(features).filter(key => !features[key]);

            if (unsupported.length > 0) {
                console.warn('BookInn Management Unified: Unsupported browser features:', unsupported);
                console.warn('BookInn Management Unified: Some functionality may not work correctly');
            }

            return unsupported.length === 0;
        },

        // Global error handler for modal operations
        handleModalError: function(operation, error, fallbackAction = null) {
            console.error(`BookInn Management Unified: Modal ${operation} error:`, error);

            // Clean up any broken modal state
            this.ModalManager.cleanupDuplicateModals();

            // Execute fallback if provided
            if (fallbackAction && typeof fallbackAction === 'function') {
                try {
                    fallbackAction();
                } catch (fallbackError) {
                    console.error(`BookInn Management Unified: Fallback action failed:`, fallbackError);
                }
            }
        },

        // Modal system cleanup and error recovery
        cleanupModalSystem: function() {
            try {
                // Remove any orphaned modals
                $('.bookinn-modal').each(function() {
                    const $modal = $(this);
                    if ($modal.hasClass('bookinn-modal-open') && !$modal.is(':visible')) {
                        $modal.removeClass('bookinn-modal-open');
                    }
                });

                // Remove orphaned backdrops
                $('.bookinn-modal-backdrop').remove();

                // Reset body classes
                if (!$('.bookinn-modal-open:visible').length) {
                    $('body').removeClass('bookinn-modal-open');
                }

                console.log('BookInn Management Unified: Modal system cleaned up');
            } catch (error) {
                console.error('BookInn Management Unified: Error cleaning up modal system:', error);
            }
        },

        // ===== BOOKING FILTERS =====

        /**
         * Apply booking filters
         */
        applyBookingFilters: function() {
            const status = $('#filter-status').val();
            const dateFrom = $('#filter-date-from').val();
            const dateTo = $('#filter-date-to').val();
            const roomId = $('#filter-room').val();

            console.log('=== BookInn Filter Debug Start ===');
            console.log('BookInn Management Unified: Applying filters:', { status, dateFrom, dateTo, roomId });

            // Debug: Check if filter elements exist
            console.log('BookInn DEBUG: Filter elements found:', {
                statusElement: $('#filter-status').length,
                statusElementValue: $('#filter-status').val(),
                dateFromElement: $('#filter-date-from').length,
                dateFromElementValue: $('#filter-date-from').val(),
                dateToElement: $('#filter-date-to').length,
                dateToElementValue: $('#filter-date-to').val(),
                roomElement: $('#filter-room').length,
                roomElementValue: $('#filter-room').val(),
                roomSelectedText: $('#filter-room option:selected').text()
            });

            // Check if any filters are actually set
            const hasFilters = status || dateFrom || dateTo || roomId;
            console.log('BookInn DEBUG: Has any filters?', hasFilters);

            if (!hasFilters) {
                console.log('BookInn WARNING: No filters set, loading all bookings');
                this.showNotification('No filters selected. Showing all bookings.', 'info');
                this.clearBookingFilters();
                return;
            }

            // Show loading spinner for filter operation
            // Usa overlay su tutta la tabella
            this.showFilterLoading();
            this.showLoading('#bookinn-bookings-table');

            // Build filter object
            const filters = {};
            if (status && status !== '') {
                filters.status = status;
                console.log('BookInn DEBUG: Added status filter:', status);
            }
            if (dateFrom && dateFrom !== '') {
                filters.date_from = dateFrom;
                console.log('BookInn DEBUG: Added date_from filter:', dateFrom);
            }
            if (dateTo && dateTo !== '') {
                filters.date_to = dateTo;
                console.log('BookInn DEBUG: Added date_to filter:', dateTo);
            }
            if (roomId && roomId !== '') {
                filters.room_id = roomId;
                console.log('BookInn DEBUG: Added room_id filter:', roomId);
            }

            console.log('BookInn DEBUG: Final filters object being sent:', filters);
            console.log('BookInn DEBUG: AJAX URL:', this.config.ajaxUrl);
            console.log('BookInn DEBUG: Nonce:', this.config.nonce);

            // Load filtered data
            this.loadBookingsData(filters).then((bookings) => {
                this.hideFilterLoading();
                this.hideLoading('#bookinn-bookings-table');
                console.log('BookInn DEBUG: Filtered bookings received:', bookings.length);
                console.log('BookInn DEBUG: Bookings data:', bookings);
                console.log('=== BookInn Filter Debug End ===');
                this.showNotification(`Filters applied successfully. Found ${bookings.length} bookings.`, 'success');
            }).catch(error => {
                this.hideFilterLoading();
                this.hideLoading('#bookinn-bookings-table');
                console.error('BookInn DEBUG: Filter error:', error);
                console.log('=== BookInn Filter Debug End (ERROR) ===');
                this.showNotification('Error applying filters: ' + error, 'error');
            });
        },

        /**
         * Show loading spinner for filter operations
         */
        showFilterLoading: function() {
            // Show spinner on Apply Filters button
            const $applyBtn = $('#apply-filters');
            if ($applyBtn.length) {
                $applyBtn.prop('disabled', true);
                const originalText = $applyBtn.data('original-text') || $applyBtn.text();
                $applyBtn.data('original-text', originalText);
                $applyBtn.html('<i class="bookinn-icon-spinner fa fa-spinner fa-spin"></i> Filtering...');
            }

            // Show loading state on table
            this.showLoading('#bookinn-bookings-table');

            // Disable other filter controls
            $('#bookinn-list-filters select, #bookinn-list-filters input').prop('disabled', true);
        },

        /**
         * Hide loading spinner for filter operations
         */
        hideFilterLoading: function() {
            // Restore Apply Filters button
            const $applyBtn = $('#apply-filters');
            if ($applyBtn.length) {
                $applyBtn.prop('disabled', false);
                const originalText = $applyBtn.data('original-text');
                if (originalText) {
                    $applyBtn.html('<i class="bookinn-icon-filter"></i> ' + originalText);
                } else {
                    $applyBtn.html('<i class="bookinn-icon-filter"></i> Apply Filters');
                }
            }

            // Hide loading state on table
            this.hideLoading('#bookings-table-body');

            // Re-enable filter controls
            $('#bookinn-list-filters select, #bookinn-list-filters input').prop('disabled', false);
        },

        /**
         * Load rooms for filter dropdown
         */
        loadRoomsForFilter: function() {
            const $roomFilter = $('#filter-room');
            if (!$roomFilter.length) {
                console.log('BookInn: Room filter element not found');
                return;
            }

            // Check if already loaded
            if ($roomFilter.find('option').length > 1) {
                console.log('BookInn: Rooms already loaded in filter');
                return;
            }

            console.log('BookInn: Loading rooms for filter...');

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_get_rooms',
                    nonce: this.config.nonce
                },
                success: function(response) {
                    console.log('BookInn: Rooms filter response:', response);
                    if (response.success && response.data) {
                        $roomFilter.find('option:not(:first)').remove();
                        response.data.forEach(function(room) {
                            const roomNumber = room.room_number || room.number || 'N/A';
                            const roomType = room.room_type_name || room.type || 'Standard';
                            $roomFilter.append(`<option value="${room.id}">${roomNumber} - ${roomType}</option>`);
                        });
                        console.log('BookInn: Added', response.data.length, 'rooms to filter');
                    } else {
                        console.error('BookInn: Failed to load rooms for filter:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('BookInn: Error loading rooms for filter:', error);
                }
            });
        },

        /**
         * Clear booking filters
         */
        clearBookingFilters: function() {
            console.log('BookInn Management Unified: Clearing filters');

            // Show loading for clear operation
            const $clearBtn = $('#clear-filters');
            if ($clearBtn.length) {
                $clearBtn.prop('disabled', true);
                const originalText = $clearBtn.data('original-text') || $clearBtn.text();
                $clearBtn.data('original-text', originalText);
                $clearBtn.html('<i class="bookinn-icon-spinner fa fa-spinner fa-spin"></i> Clearing...');
            }

            // Reset filter form
            $('#filter-status').val('');
            $('#filter-date-from').val('');
            $('#filter-date-to').val('');
            $('#filter-room').val('');

            // Show loading state on table
            this.showLoading('#bookings-table-body');

            // Reload all bookings without filters
            this.loadBookingsData().then(() => {
                this.hideLoading('#bookinn-bookings-table');
                
                // Restore clear button
                if ($clearBtn.length) {
                    $clearBtn.prop('disabled', false);
                    const originalText = $clearBtn.data('original-text');
                    if (originalText) {
                        $clearBtn.html('<i class="bookinn-icon-refresh"></i> ' + originalText);
                    } else {
                        $clearBtn.html('<i class="bookinn-icon-refresh"></i> Clear Filters');
                    }
                }
                
                this.showNotification('Filters cleared successfully', 'success');
            }).catch(error => {
                this.hideLoading('#bookinn-bookings-table');
                
                // Restore clear button on error
                if ($clearBtn.length) {
                    $clearBtn.prop('disabled', false);
                    const originalText = $clearBtn.data('original-text');
                    if (originalText) {
                        $clearBtn.html('<i class="bookinn-icon-refresh"></i> ' + originalText);
                    } else {
                        $clearBtn.html('<i class="bookinn-icon-refresh"></i> Clear Filters');
                    }
                }
                
                this.showNotification('Error clearing filters: ' + error, 'error');
            });
        },

        /**
         * Get current booking filters
         */
        getBookingFilters: function() {
            return {
                status: $('#filter-status').val(),
                date_from: $('#filter-date-from').val(),
                date_to: $('#filter-date-to').val(),
                room_id: $('#filter-room').val()
            };
        },

        // ===== CALENDAR AND REPORTS INITIALIZATION =====

        /**
         * Initialize FullCalendar widget
         */
        initializeFullCalendar: function() {
            console.log('BookInn Management Unified: Initializing FullCalendar...');

            // Try multiple possible calendar element IDs
            let calendarEl = document.getElementById('bookinn-calendar');
            if (!calendarEl) {
                calendarEl = document.getElementById('bookinn-fullcalendar');
            }

            if (!calendarEl) {
                console.warn('Calendar element not found (tried #bookinn-calendar and #bookinn-fullcalendar)');
                // Try to create calendar container if missing
                this.createCalendarContainer();
                return;
            }

            console.log('Found calendar element:', calendarEl.id);

            // Check if FullCalendar is available
            if (typeof FullCalendar === 'undefined') {
                console.warn('FullCalendar not loaded, retrying in 500ms...');
                setTimeout(() => this.initializeFullCalendar(), 500);
                return;
            }

            try {
                // Destroy existing calendar if present
                if (this.calendar) {
                    this.calendar.destroy();
                }

                // Initialize FullCalendar with availability-based colors
                const calendar = new FullCalendar.Calendar(calendarEl, {
                    initialView: 'dayGridMonth',
                    headerToolbar: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'dayGridMonth,timeGridWeek,timeGridDay'
                    },
                    height: 'auto',
                    aspectRatio: 1.35,
                    editable: true,
                    selectable: true,
                    selectMirror: true,
                    dayMaxEvents: 3,
                    weekends: true,
                    nowIndicator: true,
                    eventDisplay: 'block',
                    displayEventTime: false,
                    firstDay: 1, // Monday
                    
                    // Enhanced event sources for availability display
                    eventSources: [
                        {
                            id: 'bookings',
                            events: (fetchInfo, successCallback, failureCallback) => {
                                this.loadCalendarEventsWithAvailability(fetchInfo, successCallback, failureCallback);
                            }
                        }
                    ],
                    
                    // Custom day cell rendering for availability indicators
                    dayCellDidMount: (info) => {
                        this.customizeDayCell(info);
                    },
                    
                    select: (info) => {
                        this.handleCalendarSelect(info);
                    },
                    eventClick: (info) => {
                        this.handleEventClick(info);
                    },
                    eventDidMount: (info) => {
                        this.customizeEventDisplay(info);
                    }
                });

                calendar.render();
                this.calendar = calendar;
                console.log('FullCalendar initialized successfully with availability display');

                // Load filter options and bind events with a small delay to ensure DOM is ready
                setTimeout(() => {
                    this.bindCalendarEvents();
                }, 200);

            } catch (error) {
                console.error('Error initializing FullCalendar:', error);
            }
        },

        /**
         * Load calendar events with availability information
         */
        loadCalendarEventsWithAvailability: function(fetchInfo, successCallback, failureCallback) {
            console.log('Loading calendar events with availability...', fetchInfo.startStr, 'to', fetchInfo.endStr);
            
            // Get current filter values
            const roomFilter = $('#room-filter').val() || '';
            const roomTypeFilter = $('#room-type-filter').val() || '';
            const statusFilter = $('#status-filter').val() || '';
            const availabilityFilter = $('#availability-filter').val() || 'bookings';
            
            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_get_calendar_events',
                    nonce: this.config.managementNonce || this.config.nonce,
                    start: fetchInfo.startStr,
                    end: fetchInfo.endStr,
                    room_id: roomFilter,
                    room_type_id: roomTypeFilter,
                    status: statusFilter,
                    show_availability: availabilityFilter
                },
                success: (response) => {
                    if (response.success) {
                        console.log('Calendar events loaded:', response.data.length);
                        const enhancedEvents = this.enhanceEventsWithAvailability(response.data);
                        successCallback(enhancedEvents);
                    } else {
                        console.error('Error loading calendar events:', response.message);
                        failureCallback();
                    }
                },
                error: () => {
                    console.error('AJAX error loading calendar events');
                    failureCallback();
                }
            });
        },

        /**
         * Enhance events with availability colors
         */
        enhanceEventsWithAvailability: function(events) {
            return events.map(event => {
                // Set colors based on booking status and availability
                const availabilityColor = this.getAvailabilityColor(event.extendedProps?.status || 'confirmed');
                
                return {
                    ...event,
                    backgroundColor: availabilityColor.bg,
                    borderColor: availabilityColor.border,
                    textColor: availabilityColor.text,
                    classNames: ['bookinn-calendar-event', `bookinn-availability-${event.extendedProps?.status || 'confirmed'}`]
                };
            });
        },

        /**
         * Get availability colors based on status
         * Rosso = Fully booked, Arancione = Partial availability, Verde = Available
         */
        getAvailabilityColor: function(status) {
            const colors = {
                'confirmed': {
                    bg: '#dc3545',      // Rosso - Fully booked
                    border: '#c82333',
                    text: '#ffffff'
                },
                'pending': {
                    bg: '#fd7e14',      // Arancione - Partial availability
                    border: '#e55e00',
                    text: '#ffffff'
                },
                'checked_in': {
                    bg: '#dc3545',      // Rosso - Occupied
                    border: '#c82333',
                    text: '#ffffff'
                },
                'checked_out': {
                    bg: '#28a745',      // Verde - Available
                    border: '#1e7e34',
                    text: '#ffffff'
                },
                'cancelled': {
                    bg: '#28a745',      // Verde - Available (cancelled = room available)
                    border: '#1e7e34',
                    text: '#ffffff'
                },
                'available': {
                    bg: '#28a745',      // Verde - Full availability
                    border: '#1e7e34',
                    text: '#ffffff'
                }
            };
            
            return colors[status] || colors['available'];
        },

        /**
         * Customize day cell appearance based on availability
         */
        customizeDayCell: function(info) {
            const dayEl = info.el;
            const date = info.date;
            
            // Add availability indicator class
            dayEl.classList.add('bookinn-calendar-day');
            
            // You can add additional day-level customization here
            // For example, marking weekends, holidays, etc.
        },

        /**
         * Customize event display
         */
        customizeEventDisplay: function(info) {
            const eventEl = info.el;
            const event = info.event;
            
            // Add tooltip with detailed information
            const guestName = event.extendedProps?.guest_name || 'Guest';
            const roomNumber = event.extendedProps?.room_number || 'Room';
            const status = event.extendedProps?.status || 'confirmed';
            
            eventEl.setAttribute('title', `${guestName} - ${roomNumber} (${status.toUpperCase()})`);
            
            // Add status icon
            const statusIcon = this.getStatusIcon(status);
            if (statusIcon) {
                const iconElement = document.createElement('span');
                iconElement.className = 'bookinn-event-icon';
                iconElement.innerHTML = statusIcon;
                eventEl.querySelector('.fc-event-title')?.prepend(iconElement);
            }
        },

        /**
         * Get status icon for events
         */
        getStatusIcon: function(status) {
            const icons = {
                'confirmed': '🔴',     // Red circle for booked
                'pending': '🟠',       // Orange circle for pending
                'checked_in': '🔴',    // Red circle for occupied
                'checked_out': '🟢',   // Green circle for available
                'cancelled': '🟢',     // Green circle for available
                'available': '🟢'      // Green circle for available
            };
            
            return icons[status] || '';
        },

        /**
         * Create calendar container if missing - standardized approach
         */
        createCalendarContainer: function() {
            const tabCalendar = document.getElementById('tab-calendar') || document.getElementById('calendar');
            // Use standardized container checking - only look for main container
            if (tabCalendar && !tabCalendar.querySelector('.bookinn-calendar-container')) {
                const calendarHTML = `
                    <div class="bookinn-calendar-container">
                        <!-- Calendar Header -->
                        <div class="bookinn-section-header">
                            <h3><i class="dashicons dashicons-calendar-alt"></i> Booking Calendar</h3>
                        </div>

                        <!-- Calendar Filters -->
                        <div class="bookinn-calendar-filters">
                            <div class="filter-group">
                                <label>Room Filter:</label>
                                <select id="room-filter" class="bookinn-select">
                                    <option value="">All Rooms</option>
                                </select>
                            </div>
                            <div class="filter-group">
                                <label>Status Filter:</label>
                                <select id="status-filter" class="bookinn-select">
                                    <option value="">All Statuses</option>
                                    <option value="confirmed">Confirmed</option>
                                    <option value="pending">Pending</option>
                                    <option value="checked_in">Checked In</option>
                                </select>
                            </div>
                            <div class="bookinn-calendar-legend">
                                <span class="bookinn-legend-item">
                                    <span class="bookinn-legend-color bookinn-status-confirmed"></span>
                                    Confirmed
                                </span>
                                <span class="bookinn-legend-item">
                                    <span class="bookinn-legend-color bookinn-status-pending"></span>
                                    Pending
                                </span>
                            </div>
                        </div>

                        <!-- FullCalendar Container -->
                        <div class="bookinn-calendar-wrapper">
                            <div id="bookinn-calendar" class="bookinn-calendar-widget"></div>
                        </div>
                    </div>
                `;
                tabCalendar.innerHTML = calendarHTML;
                // Retry initialization
                setTimeout(() => this.initializeFullCalendar(), 100);
            } else if (tabCalendar && tabCalendar.querySelector('.bookinn-calendar-container')) {
                // If container already exists, just initialize FullCalendar
                setTimeout(() => this.initializeFullCalendar(), 100);
            }
        },

        /**
         * Bind calendar-specific events
         */
        bindCalendarEvents: function() {
            const self = this;
            
            // Refresh calendar button
            $(document).on('click', '#refresh-calendar', function() {
                if (self.calendar) {
                    self.calendar.refetchEvents();
                }
            });
            
            // Room filter
            $(document).on('change', '#room-filter', function() {
                const roomId = $(this).val();
                if (self.calendar) {
                    self.calendar.refetchEvents();
                }
            });
            
            // Room type filter
            $(document).on('change', '#room-type-filter', function() {
                const roomTypeId = $(this).val();
                if (self.calendar) {
                    self.calendar.refetchEvents();
                }
            });
            
            // Status filter
            $(document).on('change', '#status-filter', function() {
                const status = $(this).val();
                if (self.calendar) {
                    self.calendar.refetchEvents();
                }
            });
            
            // Availability filter
            $(document).on('change', '#availability-filter', function() {
                const availability = $(this).val();
                if (self.calendar) {
                    self.calendar.refetchEvents();
                }
            });
            
            // New booking from calendar
            $(document).on('click', '#new-booking-from-calendar', function() {
                self.showBookingModal();
            });
            
            // Export calendar
            $(document).on('click', '#export-calendar', function() {
                self.exportCalendar();
            });
            
            // Load filter options with retry mechanism
            self.loadRoomFilterOptions();
            self.loadRoomTypeFilterOptions();

            // Retry filter loading if elements weren't found initially
            setTimeout(() => {
                if (!$('#room-filter').length || !$('#room-type-filter').length) {
                    console.log('Retrying filter options loading...');
                    self.loadRoomFilterOptions();
                    self.loadRoomTypeFilterOptions();
                }
            }, 1000);
        },

        /**
         * Load room options for calendar filter
         */
        loadRoomFilterOptions: function() {
            const self = this;

            // Check if room filter element exists
            const $roomFilter = $('#room-filter');
            if (!$roomFilter.length) {
                console.log('Room filter dropdown not found in DOM, skipping room options loading');
                return;
            }

            // Use correct AJAX configuration
            const ajaxUrl = this.config?.ajaxUrl || window.bookinnAjax?.url || window.bookinn_dashboard?.ajax_url || '/wp-admin/admin-ajax.php';
            const nonce = this.config?.nonce || window.bookinnAjax?.nonce || window.bookinn_dashboard?.nonce;

            console.log('Loading room filter options...');

            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_get_rooms',
                    nonce: nonce
                },
                success: function(response) {
                    console.log('Rooms response:', response);
                    if (response.success && response.data) {
                        const $roomFilter = $('#room-filter');
                        if ($roomFilter.length) {
                            // Clear existing options except "All Rooms"
                            $roomFilter.find('option:not(:first)').remove();

                            // Add room options
                            response.data.forEach(function(room) {
                                $roomFilter.append(
                                    $('<option>', {
                                        value: room.id,
                                        text: room.room_number + ' - ' + (room.room_type_name || 'Standard')
                                    })
                                );
                            });

                            console.log('Added', response.data.length, 'room options to filter');
                        } else {
                            console.warn('Room filter dropdown not found in DOM');
                        }
                    } else {
                        console.warn('No rooms data received or request failed:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Could not load room options for filter:', error);
                    console.error('XHR details:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    });
                }
            });
        },

        /**
         * Load room type options for calendar filter
         */
        loadRoomTypeFilterOptions: function() {
            const self = this;

            // Check if room type filter element exists
            const $roomTypeFilter = $('#room-type-filter');
            if (!$roomTypeFilter.length) {
                console.log('Room type filter dropdown not found in DOM, skipping room type options loading');
                return;
            }

            // Use correct AJAX configuration
            const ajaxUrl = this.config?.ajaxUrl || window.bookinnAjax?.url || window.bookinn_dashboard?.ajax_url || '/wp-admin/admin-ajax.php';
            const nonce = this.config?.nonce || window.bookinnAjax?.nonce || window.bookinn_dashboard?.nonce;

            console.log('Loading room type filter options...');

            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_get_room_types',
                    nonce: nonce
                },
                success: function(response) {
                    console.log('Room types response:', response);
                    if (response.success && response.data) {
                        const $roomTypeFilter = $('#room-type-filter');
                        if ($roomTypeFilter.length) {
                            // Clear existing options except "All Types"
                            $roomTypeFilter.find('option:not(:first)').remove();

                            // Add room type options
                            response.data.forEach(function(roomType) {
                                $roomTypeFilter.append(
                                    $('<option>', {
                                        value: roomType.id,
                                        text: roomType.name + ' (' + (roomType.capacity || '2') + ' guests)'
                                    })
                                );
                            });

                            console.log('Added', response.data.length, 'room type options to filter');
                        } else {
                            console.warn('Room type filter dropdown not found in DOM');
                        }
                    } else {
                        console.warn('No room types data received or request failed:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Could not load room type options for filter:', error);
                    console.error('XHR details:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    });
                }
            });
        },

        /**
         * Export calendar functionality
         */
        exportCalendar: function() {
            console.log('Exporting calendar...');
            // Implement export functionality here
            alert('Funzionalità di esportazione calendario in sviluppo');
        },

        /**
         * Handle calendar event click
         */
        handleEventClick: function(info) {
            console.log('Calendar event clicked:', info);
            // Implementation for editing booking
        },

        /**
         * Initialize reports charts
         */
        initializeReportsCharts: function() {
            console.log('BookInn Management Unified: Initializing reports charts...');

            // Wait for Chart.js to be available
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded, retrying in 500ms...');
                setTimeout(() => this.initializeReportsCharts(), 500);
                return;
            }

            // Initialize reports-specific charts
            setTimeout(() => {
                this.initReportsRevenueChart();
                this.initReportsOccupancyChart();
                this.initReportsRoomTypeChart();
                this.initReportsBookingSourceChart();
            }, 500);
        },

        /**
         * Initialize Reports Revenue Chart
         */
        initReportsRevenueChart: function() {
            const canvas = document.getElementById('revenue-by-month-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            this.charts.reportsRevenue = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Monthly Revenue (€)',
                        data: [5200, 6800, 7500, 8200, 9100, 8800],
                        borderColor: '#274690',
                        backgroundColor: 'rgba(39, 70, 144, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        /**
         * Initialize Reports Occupancy Chart
         */
        initReportsOccupancyChart: function() {
            const canvas = document.getElementById('occupancy-trends-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            this.charts.reportsOccupancy = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Occupancy Rate %',
                        data: [78, 85, 92, 88, 95, 90],
                        backgroundColor: 'rgba(39, 70, 144, 0.8)',
                        borderColor: '#274690',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        },

        /**
         * Initialize Reports Room Type Chart
         */
        initReportsRoomTypeChart: function() {
            const canvas = document.getElementById('room-type-performance-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            this.charts.reportsRoomType = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Standard', 'Deluxe', 'Suite', 'Executive'],
                    datasets: [{
                        label: 'Revenue (€)',
                        data: [15000, 22000, 35000, 18000],
                        backgroundColor: 'rgba(39, 70, 144, 0.8)',
                        borderColor: '#274690',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        /**
         * Initialize Reports Booking Source Chart
         */
        initReportsBookingSourceChart: function() {
            const canvas = document.getElementById('booking-sources-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            this.charts.reportsBookingSource = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Direct', 'Booking.com', 'Airbnb', 'Expedia', 'Other'],
                    datasets: [{
                        data: [35, 25, 20, 12, 8],
                        backgroundColor: [
                            '#274690', '#3b5998', '#4a6fa5', '#5985b3', '#689bc1'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                fontSize: 11
                            }
                        }
                    }
                }
            });
        },

        /**
         * Perform check-in
         */
        performCheckin: function(bookingId) {
            console.log('BookInn Management Unified: Performing check-in for booking:', bookingId);

            if (confirm('Confirm check-in for this booking?')) {
                // Update booking status to checked_in
                this.updateBookingStatus(bookingId, 'checked_in');
                this.showSuccess('Guest checked in successfully!');
            }
        },

        /**
         * Perform check-out
         */
        performCheckout: function(bookingId) {
            console.log('BookInn Management Unified: Performing check-out for booking:', bookingId);

            if (confirm('Confirm check-out for this booking?')) {
                // Update booking status to checked_out
                this.updateBookingStatus(bookingId, 'checked_out');
                this.showSuccess('Guest checked out successfully!');
            }
        },

        /**
         * Show room status modal
         */
        showRoomStatusModal: function(roomId) {
            console.log('BookInn Management Unified: Showing room status modal for room:', roomId);

            // Create simple room status modal
            const modalHtml = `
                <div id="room-status-modal" class="bookinn-modal" role="dialog">
                    <div class="bookinn-modal-content">
                        <div class="bookinn-modal-header">
                            <h4>Update Room Status</h4>
                            <button class="bookinn-close-modal" type="button">&times;</button>
                        </div>
                        <div class="bookinn-modal-body">
                            <div class="bookinn-form-group">
                                <label for="room-status-select">Room Status</label>
                                <select id="room-status-select" class="bookinn-select">
                                    <option value="available">Available</option>
                                    <option value="occupied">Occupied</option>
                                    <option value="maintenance">Maintenance</option>
                                    <option value="cleaning">Cleaning</option>
                                </select>
                            </div>
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-close-modal">Cancel</button>
                            <button type="button" class="bookinn-btn bookinn-btn-primary" onclick="BookInnDashboard.updateRoomStatus(${roomId})">Update Status</button>
                        </div>
                    </div>
                </div>
            `;

            $('body').append(modalHtml);
            this.ModalManager.show(document.getElementById('room-status-modal'));
        },

        /**
         * Update room status
         */
        updateRoomStatus: function(roomId) {
            const status = $('#room-status-select').val();
            console.log('BookInn Management Unified: Updating room status:', roomId, status);

            // Close modal
            this.ModalManager.hide(document.getElementById('room-status-modal'));
            $('#room-status-modal').remove();

            this.showSuccess(`Room status updated to: ${status}`);
        },

        /**
         * Update booking status
         */
        updateBookingStatus: function(bookingId, status) {
            console.log('BookInn Management Unified: Updating booking status:', bookingId, status);

            // Here you would make an AJAX call to update the booking status
            // For now, just show success message
            setTimeout(() => {
                this.loadBookingsData(); // Refresh the bookings list
            }, 500);
        },

        // ===== CHART INITIALIZATION =====

        /**
         * Initialize all charts
         */
        initCharts: function() {
            console.log('BookInn Management Unified: Initializing charts...');

            // Wait for Chart.js to be available
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded, retrying in 500ms...');
                setTimeout(() => this.initCharts(), 500);
                return;
            }

            // Initialize charts when dashboard tab is active
            setTimeout(() => {
                this.initRevenueChart();
                this.initForecastChart();
                this.initBookingDaysChart();
                this.initBookingRatesChart();
            }, 1000);
        },

        /**
         * Load chart data with filtering
         */
        loadChartData: function(chartType, period) {
            console.log(`Loading ${chartType} chart data for period: ${period}`);

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_get_chart_data',
                    chart_type: chartType,
                    period: period,
                    nonce: this.config.nonce
                },
                success: (response) => {
                    if (response && response.success && response.data) {
                        this.updateChartData(chartType, period, response.data);
                    } else {
                        console.error(`Failed to load ${chartType} chart data:`, response);
                        // Load mock data as fallback
                        this.loadMockChartData(chartType, period);
                    }
                },
                error: (xhr, status, error) => {
                    console.error(`Error loading ${chartType} chart data:`, error);
                    // Load mock data as fallback
                    this.loadMockChartData(chartType, period);
                }
            });
        },

        /**
         * Update chart data
         */
        updateChartData: function(chartType, period, data) {
            // Map chart type names to chart instances
            const chartMap = {
                'revenue': 'revenue',
                'forecast': 'forecast',
                'booking-days': 'bookingDays',
                'booking-rates': 'bookingRates'
            };

            const chartInstanceKey = chartMap[chartType] || chartType;
            const chartInstance = this.charts[chartInstanceKey];

            if (!chartInstance || !data) {
                console.warn(`Chart instance not found for type: ${chartType} (mapped to: ${chartInstanceKey})`);
                return;
            }

            // Update chart data based on type
            if (data.labels) {
                chartInstance.data.labels = data.labels;
            }

            if (data.datasets && data.datasets.length > 0) {
                chartInstance.data.datasets.forEach((dataset, index) => {
                    if (data.datasets[index]) {
                        dataset.data = data.datasets[index].data || data.datasets[index];
                    }
                });
            } else if (data.values) {
                // Handle simple data format
                chartInstance.data.datasets[0].data = data.values;
            }

            // Special handling for different chart types
            if (chartType === 'booking-days') {
                this.updateBookingDaysChart(chartInstance, data, period);
            } else if (chartType === 'booking-rates') {
                this.updateBookingRatesChart(chartInstance, data, period);
            }

            chartInstance.update();
            console.log(`Updated ${chartType} chart for period: ${period}`);
        },

        /**
         * Update Booking Days chart with period-specific data
         */
        updateBookingDaysChart: function(chartInstance, data, period) {
            // Generate period-appropriate labels and data
            if (period === '7') {
                chartInstance.data.labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                chartInstance.data.datasets[0].data = data.values || [8, 6, 12, 9, 15, 22, 18];
            } else if (period === '30') {
                chartInstance.data.labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
                chartInstance.data.datasets[0].data = data.values || [45, 38, 52, 48];
            } else if (period === '90') {
                chartInstance.data.labels = ['Month 1', 'Month 2', 'Month 3'];
                chartInstance.data.datasets[0].data = data.values || [125, 142, 138];
            }
        },

        /**
         * Update Booking Rates chart with period-specific data
         */
        updateBookingRatesChart: function(chartInstance, data, period) {
            // Generate period-appropriate labels and data
            if (period === '7') {
                chartInstance.data.labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'];
                chartInstance.data.datasets[0].data = data.values || [72, 68, 75, 82, 88, 85, 79];
            } else if (period === '30') {
                chartInstance.data.labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
                chartInstance.data.datasets[0].data = data.values || [65, 72, 78, 85];
            } else if (period === '90') {
                chartInstance.data.labels = ['Month 1', 'Month 2', 'Month 3'];
                chartInstance.data.datasets[0].data = data.values || [68, 75, 82];
            }
        },

        /**
         * Load mock chart data as fallback
         */
        loadMockChartData: function(chartType, period) {
            console.log(`Loading mock data for ${chartType} chart, period: ${period}`);

            const mockData = this.generateMockChartData(chartType, period);
            this.updateChartData(chartType, period, mockData);
        },

        /**
         * Generate mock chart data
         */
        generateMockChartData: function(chartType, period) {
            let periodData, values = [];

            // Define period-specific data structures
            if (chartType === 'booking-days') {
                if (period === '7') {
                    periodData = { labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'], count: 7 };
                    values = [8, 6, 12, 9, 15, 22, 18]; // Mock booking counts per day
                } else if (period === '30') {
                    periodData = { labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'], count: 4 };
                    values = [45, 38, 52, 48]; // Mock booking counts per week
                } else if (period === '90') {
                    periodData = { labels: ['Month 1', 'Month 2', 'Month 3'], count: 3 };
                    values = [125, 142, 138]; // Mock booking counts per month
                }
            } else if (chartType === 'booking-rates') {
                if (period === '7') {
                    periodData = { labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'], count: 7 };
                    values = [72, 68, 75, 82, 88, 85, 79]; // Mock booking rates %
                } else if (period === '30') {
                    periodData = { labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'], count: 4 };
                    values = [65, 72, 78, 85]; // Mock booking rates %
                } else if (period === '90') {
                    periodData = { labels: ['Month 1', 'Month 2', 'Month 3'], count: 3 };
                    values = [68, 75, 82]; // Mock booking rates %
                }
            } else {
                // Default periods for revenue and forecast charts
                const periods = {
                    '7': { labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'], count: 7 },
                    '30': { labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'], count: 4 },
                    '90': { labels: ['Month 1', 'Month 2', 'Month 3'], count: 3 },
                    '365': { labels: ['Q1', 'Q2', 'Q3', 'Q4'], count: 4 }
                };

                periodData = periods[period] || periods['30'];

                for (let i = 0; i < periodData.count; i++) {
                    if (chartType === 'revenue') {
                        values.push(Math.floor(Math.random() * 5000) + 1000);
                    } else if (chartType === 'forecast') {
                        values.push(Math.floor(Math.random() * 100) + 20);
                    } else {
                        values.push(Math.floor(Math.random() * 100) + 10);
                    }
                }
            }

            return {
                labels: periodData.labels,
                values: values
            };
        },

        /**
         * Initialize Revenue Chart
         */
        initRevenueChart: function() {
            const canvas = document.getElementById('bookinn-revenue-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            this.charts.revenue = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                    datasets: [{
                        label: 'Revenue (€)',
                        data: [1200, 1900, 3000, 2500],
                        borderColor: '#274690',
                        backgroundColor: 'rgba(39, 70, 144, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        },

        /**
         * Initialize Forecast Chart
         */
        initForecastChart: function() {
            const canvas = document.getElementById('bookinn-forecast-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            this.charts.forecast = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                    datasets: [{
                        label: 'Occupancy %',
                        data: [75, 85, 92, 68],
                        backgroundColor: 'rgba(39, 70, 144, 0.8)',
                        borderColor: '#274690',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        },

        /**
         * Initialize Booking Days Chart
         */
        initBookingDaysChart: function() {
            const canvas = document.getElementById('bookinn-booking-days-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            this.charts.bookingDays = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        data: [12, 8, 15, 10, 18, 25, 20],
                        backgroundColor: [
                            '#274690', '#3b5998', '#4a6fa5', '#5985b3',
                            '#689bc1', '#77b1cf', '#86c7dd'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                fontSize: 11
                            }
                        }
                    }
                }
            });
        },

        /**
         * Initialize Booking Rates Chart
         */
        initBookingRatesChart: function() {
            const canvas = document.getElementById('bookinn-booking-rates-chart');
            if (!canvas) return;

            const ctx = canvas.getContext('2d');
            this.charts.bookingRates = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Booking Rate %',
                        data: [65, 72, 78, 85, 92, 88],
                        borderColor: '#274690',
                        backgroundColor: 'rgba(39, 70, 144, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        },
        
        /**
         * Populate edit booking form
         */
        populateEditBookingForm: function(booking) {
            console.log('Populating edit booking form with data:', booking);
            if (!booking) {
                console.error('No booking data provided');
                return;
            }

            try {
                // Set booking ID
                $('#edit-booking-id').val(booking.id);
                console.log('Set booking ID:', booking.id);

                // Debug: mostra valore status ricevuto e tutte le opzioni disponibili
                const validStatuses = ['pending','confirmed','checked_in','checked_out','cancelled','no_show'];
                let statusValue = (booking.status || '').toLowerCase();
                console.log('[DEBUG] Valore status ricevuto:', booking.status, '| Normalizzato:', statusValue);
                const $statusSelect = $('#edit-booking-status');
                // Rimuovi qualsiasi selected dalle opzioni (eccetto il placeholder)
                $statusSelect.find('option').prop('selected', false);
                $statusSelect.find('option[value=""]').prop('selected', true);
                $statusSelect.find('option').each(function() {
                    console.log('[DEBUG] Opzione select:', $(this).val());
                });
                if (validStatuses.includes(statusValue)) {
                    $statusSelect.val(statusValue).trigger('change');
                    console.log('Set status:', statusValue);
                } else {
                    $statusSelect.val('').trigger('change');
                    console.log('Status non valido o mancante, nessuna selezione impostata');
                }
                
                $('#edit-total-amount').val(booking.total_amount || '0');
                console.log('Set total amount:', booking.total_amount);
                
                // Check-in/out dates - support multiple field formats
                const checkinDate = booking.checkin_date || booking.check_in_date || '';
                const checkoutDate = booking.checkout_date || booking.check_out_date || '';
                
                $('#edit-checkin').val(checkinDate);
                $('#edit-booking-checkin').val(checkinDate);
                console.log('Set checkin:', checkinDate);
                
                $('#edit-checkout').val(checkoutDate);
                $('#edit-booking-checkout').val(checkoutDate);
                console.log('Set checkout:', checkoutDate);
                
                $('#edit-adults').val(booking.adults || '1');
                $('#edit-booking-adults').val(booking.adults || '1');
                console.log('Set adults:', booking.adults);
                
                $('#edit-children').val(booking.children || '0');
                $('#edit-booking-children').val(booking.children || '0');
                console.log('Set children:', booking.children);
                
                // Guest information - support multiple name formats
                let guestName = '';
                if (booking.guest_first_name && booking.guest_last_name) {
                    guestName = `${booking.guest_first_name} ${booking.guest_last_name}`;
                    console.log('Set guest name from first+last:', booking.guest_first_name, booking.guest_last_name);
                } else {
                    guestName = booking.guest_name || '';
                    console.log('Set guest name:', booking.guest_name);
                }
                
                $('#booking-guest-name').val(guestName);
                $('#booking-guest-email').val(booking.guest_email || '');
                $('#booking-guest-phone').val(booking.guest_phone || '');
                $('#booking-guest-address').val(booking.guest_address || '');
                console.log('Set guest name:', guestName);
                console.log('Set guest email:', booking.guest_email);
                console.log('Set guest phone:', booking.guest_phone);
                console.log('Set guest address:', booking.guest_address);

                $('#booking-special-requests').val(booking.special_requests || booking.notes || '');
                $('#booking-internal-notes').val(booking.internal_notes || '');
                console.log('Set special requests:', booking.special_requests || booking.notes);
                console.log('Set internal notes:', booking.internal_notes);
                
                // Set room info (readonly field)
                if (booking.room_number && booking.room_type_name) {
                    $('#edit-room-info').val(`Room ${booking.room_number} - ${booking.room_type_name}`);
                    console.log('Set room info from room_number+room_type_name:', booking.room_number, booking.room_type_name);
                } else if (booking.room_id) {
                    $('#edit-room-info').val(`Room ID: ${booking.room_id}`);
                    console.log('Set room info from room_id:', booking.room_id);
                } else {
                    $('#edit-room-info').val('Room information not available');
                    console.log('No room information available');
                }
                
                // Update summary
                this.updateEditBookingSummary();
                
                console.log('Edit booking form populated successfully');
            } catch (error) {
                console.error('Error populating edit booking form:', error);
            }
        },

        /**
         * Calculate booking total
         */
        calculateBookingTotal: function($form) {
            const roomSelect = $form.find('[name="room_id"]');
            const checkinDate = $form.find('[name="check_in_date"]').val();
            const checkoutDate = $form.find('[name="check_out_date"]').val();
            const selectedOption = roomSelect.find(':selected');
            const pricePerNight = parseFloat(selectedOption.data('price')) || 0;

            if (checkinDate && checkoutDate && pricePerNight > 0) {
                const nights = this.calculateNights(checkinDate, checkoutDate);
                const total = nights * pricePerNight;
                $form.find('[name="total_amount"]').val(total.toFixed(2));
            }
        },

        /**
         * Calculate booking total from room selection change
         */
        calculateBookingTotalFromRoomSelection: function() {
            const modal = $('#bookinn-booking-modal');
            const checkinDate = modal.find('#new-booking-checkin').val();
            const checkoutDate = modal.find('#new-booking-checkout').val();
            const roomSelect = modal.find('#new-booking-room-select-v2');
            const selectedOption = roomSelect.find(':selected');
            const roomId = selectedOption.val();
            const pricePerNight = parseFloat(selectedOption.data('price')) || 0;
            const roomStatus = selectedOption.data('status') || 'unknown';

            console.log('BookInn: Calculating total for room selection:', {
                roomId: roomId,
                pricePerNight: pricePerNight,
                roomStatus: roomStatus,
                checkinDate: checkinDate,
                checkoutDate: checkoutDate
            });

            // Calculate and display total amount
            if (checkinDate && checkoutDate && pricePerNight > 0) {
                const nights = this.calculateNights(checkinDate, checkoutDate);
                const total = nights * pricePerNight;
                modal.find('#new-booking-total').val(total.toFixed(2));
                console.log('BookInn: Calculated total:', total, 'for', nights, 'nights at', pricePerNight, 'per night');
            } else {
                modal.find('#new-booking-total').val('');
            }

            // Display room status
            const statusDisplay = modal.find('#room-status-display');
            if (roomId && roomId !== '' && statusDisplay.length) {
                const statusText = roomStatus.charAt(0).toUpperCase() + roomStatus.slice(1).replace('_', ' ');
                statusDisplay.find('.room-status-value').text(statusText).removeClass('available occupied maintenance out_of_order').addClass(roomStatus);
                statusDisplay.show();
                console.log('BookInn: Room status displayed:', roomStatus);
            } else if (statusDisplay.length) {
                statusDisplay.hide();
            }
        },

        /**
         * Load available rooms for new booking modal
         */
        loadAvailableRoomsForNewBooking: function() {

            // Use more specific selectors to avoid duplicate ID issues
            const modal = $('#bookinn-booking-modal');
            const checkinDate = modal.find('#new-booking-checkin').val();
            const checkoutDate = modal.find('#new-booking-checkout').val();
            const adults = parseInt(modal.find('#new-booking-adults').val()) || 1;
            const children = parseInt(modal.find('#new-booking-children').val()) || 0;
            const roomSelect = modal.find('#new-booking-room-select-v2');
            const self = this;

            // DEBUG: Check for duplicate elements
            const checkinElements = $('[id="new-booking-checkin"]');
            const checkoutElements = $('[id="new-booking-checkout"]');
            const adultsElements = $('[id="new-booking-adults"]');
            
            console.log('[BookInn DEBUG] Elementi trovati nel DOM:', {
                checkinCount: checkinElements.length,
                checkoutCount: checkoutElements.length,
                adultsCount: adultsElements.length,
                checkinValues: checkinElements.map((i, el) => el.value).get(),
                checkoutValues: checkoutElements.map((i, el) => el.value).get(),
                adultsValues: adultsElements.map((i, el) => el.value).get(),
                modalCheckinValue: modal.find('#new-booking-checkin').val(),
                modalCheckoutValue: modal.find('#new-booking-checkout').val(),
                modalAdultsValue: modal.find('#new-booking-adults').val()
            });

            // DEBUG: Log values from DOM just before AJAX
            console.log('[BookInn DEBUG] Valori input prima della chiamata AJAX:', {
                checkinDate: checkinDate,
                checkoutDate: checkoutDate,
                adults: adults,
                children: children,
                modalFound: modal.length > 0,
                roomSelectFound: roomSelect.length > 0,
                roomSelectElement: roomSelect[0]
            });

            if (!roomSelect.length) {
                console.error('Room select element not found! Looking for #new-booking-room-select-v2');
                console.log('Available elements:', $('[id*="room"]').map(function() { return this.id; }).get());
                return;
            }

            if (!checkinDate || !checkoutDate) {
                console.log('Missing dates for room availability check');
                return;
            }

            // Validate dates
            if (checkinDate >= checkoutDate) {
                console.log('Invalid date range: check-in must be before check-out');
                roomSelect.find('option:not(:first)').remove();
                roomSelect.append('<option value="">Invalid date range</option>');
                return;
            }

            // Clear current options except the first one
            roomSelect.find('option:not(:first)').remove();
            roomSelect.append('<option value="">Loading...</option>');

            // Use correct AJAX configuration
            const ajaxUrl = this.config?.ajaxUrl || window.bookinnAjax?.url || window.bookinn_dashboard?.ajax_url || '/wp-admin/admin-ajax.php';
            const nonce = this.config?.nonce || window.bookinnAjax?.nonce || window.bookinn_dashboard?.nonce;

            console.log('Loading available rooms with config:', {
                url: ajaxUrl,
                nonce: nonce ? 'present' : 'missing',
                nonceValue: nonce ? nonce.substring(0, 10) + '...' : 'none',
                checkin: checkinDate,
                checkout: checkoutDate,
                adults: adults,
                children: children
            });

            // Check if we have a valid nonce
            if (!nonce) {
                console.error('BookInn: No nonce available for AJAX request');
                roomSelect.find('option:not(:first)').remove();
                roomSelect.append('<option value="">Security error - please refresh page</option>');
                return;
            }

            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_get_available_rooms',
                    nonce: nonce,
                    check_in_date: checkinDate,  // Use consistent parameter names
                    check_out_date: checkoutDate,
                    adults: adults,
                    children: children
                },
                success: function(response) {
                    console.log('BookInn: Available rooms AJAX response received:', response);
                    console.log('BookInn: Room select element at success:', {
                        elementExists: roomSelect.length > 0,
                        elementId: roomSelect.attr('id'),
                        currentOptions: roomSelect.find('option').length
                    });

                    // Clear loading option
                    roomSelect.find('option:not(:first)').remove();
                    console.log('BookInn: Cleared existing options, remaining options:', roomSelect.find('option').length);

                    if (response.success && response.data && response.data.length > 0) {
                        console.log('BookInn: Processing', response.data.length, 'available rooms');

                        // Add available rooms
                        response.data.forEach(function(room, index) {
                            const price = room.base_price || room.price || 0;
                            const roomNumber = room.room_number || room.number || 'N/A';
                            const roomTypeName = room.room_type_name || room.type || 'Standard';
                            const roomStatus = room.status || 'available';

                            const option = $('<option></option>')
                                .attr('value', room.id)
                                .attr('data-price', price)
                                .attr('data-status', roomStatus)
                                .text(`${roomNumber} - ${roomTypeName} (€${price}/night)`);

                            console.log(`BookInn: Adding room option ${index + 1}:`, {
                                id: room.id,
                                roomNumber: roomNumber,
                                roomType: roomTypeName,
                                price: price,
                                status: roomStatus,
                                optionText: option.text()
                            });

                            roomSelect.append(option);

                            // Verify the option was added
                            const addedOption = roomSelect.find(`option[value="${room.id}"]`);
                            console.log(`BookInn: Option ${room.id} added successfully:`, addedOption.length > 0);
                        });

                        const finalOptionCount = roomSelect.find('option').length;
                        console.log('BookInn: Room loading complete:', {
                            roomsProcessed: response.data.length,
                            finalOptionCount: finalOptionCount,
                            selectElement: roomSelect[0].id,
                            selectHTML: roomSelect[0].outerHTML.substring(0, 200) + '...'
                        });

                        // Trigger change event to update total if a room gets selected
                        roomSelect.trigger('change');
                    } else {
                        roomSelect.append('<option value="">No rooms available for selected dates</option>');
                        console.log('BookInn: No rooms available for the selected criteria');
                        console.log('BookInn: Response details:', {
                            success: response.success,
                            hasData: !!response.data,
                            dataLength: response.data ? response.data.length : 0,
                            responseData: response.data
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading available rooms:', error);
                    console.error('XHR details:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    });
                    roomSelect.find('option:not(:first)').remove();
                    roomSelect.append('<option value="">Error loading rooms</option>');
                }
            });
        },

        /**
         * Calculate nights between dates
         */
        calculateNights: function(checkin, checkout) {
            const date1 = new Date(checkin);
            const date2 = new Date(checkout);
            const timeDiff = date2.getTime() - date1.getTime();
            return Math.ceil(timeDiff / (1000 * 3600 * 24));
        },

        /**
         * Save new booking
         */
        saveNewBooking: function() {
            console.log('BookInn: saveNewBooking function called');
            const $form = $('#bookinn-booking-form');

            if (!$form.length) {
                console.error('BookInn: Form #bookinn-booking-form not found');
                this.showError('Form not found');
                return;
            }

            console.log('BookInn: Form found, proceeding with validation');
            if (!this.validateBookingForm($form)) {
                console.log('BookInn: Form validation failed');
                return;
            }

            console.log('BookInn: Form validation passed, collecting data');

            // Use modal-specific selectors to avoid DOM ID conflicts
            const modal = $('#bookinn-booking-modal');
            
            // Combine first and last name for guest_name field
            const firstName = modal.find('#new-guest-first-name').val();
            const lastName = modal.find('#new-guest-last-name').val();
            const guestName = (firstName + ' ' + lastName).trim();

            // Use correct AJAX configuration
            const ajaxUrl = this.config?.ajaxUrl || window.bookinnAjax?.url || window.bookinn_dashboard?.ajax_url || '/wp-admin/admin-ajax.php';
            const nonce = this.config?.nonce || window.bookinnAjax?.nonce || window.bookinn_dashboard?.nonce;

            const formData = {
                action: 'bookinn_save_booking',
                nonce: nonce,
                check_in_date: modal.find('#new-booking-checkin').val(),
                check_out_date: modal.find('#new-booking-checkout').val(),
                adults: modal.find('#new-booking-adults').val(),
                children: modal.find('#new-booking-children').val(),
                guest_name: guestName, // Combined name as expected by server
                guest_email: modal.find('#new-guest-email').val(),
                guest_phone: modal.find('#new-guest-phone').val(),
                room_id: modal.find('#new-booking-room-select-v2').val(),
                special_requests: modal.find('#new-booking-notes').val(), // Map notes to special_requests
                total_amount: modal.find('#new-booking-total').val(),
                status: modal.find('#new-booking-status').val() || 'pending', // Default to pending
                booking_source: modal.find('#new-booking-booking-source').val() || 'direct', // Add booking source
                payment_method: modal.find('#new-booking-payment-method').val() || 'cash' // Add payment method
            };

            // Debug: Verify correct values are being collected
            console.log('BookInn: Values collected from modal:', {
                checkin: modal.find('#new-booking-checkin').val(),
                checkout: modal.find('#new-booking-checkout').val(),
                adults: modal.find('#new-booking-adults').val(),
                children: modal.find('#new-booking-children').val(),
                roomId: modal.find('#new-booking-room-select-v2').val(),
                total: modal.find('#new-booking-total').val(),
                status: modal.find('#new-booking-status').val(),
                bookingSource: modal.find('#new-booking-booking-source').val(),
                paymentMethod: modal.find('#new-booking-payment-method').val(),
                firstName: firstName,
                lastName: lastName,
                email: modal.find('#new-guest-email').val()
            });

            console.log('Saving booking with data:', formData);

            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: formData,
                success: (response) => {
                    console.log('Save booking response:', response);
                    if (response.success) {
                        this.showSuccess('Booking created successfully');
                        // Close modal using the correct method
                        if (window.BookInn.Core && window.BookInn.Core.ModalSystem) {
                            window.BookInn.Core.ModalSystem.hide('#bookinn-booking-modal');
                        } else {
                            $('#bookinn-booking-modal').removeClass('is-active');
                            $('body').removeClass('bookinn-modal-open');
                        }
                        this.loadBookingsData(); // Refresh bookings list
                    } else {
                        console.error('Error creating booking:', response.data);
                        this.showError(response.data || 'Error creating booking');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('Error saving booking:', error);
                    console.error('XHR details:', {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        responseText: xhr.responseText
                    });
                    this.showError('Error saving booking. Please try again.');
                }
            });
        },

       

        /**
         * Validate booking form
         */
        validateBookingForm: function($form) {
            let isValid = true;
            // Rimuovi errori precedenti
            $form.find('.bookinn-input, .bookinn-select').removeClass('bookinn-error');
            $form.find('.bookinn-error-message').remove();

            // Lista dei campi richiesti (aggiornata per coerenza)
            const requiredFields = [
                { selector: '[name="check_in_date"]', label: 'Check-in Date' },
                { selector: '[name="check_out_date"]', label: 'Check-out Date' },
                { selector: '[name="adults"]', label: 'Adults' },
                { selector: '[name="guest_first_name"]', label: 'Guest First Name' },
                { selector: '[name="guest_last_name"]', label: 'Guest Last Name' },
                { selector: '[name="guest_email"]', label: 'Email' },
                { selector: '[name="room_id"]', label: 'Room' },
                { selector: '[name="total_amount"]', label: 'Total Amount' }
            ];

            requiredFields.forEach(function(field) {
                const $field = $form.find(field.selector);
                const value = $field.val() ? $field.val().trim() : '';
                if (!value) {
                    $field.addClass('bookinn-error');
                    // Mostra messaggio di errore accanto al campo
                    if ($field.next('.bookinn-error-message').length === 0) {
                        $field.after('<span class="bookinn-error-message" style="color:#b91c1c;font-size:12px;margin-left:8px;">' + field.label + ' obbligatorio</span>');
                    }
                    isValid = false;
                }
            });

            // Validazione date
            const checkinField = $form.find('[name="check_in_date"]');
            const checkoutField = $form.find('[name="check_out_date"]');
            const checkinDate = new Date(checkinField.val());
            const checkoutDate = new Date(checkoutField.val());
            if (checkoutDate <= checkinDate) {
                checkoutField.addClass('bookinn-error');
                if (checkoutField.next('.bookinn-error-message').length === 0) {
                    checkoutField.after('<span class="bookinn-error-message" style="color:#b91c1c;font-size:12px;margin-left:8px;">La data di check-out deve essere successiva al check-in</span>');
                }
                isValid = false;
            }

            // Validazione email
            const emailField = $form.find('[name="guest_email"]');
            const email = emailField.val();
            if (email && !this.isValidEmail(email)) {
                emailField.addClass('bookinn-error');
                if (emailField.next('.bookinn-error-message').length === 0) {
                    emailField.after('<span class="bookinn-error-message" style="color:#b91c1c;font-size:12px;margin-left:8px;">Email non valida</span>');
                }
                isValid = false;
            }

            if (!isValid) {
                this.showError('Per favore compila tutti i campi obbligatori evidenziati.');
            }
            return isValid;
        },

        /**
         * Validate email format
         */
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        
    };


    
    // Standard edit booking function
    function editBooking(bookingId) {
        console.log('BookInn: Starting standard editBooking for ID:', bookingId);
        // This function was incomplete and causing syntax errors - commenting out for now
        console.log('BookInn: editBooking function needs to be implemented');
    }
    
    // Standard edit booking function
    function editBooking(bookingId) {
        console.log('BookInn: Starting standard editBooking for ID:', bookingId);
        
        $.ajax({
            url: bookinn_dashboard.ajax_url,
            type: 'POST',
            data: {
                action: 'bookinn_get_booking',
                booking_id: bookingId,
                nonce: bookinn_dashboard.nonce
            },
            success: function(response) {
                console.log('BookInn: AJAX success response:', response);
                
                if (response.success && response.data) {
                    console.log('BookInn: Calling populate function with data:', response.data);

                    // Try to use the unified management system first
                    if (window.BookInn && window.BookInn.Management && typeof window.BookInn.Management.showBookingEditModal === 'function') {
                        console.log('BookInn: Using unified management system');
                        window.BookInn.Management.showBookingEditModal(response.data);
                    } else if (window.BookInn && window.BookInn.ManagementActions && typeof window.BookInn.ManagementActions.showEditBookingModal === 'function') {
                        console.log('BookInn: Using management-actions.js modal system');
                        window.BookInn.ManagementActions.showEditBookingModal(response.data);
                    } else {
                        // DISABLED FALLBACK - Conflicts with unified system
                        console.warn('BookInn: No unified management system found - fallback disabled to prevent conflicts');
                        console.log('BookInn: Available systems:', {
                            'window.BookInn': !!window.BookInn,
                            'window.BookInn.Management': !!(window.BookInn && window.BookInn.Management),
                            'showBookingEditModal': !!(window.BookInn && window.BookInn.Management && window.BookInn.Management.showBookingEditModal)
                        });
                        // populateBookingForm(response.data); // REMOVED - Causes conflicts
                        // $('#editBookingModal, #edit-booking-modal, #bookinn-booking-edit-modal').show(); // REMOVED
                    }
                } else {
                    console.error('BookInn: Error getting booking data:', response.data);
                    alert('Error loading booking data: ' + (response.data || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('BookInn: AJAX error:', {xhr, status, error});
                alert('Error loading booking data');
            }
        });
    }

    // REMOVED: Legacy populateBookingForm function
    // This function was causing conflicts with the unified system
    // All functionality moved to BookInn.Management.populateBookingEditModal

    // Auto-initialize when DOM is ready
    $(document).ready(function() {
        console.log('BookInn Management Unified: DOM ready');

        // Use MutationObserver to detect when management widget is added
        let initialized = false;
        let observer = null; // Declare observer variable first

        function tryInit() {
            if (!initialized && $('.bookinn-management-widget, .bookinn-dashboard-container').length > 0) {
                BookInn.ManagementUnified.init();
                initialized = true;
                if (observer) {
                    observer.disconnect(); // Safe to disconnect now
                }
                console.log('BookInn Management Unified: Initialized via observer');
                
                // Debug: Check if wizards are present
                console.log('BookInn Wizards Debug:', {
                    newBookingWizard: $('#bookinn-new-booking-wizard').length,
                    editBookingWizard: $('#bookinn-edit-booking-wizard').length,
                    wizardGlobal: false
                });
            }
        }

        // Initial check
        tryInit();

        // Only create observer if not already initialized
        if (!initialized) {
            observer = new MutationObserver(tryInit);
            observer.observe(document.body, { childList: true, subtree: true });
        }
    });

    // ===== MIGRATED MANAGEMENT ACTIONS FUNCTIONALITY =====
    // Migrated from management-actions.js for unified system

    /**
     * Enhanced Management Actions - Migrated and Integrated
     */
    window.BookInn.ManagementActions = {

        /**
         * Initialize management actions
                self.updateReportsPeriod();
            });
                    this.closeModal();
                    alert('Error loading booking data. Please try again.');
                });
        },

        /**
         * Show edit booking modal
         */
        showEditBookingModal: function(bookingData = null, showLoading = false) {
            console.log('Opening edit booking modal...', bookingData ? 'with data' : 'loading mode');

            const modalHtml = `
                <div class="bookinn-modal is-active" id="edit-booking-modal">
                    <div class="bookinn-modal-backdrop"></div>
                    <div class="bookinn-modal-content bookinn-modal-lg">
                        <div class="bookinn-modal-header">
                            <h3 class="bookinn-modal-title">${showLoading ? 'Loading Booking...' : 'Edit Booking'}</h3>
                            <button type="button" class="bookinn-modal-close">&times;</button>
                        </div>
                        <div class="bookinn-modal-body" style="max-height: 500px; overflow-y: auto; padding: 20px;">
                            ${showLoading ? '<div class="bookinn-loading" style="text-align: center; padding: 40px;"><div class="bookinn-spinner" style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #274690; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 20px;"></div><p>Loading booking data...</p></div><style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>' : this.getEditBookingFormHtml()}
                        </div>
                        <div class="bookinn-modal-footer">
                            <button type="button" class="bookinn-btn bookinn-btn-secondary bookinn-close-modal">Cancel</button>
                            <button type="button" class="bookinn-btn bookinn-btn-primary" data-action="update-booking" ${showLoading ? 'disabled' : ''}>Update Booking</button>
                        </div>
                    </div>
                </div>
            `;

            // Ensure modal container exists
            if ($('#bookinn-modal-container').length === 0) {
                $('body').append('<div id="bookinn-modal-container"></div>');
            }

            $('#bookinn-modal-container').html(modalHtml);
            this.bindModalEvents();

            if (bookingData && !showLoading) {
                this.populateEditBookingForm(bookingData);
            }
        },

        /**
         * Load booking data from server
         */
        loadBookingData: function(bookingId) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: (window.bookinnAjax?.url || window.bookinn_dashboard?.ajax_url || '/wp-admin/admin-ajax.php'),
                    type: 'POST',
                    data: {
                        action: 'bookinn_get_booking',
                        nonce: (window.bookinnAjax?.nonce || window.bookinn_dashboard?.nonce),
                        booking_id: bookingId
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to load booking data');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error || 'Network error');
                    }
                });
            });
        },

        /**
         * Populate edit booking form with data
         */
        populateEditBookingForm: function(bookingData) {
            console.log('Populating edit form with:', bookingData);

            // Update modal title
            $('#edit-booking-modal .bookinn-modal-title').text(`Edit Booking #${bookingData.booking_reference || bookingData.id}`);

            // Replace loading content with form
            $('#edit-booking-modal .bookinn-modal-body').html(this.getEditBookingFormHtml());

            // Debug: Check if the form contains our fields
            console.log('BookInn Edit Form: payment-method field exists:', $('#edit-payment-method').length > 0);
            console.log('BookInn Edit Form: booking-source field exists:', $('#edit-booking-source').length > 0);
            console.log('BookInn Edit Form: Form HTML length:', $('#edit-booking-modal .bookinn-modal-body').html().length);

            // Populate form fields
            $('#edit-booking-id').val(bookingData.id);
            $('#edit-guest-first-name').val(bookingData.guest_first_name || '');
            $('#edit-guest-last-name').val(bookingData.guest_last_name || '');
            $('#edit-guest-email').val(bookingData.guest_email || '');
            $('#edit-guest-phone').val(bookingData.guest_phone || '');
            $('#edit-check-in-date').val(bookingData.check_in_date || '');
            $('#edit-check-out-date').val(bookingData.check_out_date || '');
            $('#edit-adults').val(bookingData.adults || 1);
            $('#edit-children').val(bookingData.children || 0);
            $('#edit-status').val(bookingData.status || 'pending');
            $('#edit-booking-source').val(bookingData.booking_source || 'direct');
            $('#edit-payment-method').val(bookingData.payment_method || 'cash');
            $('#edit-special-requests').val(bookingData.special_requests || '');

            // Load and select room
            this.loadRoomsForEdit(bookingData.room_id);

            // Enable update button
            $('[data-action="update-booking"]').prop('disabled', false);
        },

        /**
         * Update existing booking
         */
        updateBooking: function() {
            const $form = $('#edit-booking-form');
            const formData = $form.serialize();

            // Validate required fields
            let isValid = true;
            $form.find('[required]').each(function() {
                if (!$(this).val().trim()) {
                    $(this).addClass('bookinn-error');
                    isValid = false;
                } else {
                    $(this).removeClass('bookinn-error');
                }
            });

            if (!isValid) {
                alert('Please fill in all required fields');
                return;
            }

            // Validate dates
            const checkIn = new Date($('[name="check_in_date"]').val());
            const checkOut = new Date($('[name="check_out_date"]').val());

            if (checkIn >= checkOut) {
                alert('Check-out date must be after check-in date');
                return;
            }

            const $updateBtn = $('[data-action="update-booking"]');
            $updateBtn.prop('disabled', true).text('Updating...');

            $.ajax({
                url: (window.bookinnAjax?.url || window.bookinn_dashboard?.ajax_url || '/wp-admin/admin-ajax.php'),
                type: 'POST',
                data: formData + '&action=bookinn_update_booking&nonce=' + (window.bookinnAjax?.nonce || window.bookinn_dashboard?.nonce),
                success: function(response) {
                    if (response.success) {
                        alert('Booking updated successfully!');
                        window.BookInn.ManagementActions.closeModal();
                        window.BookInn.ManagementActions.refreshCurrentTab();
                    } else {
                        alert('Error: ' + (response.message || 'Failed to update booking'));
                    }
                },
                error: function() {
                    alert('Error updating booking. Please try again.');
                },
                complete: function() {
                    $updateBtn.prop('disabled', false).text('Update Booking');
                }
            });
        },

        /**
         * Close modal
         */
        closeModal: function() {
            $('.bookinn-modal').removeClass('is-active');
            setTimeout(() => {
                $('#bookinn-modal-container').empty();
            }, 300); // Allow animation to complete
        },

        /**
         * Placeholder functions for other actions
         */
        viewBooking: function(bookingId) {
            console.log('Viewing booking:', bookingId);
            alert('View booking functionality will be implemented');
        },

        deleteBooking: function(bookingId) {
            console.log('Deleting booking:', bookingId);
            alert('Delete booking functionality will be implemented');
        },

        showNewRoomModal: function() {
            console.log('Opening new room modal...');
            alert('New room functionality will be implemented');
        },

        viewRoom: function(roomId) {
            console.log('Viewing room:', roomId);
            alert('View room functionality will be implemented');
        },

        // Duplicate functions removed - using implementations above

        refreshCurrentTab: function() {
            console.log('Refreshing current tab...');
            location.reload();
        },

        filterBookings: function() {
            console.log('Filtering bookings...');
            alert('Filter bookings functionality will be implemented');
        },

        updateReportsPeriod: function() {
            console.log('Updating reports period...');
            alert('Update reports period functionality will be implemented');
        },

        exportReport: function() {
            console.log('Exporting report...');
            alert('Export report functionality will be implemented');
        },

        saveBooking: function() {
            console.log('BookInn Management Unified: Saving booking...');

            const formData = this.collectBookingFormData();
            if (!this.validateBookingData(formData)) {
                return;
            }

            this.showSaveLoading('#save-booking');

            // Save via AJAX
            this.saveBookingData(formData).then(response => {
                this.hideSaveLoading('#save-booking');
                this.showSuccessMessage('Booking saved successfully!');
                this.closeActiveModal();
                this.refreshBookingsData();
            }).catch(error => {
                this.hideSaveLoading('#save-booking');
                console.error('Error saving booking:', error);
                alert('Error saving booking. Please try again.');
            });
        },

        /**
         * Collect booking form data
         */
        collectBookingFormData: function() {
            const firstName = $('#booking-guest-first-name').val() || $('#guest-first-name').val();
            const lastName = $('#booking-guest-last-name').val() || $('#guest-last-name').val();
            const guestName = (firstName + ' ' + lastName).trim();

            return {
                booking_id: $('#booking-id').val(),
                guest_name: guestName,
                guest_email: $('#booking-guest-email').val() || $('#guest-email').val(),
                guest_phone: $('#booking-guest-phone').val() || $('#guest-phone').val(),
                room_id: $('#booking-room-id').val() || $('#booking-room').val(),
                check_in_date: $('#booking-checkin').val() || $('#booking-check-in').val(),
                check_out_date: $('#booking-checkout').val() || $('#booking-check-out').val(),
                adults: $('#booking-adults').val() || 1,
                children: $('#booking-children').val() || 0,
                total_amount: $('#booking-total').val() || $('#booking-total-amount').val(),
                status: $('#booking-status').val(),
                special_requests: $('#booking-notes').val() || $('#booking-special-requests').val()
            };
        },

        /**
         * Validate booking data
         */
        validateBookingData: function(data) {
            if (!data.guest_name || data.guest_name.trim() === '') {
                alert('Guest name is required');
                return false;
            }

            if (!data.guest_email || !this.isValidEmail(data.guest_email)) {
                alert('Valid email address is required');
                return false;
            }

            if (!data.check_in_date) {
                alert('Check-in date is required');
                return false;
            }

            if (!data.check_out_date) {
                alert('Check-out date is required');
                return false;
            }

            if (!data.room_id) {
                alert('Room selection is required');
                return false;
            }

            return true;
        },

        /**
         * Save booking data via AJAX
         */
        saveBookingData: function(formData) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: this.config.ajaxUrl,
                    type: 'POST',
                    data: {
                        action: 'bookinn_save_booking',
                        nonce: this.config.nonce,
                        ...formData
                    },
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(response.data || 'Failed to save booking');
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(error);
                    }
                });
            });
        },

        /**
         * Helper function to validate email
         */
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

    // ...existing code...

        saveRoom: function() {
            console.log('Saving room...');
            alert('Save room functionality will be implemented');
        },

        saveRoomStatus: function() {
            console.log('Saving room status...');
            alert('Save room status functionality will be implemented');
        },

        loadAvailableRooms: function() {
            console.log('Loading available rooms...');
            // Implementation will be added
        },

        loadRoomsForEdit: function(selectedRoomId) {
            console.log('Loading rooms for edit...');
            // Implementation will be added
        },

        getNewBookingFormHtml: function() {
            return '<p>New booking form HTML will be implemented</p>';
        },

        /**
         * Check room availability for the selected period
         */
        checkRoomAvailability: function() {
            const checkinDate = $('#rooms-checkin-filter').val();
            const checkoutDate = $('#rooms-checkout-filter').val();
            const guests = parseInt($('#rooms-guests-filter').val()) || 1;

            if (!checkinDate || !checkoutDate) {
                alert('Please select both check-in and check-out dates');
                return;
            }

            if (checkinDate >= checkoutDate) {
                alert('Check-out date must be after check-in date');
                return;
            }

            const self = this;
            const $button = $('#check-room-availability');
            
            // Show loading state with spinner
            const originalText = $button.html();
            $button.html('<span class="bookinn-spinner"></span>Checking...').prop('disabled', true);
            
            // Show loading state on all availability status cells
            $('.bookinn-availability-status').html('<span class="bookinn-status-badge bookinn-status-pending"><span class="bookinn-spinner"></span>Checking...</span>');

            $.ajax({
                url: this.config.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'bookinn_get_available_rooms',
                    nonce: this.config.nonce,
                    checkin_date: checkinDate,
                    checkout_date: checkoutDate,
                    adults: guests,
                    children: 0
                },
                success: function(response) {
                    // Restore button state
                    $button.html(originalText).prop('disabled', false);
                    
                    if (response.success) {
                        const availableRoomIds = response.data.map(room => room.id);
                        
                        // Update availability status for each room
                        $('.bookinn-availability-status').each(function() {
                            const roomId = $(this).data('room-id');
                            const isAvailable = availableRoomIds.includes(parseInt(roomId));
                            
                            if (isAvailable) {
                                $(this).html('<span class="bookinn-status-badge bookinn-status-available">Available</span>');
                            } else {
                                $(this).html('<span class="bookinn-status-badge bookinn-status-occupied">Not Available</span>');
                            }
                        });

                        // Store the filter data for quick booking
                        self.roomFilterData = {
                            checkin: checkinDate,
                            checkout: checkoutDate,
                            guests: guests
                        };

                        console.log('Room availability checked for', checkinDate, 'to', checkoutDate);
                        
                        // Show success message briefly
                        const availableCount = availableRoomIds.length;
                        const totalRooms = $('.bookinn-availability-status').length;
                        const statusMessage = `Found ${availableCount} of ${totalRooms} rooms available`;
                        
                        // Show temporary success notification
                        self.showTemporaryNotification(statusMessage, 'success');
                        
                    } else {
                        $('.bookinn-availability-status').html('<span class="bookinn-status-badge bookinn-status-error">Error</span>');
                        self.showTemporaryNotification('Error checking room availability: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: function() {
                    // Restore button state
                    $button.html(originalText).prop('disabled', false);
                    $('.bookinn-availability-status').html('<span class="bookinn-status-badge bookinn-status-error">Error</span>');
                    self.showTemporaryNotification('Error checking room availability. Please try again.', 'error');
                }
            });
        },

        /**
         * Show temporary notification
         */
        showTemporaryNotification: function(message, type) {
            // Remove existing notifications
            $('.bookinn-temp-notification').remove();
            
            const notificationClass = type === 'success' ? 'bookinn-notification-success' : 'bookinn-notification-error';
            const notification = $(`
                <div class="bookinn-temp-notification ${notificationClass}" style="
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 16px;
                    border-radius: 6px;
                    color: white;
                    font-weight: 500;
                    z-index: 10000;
                    max-width: 300px;
                    font-size: 14px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    ${type === 'success' ? 'background-color: #10b981;' : 'background-color: #ef4444;'}
                ">
                    ${message}
                </div>
            `);
            
            $('body').append(notification);
            
            // Fade out after 3 seconds
            setTimeout(() => {
                notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        },

        /**
         * Clear room filters and reload all rooms
         */
        clearRoomFilters: function() {
            console.log('BookInn: Clearing room filters...');

            // Reset filter inputs to default values
            const $statusFilter = $('#rooms-status-filter');
            const $numberFilter = $('#rooms-number-filter');
            const $typeFilter = $('#rooms-type-filter');

            console.log('BookInn: Filter elements found:', {
                status: $statusFilter.length,
                number: $numberFilter.length,
                type: $typeFilter.length
            });

            // Reset each filter
            if ($statusFilter.length) {
                $statusFilter.val('').trigger('change');
                console.log('BookInn: Status filter cleared');
            }
            
            if ($numberFilter.length) {
                $numberFilter.val('').trigger('input');
                console.log('BookInn: Number filter cleared');
            }
            
            if ($typeFilter.length) {
                $typeFilter.val('').trigger('change');
                console.log('BookInn: Type filter cleared');
            }

            // Clear stored filter data
            this.roomFilterData = null;

            // Reload all rooms without filters
            console.log('BookInn: Reloading all rooms...');
            this.loadRoomsData({})
                .then((rooms) => {
                    console.log('BookInn: Filters cleared successfully, showing', rooms.length, 'rooms');
                    this.showNotification('Filters cleared - showing all ' + rooms.length + ' rooms', 'success');
                })
                .catch((error) => {
                    console.error('BookInn: Failed to reload rooms after clearing filters:', error);
                    this.showNotification('Error reloading rooms: ' + error, 'error');
                });
        },

        /**
         * Show booking modal with pre-selected room and dates
         */
        showQuickBookWithRoom: function(roomId, roomNumber, roomType) {
            console.log('Quick booking room:', roomId, roomNumber, roomType);

            // Show the booking modal
            this.showBookingModal();

            // Wait for modal to be created and visible
            setTimeout(() => {
                // Pre-populate dates if available from filter
                if (this.roomFilterData) {
                    $('#new-booking-checkin').val(this.roomFilterData.checkin);
                    $('#new-booking-checkout').val(this.roomFilterData.checkout);
                    
                    if (this.roomFilterData.guests <= 4) {
                        $('#new-booking-adults').val(this.roomFilterData.guests);
                    }
                }

                // Load available rooms first
                this.loadAvailableRoomsForNewBooking();

                // Pre-select the room after rooms are loaded
                setTimeout(() => {
                    const roomSelect = $('#new-booking-room-select-v2');
                    if (roomSelect.find(`option[value="${roomId}"]`).length > 0) {
                        roomSelect.val(roomId).trigger('change');
                        console.log('Room pre-selected:', roomId);
                    } else {
                        console.log('Room not available for selected dates');
                        alert(`Room ${roomNumber} is not available for the selected dates. Please choose different dates or another room.`);
                    }
                }, 1000);

            }, 200);
        }
    };

    // CRITICAL FIX: Instead of complex object assignment, define functions directly
    // This bypasses all object assignment issues
    
    // Ensure the target object exists
    window.BookInn.ManagementUnified = window.BookInn.ManagementUnified || {};
    
    // DIRECT FUNCTION DEFINITION - Bypass object assignment issues
    window.BookInn.ManagementUnified.checkRoomAvailability = function() {
        const checkinDate = $('#rooms-checkin-filter').val();
        const checkoutDate = $('#rooms-checkout-filter').val();
        const guests = parseInt($('#rooms-guests-filter').val()) || 1;

        if (!checkinDate || !checkoutDate) {
            alert('Please select both check-in and check-out dates');
            return;
        }

        if (checkinDate >= checkoutDate) {
            alert('Check-out date must be after check-in date');
            return;
        }

        const self = window.BookInn.ManagementUnified;
        const $button = $('#check-room-availability');
        
        // Show loading state with spinner
        const originalText = $button.html();
        $button.html('<span class="bookinn-spinner"></span>Checking...').prop('disabled', true);
        
        // Show loading state on all availability status cells
        $('.bookinn-availability-status').html('<span class="bookinn-status-badge bookinn-status-pending"><span class="bookinn-spinner"></span>Checking...</span>');

        $.ajax({
            url: self.config ? self.config.ajaxUrl : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'bookinn_get_available_rooms',
                nonce: (self.config && self.config.nonce) ? self.config.nonce : '',
                checkin_date: checkinDate,
                checkout_date: checkoutDate,
                adults: guests,
                children: 0
            },
            success: function(response) {
                // Restore button state
                $button.html(originalText).prop('disabled', false);
                
                if (response.success) {
                    const availableRoomIds = response.data.map(room => room.id);
                    
                    // Update availability status for each room
                    $('.bookinn-availability-status').each(function() {
                        const roomId = $(this).data('room-id');
                        const isAvailable = availableRoomIds.includes(parseInt(roomId));
                        
                        if (isAvailable) {
                            $(this).html('<span class="bookinn-status-badge bookinn-status-available">Available</span>');
                        } else {
                            $(this).html('<span class="bookinn-status-badge bookinn-status-occupied">Not Available</span>');
                        }
                    });

                    // Store the filter data for quick booking
                    self.roomFilterData = {
                        checkin: checkinDate,
                        checkout: checkoutDate,
                        guests: guests
                    };
                    
                    // Show temporary notification
                    self.showTemporaryNotification && self.showTemporaryNotification('Room availability updated for selected dates', 'success');
                } else {
                    alert('Error checking room availability: ' + (response.data || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                // Restore button state
                $button.html(originalText).prop('disabled', false);
                $('.bookinn-availability-status').html('<span class="bookinn-status-badge bookinn-status-error">Error</span>');
                
                console.error('AJAX Error:', error);
                alert('Error checking room availability. Please try again.');
            }
        });
    };

    window.BookInn.ManagementUnified.showQuickBookWithRoom = function(roomId, roomNumber, roomType) {
        console.log('Quick booking room:', roomId, roomNumber, roomType);

        // Show the booking modal
        if (this.showBookingModal) {
            this.showBookingModal();
        } else {
            console.error('showBookingModal function not available');
            alert('Booking modal not available. Please try refreshing the page.');
            return;
        }

        // Wait for modal to be created and visible
        setTimeout(() => {
            // Pre-populate dates if available from filter
            if (this.roomFilterData) {
                $('#new-booking-checkin').val(this.roomFilterData.checkin);
                $('#new-booking-checkout').val(this.roomFilterData.checkout);
                
                if (this.roomFilterData.guests <= 4) {
                    $('#new-booking-adults').val(this.roomFilterData.guests);
                }
            }

            // Load available rooms first
            if (this.loadAvailableRoomsForNewBooking) {
                this.loadAvailableRoomsForNewBooking();
            }

            // Pre-select the room after rooms are loaded
            setTimeout(() => {
                const roomSelect = $('#new-booking-room-select-v2');
                if (roomSelect.find(`option[value="${roomId}"]`).length > 0) {
                    roomSelect.val(roomId).trigger('change');
                    console.log('Room pre-selected:', roomId);
                } else {
                    console.log('Room not available for selected dates');
                    alert(`Room ${roomNumber} is not available for the selected dates. Please choose different dates or another room.`);
                }
            }, 1000);

        }, 200);
    };

    window.BookInn.ManagementUnified.clearRoomFilters = function() {
        console.log('BookInn: Global clearRoomFilters called');
        
        // Clear filter form values for new filter structure
        $('#rooms-status-filter').val('').trigger('change');
        $('#rooms-number-filter').val('').trigger('input');
        $('#rooms-type-filter').val('').trigger('change');
        
        // Clear stored filter data
        if (window.BookInn.ManagementUnified.roomFilterData) {
            delete window.BookInn.ManagementUnified.roomFilterData;
        }
        
        // Reload rooms data
        if (window.BookInn.ManagementUnified.loadRoomsData) {
            window.BookInn.ManagementUnified.loadRoomsData({})
                .then(() => {
                    console.log('BookInn: Global filters cleared and rooms reloaded');
                    if (window.BookInn.Core && window.BookInn.Core.showNotification) {
                        window.BookInn.Core.showNotification('Filters cleared - showing all rooms', 'success');
                    }
                })
                .catch((error) => {
                    console.error('BookInn: Global clear filters - reload failed:', error);
                });
        }
        
        console.log('Room filters cleared (global)');
    };

    window.BookInn.ManagementUnified.performRoomSearch = function() {
        console.log('BookInn: Global performRoomSearch called');
        
        // Get filter values
        const statusFilter = $('#rooms-status-filter').val();
        const numberFilter = $('#rooms-number-filter').val().trim();
        const typeFilter = $('#rooms-type-filter').val();

        // Build filters object
        const filters = {};
        if (statusFilter && statusFilter !== '') filters.status = statusFilter;
        if (numberFilter && numberFilter !== '') filters.room_number = numberFilter;
        if (typeFilter && typeFilter !== '') filters.room_type_id = typeFilter;

        console.log('BookInn: Global room search filters:', filters);

        // Show loading state on button
        const $button = $('#apply-room-filters');
        if ($button.length) {
            const originalText = $button.html();
            $button.html('<i class="fa-solid fa-spinner fa-spin bookinn-fa"></i> Applying...').prop('disabled', true);

            // Use global function to load rooms
            if (window.BookInn.ManagementUnified.loadRoomsData) {
                window.BookInn.ManagementUnified.loadRoomsData(filters)
                    .then((rooms) => {
                        console.log('BookInn: Global room search completed, found', rooms.length, 'rooms');
                        if (window.BookInn.Core && window.BookInn.Core.showNotification) {
                            window.BookInn.Core.showNotification('Search completed. Found ' + rooms.length + ' rooms.', 'success');
                        }
                    })
                    .catch((error) => {
                        console.error('BookInn: Global room search failed:', error);
                        if (window.BookInn.Core && window.BookInn.Core.showNotification) {
                            window.BookInn.Core.showNotification('Room search failed: ' + error, 'error');
                        }
                    })
                    .finally(() => {
                        $button.html(originalText).prop('disabled', false);
                    });
            }
        }
    };
    
    // Try to assign the full object but don't rely on it
    try {
        // Preserve existing properties and add new ones
        const existingProps = {...window.BookInn.ManagementUnified};
        window.BookInn.ManagementUnified = Object.assign(existingProps, ManagementUnifiedObject);
        console.log('BookInn: Full object assignment successful');
    } catch (error) {
        console.error('BookInn: Full object assignment failed:', error);
        // The critical functions are already assigned above, so this is non-fatal
    }
    
    // FINAL VERIFICATION: Check assignment worked
    console.log('BookInn: FINAL VERIFICATION - Direct function assignment', {
        'checkRoomAvailability': typeof window.BookInn.ManagementUnified.checkRoomAvailability,
        'showQuickBookWithRoom': typeof window.BookInn.ManagementUnified.showQuickBookWithRoom,
        'clearRoomFilters': typeof window.BookInn.ManagementUnified.clearRoomFilters,
        'Object keys count': Object.keys(window.BookInn.ManagementUnified).length
    });

    // DEBUG: Verify functions are properly assigned
    console.log('BookInn Management Unified: Object assignment complete', {
        'Object type': typeof window.BookInn.ManagementUnified,
        'checkRoomAvailability': typeof window.BookInn.ManagementUnified.checkRoomAvailability,
        'showQuickBookWithRoom': typeof window.BookInn.ManagementUnified.showQuickBookWithRoom,
        'clearRoomFilters': typeof window.BookInn.ManagementUnified.clearRoomFilters,
        'Total functions': Object.keys(window.BookInn.ManagementUnified).filter(key => typeof window.BookInn.ManagementUnified[key] === 'function').length
    });

    // ENSURE FUNCTIONS ARE GLOBALLY ACCESSIBLE - DEBUGGING AID
    if (window.BookInn && window.BookInn.ManagementUnified) {
        console.log('BookInn Management Unified: Final function validation:', {
            'Global checkRoomAvailability': typeof window.BookInn.ManagementUnified.checkRoomAvailability,
            'Global showQuickBookWithRoom': typeof window.BookInn.ManagementUnified.showQuickBookWithRoom,
            'Global clearRoomFilters': typeof window.BookInn.ManagementUnified.clearRoomFilters,
        });
    }

    // DEBUG: Verify object structure and continue with existing code
    if (window.BookInn && window.BookInn.ManagementUnified) {
        console.log('BookInn Management Unified: Final function validation:', {
            'Global checkRoomAvailability': typeof window.BookInn.ManagementUnified.checkRoomAvailability,
            'Global showQuickBookWithRoom': typeof window.BookInn.ManagementUnified.showQuickBookWithRoom,
            'Global clearRoomFilters': typeof window.BookInn.ManagementUnified.clearRoomFilters,
            'Object keys': Object.keys(window.BookInn.ManagementUnified).filter(key => typeof window.BookInn.ManagementUnified[key] === 'function').length + ' functions found'
        });
        
        // Fallback: Direct assignment to ensure availability (debug only)
        if (typeof window.BookInn.ManagementUnified.checkRoomAvailability !== 'function') {
            console.error('BookInn: checkRoomAvailability not found - attempting direct assignment');
            window.BookInn.ManagementUnified.checkRoomAvailability = ManagementUnifiedObject.checkRoomAvailability;
        }
        if (typeof window.BookInn.ManagementUnified.showQuickBookWithRoom !== 'function') {
            console.error('BookInn: showQuickBookWithRoom not found - attempting direct assignment');
            window.BookInn.ManagementUnified.showQuickBookWithRoom = ManagementUnifiedObject.showQuickBookWithRoom;
        }
        if (typeof window.BookInn.ManagementUnified.clearRoomFilters !== 'function') {
            console.error('BookInn: clearRoomFilters not found - attempting direct assignment');
            window.BookInn.ManagementUnified.clearRoomFilters = ManagementUnifiedObject.clearRoomFilters;
        }
    }

    // PREVENT OBJECT OVERRIDE: Simplified protection
    console.log('BookInn Management Unified: Assignment complete - functions should now be available');

    // Test functions for debugging (can be called from browser console)
    window.BookInnDebug = {
        testRoomDropdown: function() {
            console.log('=== BookInn Room Dropdown Test ===');
            const roomSelect = $('#new-booking-room-select-v2');
            console.log('Room dropdown element found:', roomSelect.length > 0);
            console.log('Room dropdown ID:', roomSelect.attr('id'));
            console.log('Current options count:', roomSelect.find('option').length);
            console.log('Room dropdown HTML:', roomSelect[0] ? roomSelect[0].outerHTML : 'Element not found');

            // Test loading rooms
            if (window.BookInn && window.BookInn.ManagementUnified && window.BookInn.ManagementUnified.loadAvailableRoomsForNewBooking) {
                console.log('Triggering room loading...');
                window.BookInn.ManagementUnified.loadAvailableRoomsForNewBooking();
            } else {
                console.log('Room loading function not available');
            }
        },

        testAvailabilityButton: function() {
            console.log('=== BookInn Availability Button Test ===');
            const button = $('#check-room-availability');
            console.log('Availability button found:', button.length > 0);
            console.log('Button disabled:', button.prop('disabled'));
            console.log('Button HTML:', button[0] ? button[0].outerHTML : 'Button not found');

            // Test click event
            if (button.length > 0) {
                console.log('Simulating button click...');
                button.trigger('click');
            }
        },

        testBothFixes: function() {
            console.log('=== BookInn Complete Test ===');
            this.testRoomDropdown();
            setTimeout(() => {
                this.testAvailabilityButton();
            }, 1000);
        },

        testRoomLoadingManually: function() {
            console.log('=== BookInn Manual Room Loading Test ===');

            // Set test dates
            const today = new Date();
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);

            const checkinDate = today.toISOString().split('T')[0];
            const checkoutDate = tomorrow.toISOString().split('T')[0];

            $('#new-booking-checkin').val(checkinDate);
            $('#new-booking-checkout').val(checkoutDate);
            $('#new-booking-adults').val('2');

            console.log('Test dates set:', {
                checkin: checkinDate,
                checkout: checkoutDate,
                adults: 2
            });

            // Trigger room loading
            if (window.BookInn && window.BookInn.ManagementUnified && window.BookInn.ManagementUnified.loadAvailableRoomsForNewBooking) {
                console.log('Triggering manual room loading...');
                window.BookInn.ManagementUnified.loadAvailableRoomsForNewBooking();
            } else {
                console.error('Room loading function not available');
            }
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        console.log('BookInn Management Unified: Document ready, initializing...');

        // Initialize the management system
        if (window.BookInn && window.BookInn.ManagementUnified && window.BookInn.ManagementUnified.init) {
            window.BookInn.ManagementUnified.init();
        } else {
            console.error('BookInn Management Unified: Init function not available');
        }

        // Add debug info
        console.log('BookInn Debug: Test functions available at window.BookInnDebug');
        console.log('BookInn Debug: Available test functions:');
        console.log('  - window.BookInnDebug.testBothFixes() - Test both fixes');
        console.log('  - window.BookInnDebug.testRoomDropdown() - Test room dropdown only');
        console.log('  - window.BookInnDebug.testAvailabilityButton() - Test availability button only');
        console.log('  - window.BookInnDebug.testRoomLoadingManually() - Manual room loading test');

        // Check if room dropdown exists on page load
        const roomDropdown = $('#new-booking-room-select-v2');
        console.log('BookInn Debug: Room dropdown check on page load:', {
            exists: roomDropdown.length > 0,
            id: roomDropdown.attr('id'),
            optionsCount: roomDropdown.find('option').length
        });
    });

})(jQuery, window, document);
