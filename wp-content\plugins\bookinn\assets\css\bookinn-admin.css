/* BookInn Admin Styles */
.bookinn-admin-container, .bookinn-status-container, .bookinn-import-export-container {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin: 20px 20px 20px 0;
    overflow: hidden;
}

.bookinn-admin-header {
    background: #274690;
    color: #fff;
    padding: 32px 32px 24px 32px;
    border-radius: 12px 12px 0 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    margin-bottom: 0;
}

.bookinn-admin-header h1,
.bookinn-status-header h1,
.bookinn-import-export-header h1 {
    color: white;
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
}

/* Tab Navigation - Forza stile con specificità alta */
.bookinn-admin-container .bookinn-tab-navigation {
    background: #f8f9fa !important;
    border-bottom: 1px solid #e9ecef !important;
    margin: 0 !important;
    padding: 0 32px !important;
    overflow-x: auto;
    white-space: nowrap;
    display: flex !important;
}

.bookinn-admin-container .bookinn-tab-link {
    display: inline-block !important;
    background: #f6f7f7 !important;
    border: 1px solid #e2e2e2 !important;
    border-bottom: none !important;
    padding: 16px 24px !important;
    cursor: pointer !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    color: #555d66 !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    outline: none !important;
    border-radius: 8px 8px 0 0 !important;
    margin-right: 2px !important;
    text-decoration: none !important;
    top: 2px !important;
}

.bookinn-admin-container .bookinn-tab-link.active {
    color: #2271b1 !important;
    background: white !important;
    border-bottom: 2px solid #fff !important;
    box-shadow: 0 2px 8px rgba(102,126,234,0.07) !important;
    z-index: 2 !important;
}

.bookinn-admin-container .bookinn-tab-link:focus {
    outline: 2px solid #667eea !important;
    outline-offset: 2px !important;
    z-index: 3 !important;
}

.bookinn-admin-container .bookinn-tab-link:hover {
    color: #2271b1 !important;
    background: rgba(102, 126, 234, 0.08) !important;
}
.bookinn-tab-content {
    padding: 32px;
    min-height: 400px;
}

.bookinn-tab-panel {
    display: none;
    animation: fadeInUp 0.4s ease;
}

.bookinn-tab-panel.active {
    display: block;
}

/* Text Spacing - Professional Admin Style */
.bookinn-text-content {
    padding: 8px 12px !important;
    margin: 4px 0 !important;
    display: inline-block !important;
    line-height: 1.5 !important;
}

.bookinn-section-description {
    padding: 12px 16px !important;
    margin: 8px 0 16px 0 !important;
    background: #f8f9fa !important;
    border-left: 4px solid #274690 !important;
    border-radius: 0 4px 4px 0 !important;
    color: #555d66 !important;
    font-size: 14px !important;
    line-height: 1.6 !important;
}

.bookinn-form-label {
    padding: 4px 8px !important;
    margin: 2px 0 !important;
    display: inline-block !important;
    font-weight: 500 !important;
    color: #23282d !important;
}

.bookinn-table-text {
    padding: 6px 10px !important;
    margin: 2px 0 !important;
    display: inline-block !important;
    line-height: 1.4 !important;
}

.bookinn-status-text {
    padding: 4px 8px !important;
    margin: 2px 4px !important;
    display: inline-block !important;
    border-radius: 3px !important;
    font-size: 13px !important;
    font-weight: 500 !important;
}

.bookinn-button-text {
    padding: 2px 6px !important;
    margin: 0 2px !important;
    display: inline-block !important;
    line-height: 1.3 !important;
}

.bookinn-header-text {
    padding: 8px 0 !important;
    margin: 4px 0 !important;
    display: block !important;
    line-height: 1.4 !important;
}

.bookinn-description-text {
    padding: 8px 12px !important;
    margin: 6px 0 !important;
    background: rgba(39, 70, 144, 0.05) !important;
    border-radius: 4px !important;
    color: #555d66 !important;
    font-size: 13px !important;
    line-height: 1.5 !important;
    display: block !important;
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@media (max-width: 700px) {
    .bookinn-tab-link {
        padding: 12px 16px;
        font-size: 13px;
    }
    .bookinn-tab-navigation {
        padding: 0 16px;
    }
}

.bookinn-section-title {
    font-size: 20px;
    font-weight: 600;
    color: #f8f9fa;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid #ecf0f1;
}

.bookinn-form-group {
    margin-bottom: 24px;
}

.bookinn-form-group label {
    display: block;
    font-weight: 500;
    color: #34495e;
    margin-bottom: 6px;
}

.bookinn-form-group input, .bookinn-form-group select {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.bookinn-form-group input:focus, .bookinn-form-group select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

.bookinn-info-box {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #667eea;
    border-radius: 8px;
    padding: 20px;
    margin-top: 24px;
}

.bookinn-info-box h3 {
    color: #f8f9fa;
    margin-top: 0;
}

.bookinn-info-box code {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 8px;
    color: #e83e8c;
}

.form-table th { 
    width: 200px; 
    font-weight: 500; 
    color: #495057; 
}

.form-table td { 
    padding-top: 15px; 
    padding-bottom: 15px; 
}

.submit { 
    margin-top: 24px; 
    padding-top: 24px; 
    border-top: 1px solid #e9ecef; 
}

/* Status Page */
.bookinn-status-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 24px 32px;
}

.bookinn-status-content {
    padding: 32px;
}

.bookinn-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.bookinn-status-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
}

.bookinn-status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.bookinn-status-card h3 {
    color: #f8f9fa;
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.bookinn-status-card table {
    width: 100%;
    border-collapse: collapse;
}

.bookinn-status-card td {
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
    font-size: 14px;
}

.bookinn-status-card td:first-child {
    font-weight: 500;
    color: #6c757d;
    width: 50%;
}

.bookinn-status-card td:last-child {
    text-align: right;
    color: #495057;
    font-weight: 600;
}

.status-ok { 
    color: #28a745; 
    font-weight: 600; 
}

.status-error { 
    color: #dc3545; 
    font-weight: 600; 
}

.status-warning { 
    color: #ffc107; 
    font-weight: 600; 
}

.bookinn-actions {
    text-align: center;
    padding: 24px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-top: 24px;
}

.bookinn-actions .button {
    margin: 0 8px;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
}

/* Import/Export Page */
.bookinn-import-export-header {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    color: white;
    padding: 24px 32px;
}

.bookinn-import-export-content {
    padding: 32px;
}

.bookinn-import-export-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 32px;
}

.bookinn-card {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 24px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.2s ease;
}

.bookinn-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.bookinn-card h3 {
    color: #f8f9fa;
    margin-top: 0;
    margin-bottom: 16px;
    font-size: 20px;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.bookinn-card p {
    color: #6c757d;
    margin-bottom: 20px;
    line-height: 1.6;
}

.bookinn-card .form-table th {
    background: #f8f9fa;
    padding: 12px;
    font-weight: 500;
    color: #495057;
    border-bottom: 1px solid #dee2e6;
}

.bookinn-card .form-table td {
    padding: 12px;
    border-bottom: 1px solid #f8f9fa;
}

.bookinn-card input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.bookinn-card select, .bookinn-card input[type="file"] {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    width: 100%;
    font-size: 14px;
}

.bookinn-card select:focus, .bookinn-card input[type="file"]:focus {
    border-color: #fd7e14;
    box-shadow: 0 0 0 3px rgba(253, 126, 20, 0.1);
    outline: none;
}

.bookinn-card .submit {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.bookinn-card .button {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 14px;
    min-width: 140px;
}

.button-primary {
    background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);
    border: none;
    color: white;
}

.button-primary:hover {
    background: linear-gradient(135deg, #e8690e 0%, #d21e7c 100%);
    color: white;
}
